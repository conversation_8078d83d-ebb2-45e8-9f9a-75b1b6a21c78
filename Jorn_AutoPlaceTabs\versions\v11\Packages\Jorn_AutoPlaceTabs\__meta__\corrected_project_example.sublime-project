{
    "folders":
    [
        {
            "path": ".",
            "name": "My Sublime Packages"
        }
    ],
    "settings": {
        "jorn_auto_place_tabs": {
            "auto_place_on_activation": true,
            
            // Enable debug to see what's happening
            "enable_debug_prints": true,
            
            "file_type_rules": {
                "0": [".py", ".pyw"],
                "1": [".js", ".ts", ".jsx", ".tsx"],
                "2": [".html", ".css", ".vue"],
                "7": [".md", ".txt", ".json"]
            },
            "directory_rules": {
                "0": ["*/src/*", "*/lib/*"],
                "1": ["*/tests/*", "*/test/*"],
                "2": ["*/docs/*"],
                "3": ["*/config/*"]
            },
            "project_files_group": 0,
            "external_files_group": 2,
            "auto_adjust_layout": true,
            
            // IMPORTANT: To use group index 7, you need at least 8 groups total
            // Group indices are 0-based: 0, 1, 2, 3, 4, 5, 6, 7 = 8 groups
            "max_groups": 8,
            
            // Custom layout for 8 groups (3x3 grid with 8 cells)
            "layout_configs": {
                "8": {
                    "cols": [0.0, 0.33, 0.66, 1.0],
                    "rows": [0.0, 0.33, 0.66, 1.0],
                    "cells": [
                        [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],
                        [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2],
                        [0, 2, 1, 3], [1, 2, 2, 3]
                    ]
                }
            }
        }
    }
}
