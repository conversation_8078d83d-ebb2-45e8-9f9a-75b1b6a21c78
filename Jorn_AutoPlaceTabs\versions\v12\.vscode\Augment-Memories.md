
# Quick Reference Guide

## Most Essential Preferences
- **Code Style**: Self-explanatory code with minimal comments (<10% ratio)
- **Structure**: Clean, organized project structure with src directory
- **Philosophy**: Simplicity, elegance, and fundamental connections
- **Refactoring**: Drastic code size reduction while preserving functionality
- **Testing**: Autonomous verification with proper test organization
- **Documentation**: Concise, accurate README.md reflecting current state
- **CLI**: Clean interfaces with 1-based indexing and minimal output
- **Paths**: Relative paths with forward slashes (/)
- **Templates**: Standardized three-part format with progressive sequences
- **Environment**: PowerShell on Windows for all terminal operations

## Common Code Patterns
- **Configuration**: In-class config at the top of self-contained scripts
- **Error Handling**: Graceful handling without halting execution
- **Logging**: Simplified, hierarchical logging that overwrites files
- **Output**: Timestamps with seconds in filenames (2025.05.29-kl.10.43.ss)
- **User Interaction**: Rich library's interactive prompts for CLI tools
- **Imports**: Consistent paths using aliases instead of relative paths
- **Docstrings**: Concise single-line format only where needed

## Quick Decision Guide
```
| When you need to... | Prefer this approach                             |
|---------------------|--------------------------------------------------|
| Add new feature     | Discover usage patterns first                    |
| Refactor code       | Drastic consolidation with size reduction        |
| Document code       | Minimal comments, clear structure                |
| Organize files      | Clean src directory with intuitive structure     |
| Handle errors       | Graceful handling without halting                |
| Create templates    | Three-part format with progressive sequences     |
| Test changes        | Autonomous verification with proper organization |
| Display paths       | Relative with forward slashes                    |
| Format CLI output   | Clean, minimal with 1-based indexing             |
| Execute scripts     | Make paths relative to script location           |
```

## Context Triggers
- **When starting new project**: Establish clean src structure first
- **When refactoring**: Look for patterns across entire codebase
- **When documenting**: Focus on README.md, minimize in-code comments
- **When creating templates**: Follow three-part format and stage-based IDs
- **When testing**: Organize tests to mirror codebase structure
- **When handling paths**: Make relative to script location
- **When designing CLI**: Use 1-based indexing and minimal output
- **When consolidating**: Verify functionality before removing old code

---

# Core Philosophy & Design Principles

## Fundamental Approach
- **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability; reject solutions that don't meet these criteria.
- **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.
- **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.
- **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.
- **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.

## Focus & Priorities
- **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.
- **Usage Before Features**: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.
- **Impactful Consolidation**: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.
- **Sequential Targeting**: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.

## Design Methodology
- **Inherent Structure**: Prefer file structure as the inherent source of fundamental truth; directory trees should reveal inherent relationships for decision-making.
- **Natural Organization**: Prefer file naming conventions that create natural and cohesive sequential order for better organization.
- **Synergistic Efficiency**: Prioritize synergistic fundamental efficiency and elegance through respecting inherent connections between system components.
- **Directional Clarity**: Prefer system design approaches that mirror the simplicity and directional clarity of the concepts being implemented, avoiding directionless complexity.
- **Value Extraction**: Always prefer single condensed maximally enhanced value extraction over long lists of generic data.
- **Direction Setting**: Focus on setting direction rather than planting flags when dealing with unknown complexity.

## Evaluation Criteria
- **Comprehensive Progress**: Evaluate progress using context and history analysis, emphasizing preservation of fundamental cohesive, elegant, and simplicistic conceptual implementations.
- **Inherent Direction**: Template design should maximize inherent fundamental ability to DIRECT (set trajectory toward constructive outcomes).
- **Sequential Composition**: Optimize output for sequential composition with downstream components, ensuring maximal value when elements are chained together.
- **Gradual Reduction**: Follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming complex elements into simpler forms.

---

# Development Practices & Code Style

## Code Style & Structure
- **Self-Explanatory Code**: Prefer self-explanatory code over excessive commenting/verbosity; code should be inherently readable.
- **Minimal Comments**: Keep comments and docstrings concise (less than 10% comment ratio), providing only additional value without unnecessary verbosity.
- **Concise Docstrings**: Prefer concise single-line docstrings rather than completely removing them or using verbose multi-line formats.
- **Structural Clarity**: Maintain unwavering structural clarity where every file honors its rightful domain and precisely references shared configurations.
- **Composition Over Inheritance**: Value composition over inheritance, single responsibility components, and cohesive module organization with minimal dependencies.
- **Self-Contained Scripts**: Prefer self-contained scripts with configuration in a class at the top rather than external config files.
- **Single File Preference**: Prefer self-contained scripts with all functionality in a single main.py file rather than multiple files when appropriate.
- **Clean Structure**: Prefer clean project structure with main files organized in src directory and consolidated organization to avoid bloated/messy layouts.
- **Consistent Imports**: Prefer consistent import paths using aliases (like '@') instead of relative paths like '../..' throughout the codebase.

## Implementation Approaches
- **Relative Paths**: Make output directories relative to the script's location unless an absolute path is provided.
- **Dynamic Resolution**: Prefer dynamic path resolution in IDE configs instead of absolute paths.
- **Forward Slashes**: Prefer file paths to be displayed with forward slashes (/) instead of backslashes (\\) in console output.
- **Relative References**: Prefer relative paths (e.g., 'project/src/output/file.json') instead of absolute paths in console output.
- **Timestamp Precision**: Include seconds in timestamp filenames (e.g., 2025.05.29-kl.10.43.ss) to prevent overwriting when multiple executions occur within the same minute.

## Code Refactoring & Consolidation
- **Systematic Safety**: Apply code consolidation safely and systematically with guardrails that allow autonomous verification of results.
- **Verify Before Removing**: Always check that consolidated files work properly before removing redundant/duplicate files.
- **Centralize Values**: Identify and consolidate hardcoded values into centralized locations to avoid search and replace across multiple files.
- **Remove Unused**: Remove unused files, traces, and remains after consolidating code rather than integrating them if they're not being used.
- **Verify Necessity**: Verify if missing directories/files are actually used in the codebase before creating them.
- **Actual Reduction**: Prefer refactoring that results in actual code size reduction and more impactful consolidation of functionality, not just reorganization.
- **Drastic Consolidation**: Prefer drastic code size reduction and significant consolidation rather than minor improvements.

## Error Handling & Logging
- **Graceful Errors**: Handle errors gracefully without halting the script.
- **Terminal Persistence**: Prefer that terminal applications don't automatically close/exit when errors occur, allowing visibility of error messages.
- **Simplified Logging**: Prefer simplified logging systems that overwrite rather than append to log files.
- **Hierarchical Logging**: Prefer logging systems that express hierarchical representations, directional information, and cohesive relations between components.
- **Log Preservation**: Log files should never be deleted when execution was unsuccessful (errors occurred), only delete logs after successful execution.

## Testing & Validation
- **Understand Before Changing**: Before making changes to code, first use and understand how the utility works to avoid potential issues.
- **Autonomous Verification**: When making codebase changes, set up guardrails for autonomous validation of results to ensure predictable outcomes without errors.
- **Proper Test Organization**: Prefer tests to be organized in a proper folder structure that respects the existing codebase organization rather than having test files scattered in the root directory.
- **Clean Output Structure**: Prefer a cleaner project structure for outputs and tests, using 'outputs' directory instead of 'dumps' directory for storing generated files.

## Documentation
- **Update Documentation**: Update relevant markdown documentation after making code improvements.
- **README Accuracy**: Ensure README.md documentation accurately reflects the current state of the codebase.
- **Consolidated Documentation**: Prefer removing redundant documentation files in favor of a consolidated README.md.
- **Clean Documentation**: Maintain clean documentation that accurately reflects the current state of the project, removing outdated files and ensuring documentation is properly consolidated and organized.

## User Interaction
- **Rich Interfaces**: Use Rich library's interactive prompts (Prompt.ask, Confirm.ask) for better user experience in command-line tools.
- **Clean CLI**: Prefer cleaner and less overly explicit CLI interfaces over unnecessary "bloat" in the output.
- **1-Based Indexing**: Prefer 1-based indexing (starting from 1) instead of 0-based indexing for CLI interface numbering.
- **Markdown Formatting**: Prefer responses indented by four spaces for proper markdown rendering when copying from chat interface.
- **Console Output Formatting**: Prefer console output to be wrapped with triple quotes and markdown code block formatting for easier text selection.

---

# Synergic Frameworks

## Unified Philosophy & Direction
- **Phased Maximal Consolidation**: Drive all workflows and code transformations as targeted, sequential consolidations that drastically reduce code size, eliminate redundancy, and clarify structure, while retaining and amplifying all core functional value.
- **Recursive, Meta-Descriptive Encapsulation**: Ensure every system element describes itself and its role in relation to the whole (meta-information principle), recursively reinforcing fundamental system connections and inherent relationships—enabling the architecture to clearly communicate its purpose, composition, and transformation rationale at every hierarchical level.
- **Bidirectional Enhancement of Simplicity & Autonomous Verifiability**: Prioritize elegance and simplicity as both the bedrock and beneficiary of every consolidation step, ensuring that all autonomous verification systems and organizational patterns not only guarantee correctness but also contribute to further drift reduction, ease of use, and propagative improvement.
- **Directional Clarification & Reduction of Complexity Spirals**: Systematically prevent directionless complexity by continuously visualizing abstract patterns, extracting fundamental connections, and anchoring every decision in clarified, purpose-driven structure, reinforcing orderly progress with robust guardrails.
- **Synergistic Hierarchy-Reflective Architecture**: Architect all code, documentation, and organization as a compositional, sequential hierarchy—where each phase, component, and output inherently reflects its context and role, facilitating maximal downstream chaining and modular reuse with minimal friction or bloat.

## Constraints & Requirements
- Core philosophies of self-explanatory minimalism and phase-driven maximal consolidation must **operate simultaneously**, with all enhancements in one reinforcing the efficacy and clarity of the other.
- Eliminate redundant or conflicting directives through context-driven merging; preserve the most potent and synergic features of both systems.
- All system elements must actively promote bi-directional synergy: consolidative steps improve clarity & verifiability; verifiability and self-description reinforce safety and provide the context for further consolidation.
- Ensure every output, organizational unit, and process can compose seamlessly with downstream or adjacent processes, amplifying system-wide modularity and value extraction.

## Amplified Unified Output Specification
- Every project, codebase, or system must, as an output, display **distilled structural clarity, recursive meta-descriptiveness, and maximally consolidated value**—empowering straightforward comprehension, error-proof autonomous change, and unambiguous, lineage-traceable propagation of value.
- Documentation, output structure, and transformation processes must remain in perfect lockstep, always reflecting and enabling the vision of a self-clarifying, directionally-guided, efficiently consolidating system.

---

# Template System & Instruction Design

## Context
- The current project represents a system for easily creating *highly effective* and *generalized* `system_message` instructions, this system and these templates (instructions) are most effective when designed elegantly and in sequence (instead of a single instruction that is too verbose, we instead utilize sequenced instruction templates instead). Please familiarize yourself deeply with the Codebase **as-a-whole**, then apply (and augment) your current knowledge (about this project/Codebase) though **rooting it to the principles of clarity, simplicity, elegance, precision and structure** to ensure maximal cohesion when working with the code (**e.g. coding style that prioritize readability and self-explanatory code over excessive commenting**).

## Template Structure & Format
- **Standardized Format**: Prefer instruction templates with standardized structure: title (bracketed), interpretation (goal statement with inherent parameters), and transformation (role-based syntax with process/constraints/requirements/output specifications).
- **Single Line Titles**: Instruction files should have the title in square brackets at the beginning of the content, with all text on a single line rather than using separate paragraphs with line breaks.
- **Three-Part Format**: Use standardized three-part format ([Title] Interpretation Execute as: `{Transformation}`) for AI prompt templates.
- **Precise Language**: Prefer precise, unambiguous language in system documentation rather than metaphorical or flowery language.
- **Formalized Syntax**: Want syntax formalized to be understood only through RulesForAI.md interpretation.
- **Template Specification**: Prefer template specification format with interpretation, transformation, and testprompt fields in dictionary structure.

## Template Organization & Naming
- **Hierarchical Naming**: Use hierarchical naming conventions for AI prompt templates.
- **Category Organization**: Prefer templates to be organized into their respective category files (transformers.py, generators.py, etc.) within the template system structure.
- **Abstract Intent Separation**: Separate templates by abstract intent and directional bias (expand vs compress - never both in single instruction).
- **Category Types**: Organize by categories like amplifiers, builders, clarifiers, formatters, generators, identifiers, optimizers, reducers, transformers, translators.
- **Stage-Based IDs**: Prefer stage-based template ID organization:
  - Stage1 (1000-1999): Prototyping/testing with auto-generated IDs
  - Stage2 (2000-2999): Validated but unplaced templates
  - Stage3 (3000-3999): Finalized templates
  - Ranges 4000-9999: Reserved for future stages
- **ID Generation**: Prefer automatic ID generation for stage1 (prototype) templates, manual ID specification for stage3 (production) templates.
- **Sequence Grouping**: For template sequences that are part of the same logical group, use the same number with incremental letters (e.g., 0004-a, 0004-b, 0004-c) rather than separate sequential numbers.

## Template Sequences & Progression
- **Progressive Sequences**: Prefer creating progressive template sequences (a-d format) where each iteration becomes shorter and more precise.
- **Gradual Reduction**: Template sequences should follow gradual reduction pattern (comprehensive → focused → essential → core) when transforming content.
- **Precision Improvement**: Template sequences should be designed to progressively improve precision, structure, and conciseness rather than expanding content.
- **Multi-Step Instructions**: Prefer multi-step instruction sequences structured as discrete templates that can be chained together, with each step having clear input/output specifications.
- **Modular Components**: Prefer creating multi-template sequences with modular components (e.g., persona_seed → analysis_engine → response_renderer) that work together as integrated systems.
- **Transformation Focus**: The essential component is the directive 'Your goal is not to **answer** the input prompt, but to **rephrase** it' - this transformation approach is fundamental to the template architecture.

## Template Testing & Execution
- **Sequence Syntax**: Support embedding instruction sequences directly in prompts using syntax like [SEQ:1010|1020:a|3001|3005|3003] which executes multiple templates in sequence.
- **Chained Sequences**: Support chained sequences like [SEQ:0110|0005] for testing template combinations.
- **Isolated Testing**: When testing template sequences, isolate them from chained sequences to avoid noise and get cleaner evaluation results.
- **Workflow Steps**: Want clear step-by-step workflows for prototype sequence creation and testing.
- **Input Display**: When displaying sequence execution steps, show the actual input for each instruction rather than repeating the initial user prompt.
- **Context Preservation**: When using chain mode, always include the initial prompt alongside each step's input to maintain connection to the original inquiry.
- **Direct Embedding**: When using chain mode with LLMs, embed the initial input directly into the prompt before sending it to the LLM rather than creating a separate write operation.
- **Consistent Handling**: Handle lvl1 and lvl2 templates consistently, with lvl1 templates passed via '--sequence' and lvl2 templates passed via '--aggregator'.

## Template Creation & Workflow
- **Creation Process**: Template creation workflow starts with:
  1. Functional need
  2. Determining category/naming/ID range
  3. Drafting the interpretation section
- **Existing Patterns**: Prefer solutions that leverage existing patterns and concepts in the system rather than introducing new mechanisms or approaches.
- **Flexible JSON Parsing**: Make JSON parsers more flexible to handle trailing commas and other non-strict JSON formatting.
- **Generalized Generators**: Generalize template generators to reflect the structured pattern definition for templates, reducing code size and increasing flexibility.
- **Self-Assembling Design**: Prefer self-assembling system design patterns that guide natural organization but value flexibility.
- **Incremental Steps**: Want specific small incremental steps rather than large changes.
- **Autonomy**: Prioritize autonomy and automation in system design to reduce manual labor.
- **Self-Organization**: Want solutions that self-organize rather than require manual intervention.
- **Deterministic Guidance**: Prefer deterministic guidance systems that leverage natural factors for template organization rather than automated systems that make choices autonomously.

---

Make sure you understand previously accumulated knowledge in `".vscode\Augment-Memories.md"`, then identify the most unique and high-value findamental concept of this project.
