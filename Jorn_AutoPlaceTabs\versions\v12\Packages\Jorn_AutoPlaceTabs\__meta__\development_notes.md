# Development Notes - Jorn_AutoPlaceTabs

## Design Principles

This plugin follows the established Jorn plugin ecosystem patterns:

### Architecture
- **EventListener-based**: Core functionality through `sublime_plugin.EventListener`
- **Command-driven**: Manual operations via `sublime_plugin.WindowCommand`
- **Settings-centric**: All behavior configurable through JSON settings
- **Rate-limited**: Prevents infinite loops and excessive operations

### Code Organization
- Single main Python file with all core logic
- Separate configuration files for different aspects
- Consistent naming with `PLUGIN_NAME` constant
- Instance singleton pattern for EventListener

### Integration Points
- Compatible with existing Jorn tab management plugins
- Follows established menu and command structure
- Uses consistent debug logging patterns
- Maintains familiar user experience

## Implementation Details

### Tab Placement Logic
1. **Trigger Events**: `on_activated_async` and `on_load_async`
2. **Rule Evaluation**: File type → Directory → Project membership
3. **Target Determination**: Calculate appropriate group index
4. **Placement Execution**: Use `window.set_view_index()` with safety guards

### Rate Limiting
- Maximum 5 placements per second
- Timestamp tracking with deque
- Recursion guard (`_is_placing` flag)
- Placement history to prevent oscillation

### Rule Priority
1. File type rules (highest priority)
2. Directory rules
3. Project membership rules
4. Default fallback behavior

## Future Enhancements

### Planned Features
- Layout auto-adjustment based on tab distribution
- Custom rule scripting support
- Integration with project-specific settings
- Visual indicators for auto-placed tabs

### Compatibility
- Designed to work with `Jorn_AutosortTabs` for within-group sorting
- Compatible with `Jorn_OrganizeViewsByDirectory` for directory-based organization
- Integrates with `Jorn_TabUtils` for general tab operations

## Testing Strategy

### Manual Testing
1. Create multi-group layout
2. Open files of different types
3. Verify automatic placement
4. Test manual commands
5. Validate rate limiting

### Edge Cases
- Unsaved files
- Files outside project
- Non-existent target groups
- Rapid tab switching
- Plugin reload scenarios
