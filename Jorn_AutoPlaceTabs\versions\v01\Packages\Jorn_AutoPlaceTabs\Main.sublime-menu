[
    {
        // Not exactly sure what this is for ... ?
        "id": "view",
        "children":
        [
            { "id": "side_bar" },
            { "command": "sort_tabs_menu" }
        ]
    },
    {
        // Ribbon-Menu: Preferences -> Package Settings
        "caption": "Preferences",
        "mnemonic": "n",
        "id": "preferences",
        "children":
        [
            {
                "caption": "Package Settings",
                "mnemonic": "P",
                "id": "package-settings",
                "children":
                [
                    {
                        "caption": "Jorn_SortTabs",
                        "children":
                        [
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Jorn_SortTabs.py",
                                "command": "open_file",
                                "args": {"file": "${packages}/Jorn_SortTabs/Jorn_SortTabs.py"},
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Settings",
                                "command": "open_file",
                                "args": {"file": "${packages}/Jorn_SortTabs/Jorn_SortTabs.sublime-settings"},
                            },
                            {
                                "caption": "Settings – User",
                                "command": "open_file",
                                "args": {"file": "${packages}/User/Default.sublime-settings"},
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Keymap",
                                "command": "open_file",
                                "args": {"file": "${packages}/Jorn_SortTabs/Jorn_SortTabs.sublime-keymap"},
                            },
                            {
                                "caption": "Keymap – User",
                                "command": "open_file",
                                "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Commands",
                                "command": "open_file",
                                "args": {"file": "${packages}/Jorn_SortTabs/Jorn_SortTabs.sublime-commands"},
                            },
                            {
                                "caption": "Commands – User",
                                "command": "open_file",
                                "args": {"file": "${packages}/User/Default (Windows).sublime-commands"},
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                            {
                                "caption": "Open Folder -> Todo",
                                "args": {"cmd": ["explorer.exe", "${packages}/Jorn_SortTabs"]}
                                // "command": "jorn_open_directory",
                                // "args": {"group": -1, "index": -1 },
                            },
                            {/* --------------------------------------------- */ "caption": "-"},
                        ]
                    }
                ]
            }
        ]
    },
    {
        // Ribbon-Menu: Jorn Tools
        "caption": "Jorn Tools",
        "mnemonic": "J",
        "id": "jorn-tools",
        "children": [
            {
                "caption": "Jorn_SortTabs",
                "children":
                [
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Jorn_SortTabs.py",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_SortTabs/Jorn_SortTabs.py"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Settings",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_SortTabs/Jorn_SortTabs.sublime-settings"},
                    },
                    {
                        "caption": "Settings – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default.sublime-settings"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Keymap",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_SortTabs/Jorn_SortTabs.sublime-keymap"},
                    },
                    {
                        "caption": "Keymap – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Commands",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_SortTabs/Jorn_SortTabs.sublime-commands"},
                    },
                    {
                        "caption": "Commands – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default (Windows).sublime-commands"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Open Folder -> Todo",
                        "args": {"cmd": ["explorer.exe", "${packages}/Jorn_SortTabs"]}
                        // "command": "jorn_open_directory",
                        // "args": {"group": -1, "index": -1 },
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                ]
            }
        ]
    }
]

