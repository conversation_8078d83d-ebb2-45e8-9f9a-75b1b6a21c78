# Technology Stack

## Core Platform
- **Sublime Text 4** - Target editor platform
- **Python 3.8+** - Plugin implementation language
- **Sublime Text Plugin API** - Core integration framework

## Plugin Architecture
- **sublime_plugin.EventListener** - Event-driven tab management
- **sublime_plugin.TextCommand** - User-invoked commands
- **sublime.Settings** - Configuration management
- **sublime.Window/View** - UI manipulation

## Configuration Systems
- **JSON** - Settings and project files (.sublime-settings, .sublime-project)
- **XML** - Menu definitions (.sublime-menu)
- **YAML** - Keymap definitions (.sublime-keymap)

## File Organization
- **Package Structure** - Standard Sublime Text package layout
- **Module System** - Python imports and lib/ subdirectory
- **Settings Hierarchy** - Global → Project → User preferences

## Development Tools
- **Git** - Version control (implied by version history)
- **Markdown** - Documentation format
- **VS Code Workspace** - Development environment (v23)

## Key Dependencies
- **fnmatch** - Pattern matching for file rules
- **collections** - Data structures (defaultdict, deque)
- **os/time** - System utilities and timing controls
