# Elegant Layout System

## Overview

The `Jorn_AutoPlaceTabs` plugin now features a sophisticated, configurable layout management system inspired by `Jorn_SublimeTabOrganizer` and `Origami`. This replaces the previous hardcoded layout generation with an elegant, reusable system.

## Architecture

### Layout Manager
The `LayoutManager` class handles all layout operations:
- **Layout Discovery**: Finds appropriate layouts from multiple sources
- **Strategy-Based Generation**: Creates layouts using configurable strategies
- **Intelligent Selection**: Automatically chooses the best layout for group requirements
- **Graceful Fallbacks**: Provides sensible defaults when specific layouts aren't available

### Layout Sources (Priority Order)

1. **Exact Group Count Match**: `layout_configs["8"]` for exactly 8 groups
2. **Named Layout Selection**: Automatically selects from `named_layouts` that can accommodate the groups
3. **Strategy-Based Generation**: Uses `layout_strategy` to generate appropriate layouts
4. **Fallback**: Simple columns layout as last resort

## Configuration

### Layout Strategy
```json
{
    "layout_strategy": "auto"  // "auto", "columns", "rows", "grid"
}
```

**Strategies:**
- `"auto"`: Intelligently chooses the best layout type based on group count
- `"columns"`: Always generates horizontal column layouts
- `"rows"`: Always generates vertical row layouts
- `"grid"`: Prefers grid layouts when possible

### Layout Configs
Specific layouts for exact group counts:
```json
{
    "layout_configs": {
        "8": {
            "cols": [0.0, 0.33, 0.66, 1.0],
            "rows": [0.0, 0.33, 0.66, 1.0],
            "cells": [
                [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],
                [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2],
                [0, 2, 1, 3], [1, 2, 2, 3]
            ],
            "nickname": "3x3 Grid for 8 Groups"
        }
    }
}
```

### Named Layouts
Reusable layouts that can be automatically selected:
```json
{
    "named_layouts": {
        "counterclockwise": {
            "cells": [[0, 0, 1, 1], [1, 0, 2, 2], [0, 1, 1, 2]],
            "cols": [0.0, 0.5, 1.0],
            "rows": [0.0, 0.114307153577, 1.0],
            "nickname": "Counterclockwise"
        },
        "special_ringlandskap": {
            "cells": [
                [0, 0, 1, 2], [1, 1, 2, 3], [1, 0, 3, 1],
                [2, 1, 3, 3], [0, 2, 1, 3]
            ],
            "cols": [0.0, 0.469470218497, 0.75, 1.0],
            "rows": [0.0, 0.364111498258, 0.621118012422, 1.0],
            "nickname": "Special Ringlandskap"
        }
    }
}
```

## Layout Selection Logic

### For 8 Groups (your use case):

1. **Check `layout_configs["8"]`** - If defined, use it
2. **Check `named_layouts`** - Find layouts with ≥8 groups (but ≤10 to avoid waste)
3. **Generate by strategy**:
   - `"auto"`: 3x3 grid (9 cells, uses 8)
   - `"grid"`: 3x3 grid
   - `"columns"`: 8 horizontal columns
   - `"rows"`: 8 vertical rows
4. **Fallback**: Simple 8 columns

### Auto Strategy Intelligence

The `"auto"` strategy chooses optimal layouts:
- **1-2 groups**: Columns
- **3-4 groups**: 2x2 grid
- **5-6 groups**: 2x3 grid  
- **7-9 groups**: 3x3 grid
- **10+ groups**: Columns (to avoid tiny panes)

## Commands

### Test Layout
`Jorn AutoPlace: Test Layout` - Interactive layout testing:
- Browse all available layouts
- Preview generated layouts
- Apply layouts immediately for testing

### Show Current Rules
Enhanced to display:
- Active layout strategy
- Available configured layouts
- Available named layouts
- Layout selection logic

## Examples

### Web Development Project
```json
{
    "jorn_auto_place_tabs": {
        "layout_strategy": "grid",
        "max_groups": 6,
        "named_layouts": {
            "web_dev": {
                "cells": [
                    [0, 0, 1, 1], [1, 0, 2, 1],  // Code files
                    [0, 1, 1, 2], [1, 1, 2, 2],  // Styles & tests
                    [0, 2, 2, 3]                  // Documentation (wide)
                ],
                "cols": [0.0, 0.5, 1.0],
                "rows": [0.0, 0.33, 0.66, 1.0],
                "nickname": "Web Development Layout"
            }
        }
    }
}
```

### Your Original Use Case
```json
{
    "jorn_auto_place_tabs": {
        "file_type_rules": {
            "0": [".py", ".pyw"],
            "1": [".js", ".ts", ".jsx", ".tsx"],
            "2": [".html", ".css", ".vue"],
            "7": [".md", ".txt", ".json"]
        },
        "auto_adjust_layout": true,
        "layout_strategy": "auto",
        "max_groups": 8,
        "layout_configs": {
            "8": {
                "cols": [0.0, 0.33, 0.66, 1.0],
                "rows": [0.0, 0.33, 0.66, 1.0],
                "cells": [
                    [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],
                    [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2],
                    [0, 2, 1, 3], [1, 2, 2, 3]
                ],
                "nickname": "Perfect 8-Group Grid"
            }
        }
    }
}
```

## Benefits

### Elegance
- **Configurable**: No hardcoded layouts
- **Reusable**: Named layouts work across projects
- **Extensible**: Easy to add new layout strategies
- **Intelligent**: Automatic selection based on requirements

### Flexibility
- **Multiple Sources**: Configs, named layouts, generated layouts
- **Strategy-Based**: Different generation approaches
- **Project-Specific**: Override layouts per project
- **Fallback-Safe**: Always provides a working layout

### Integration
- **Origami-Compatible**: Uses same layout format
- **Jorn-Consistent**: Follows established plugin patterns
- **User-Friendly**: Interactive testing and clear documentation

This system transforms the plugin from having hardcoded layout generation to a sophisticated, configurable layout management system that rivals dedicated layout plugins while maintaining the simplicity and elegance of the Jorn ecosystem.
