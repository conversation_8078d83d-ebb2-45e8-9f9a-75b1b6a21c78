{
    "folders": [
        {
            "path": ".",
            "name": "My Project"
        }
    ],
    "settings": {
        "jorn_auto_place_tabs": {
            // Core settings
            "auto_place_on_activation": true,
            "auto_adjust_layout": true,
            "layout_mode": "compact",
            "layout_type": "grid",
            
            // UNIFIED SEMANTIC RULE SYSTEM
            // All file type, directory, and custom rules are defined within each group rule
            "group_rules": {
                "0": [
                    {
                        "description": "Project Python source files (not tests)",
                        "match": {
                            "extensions": [".py", ".pyw"],
                            "directory_patterns": ["*/src/*", "*/lib/*", "*/app/*"],
                            "types": ["project"]
                        },
                        "exclude": {
                            "file_name_patterns": ["test_*.py", "__init__.py"],
                            "directory_patterns": ["*/tests/*", "*/test/*"]
                        }
                    }
                ],
                "1": [
                    {
                        "description": "Project JavaScript/TypeScript source",
                        "match": {
                            "extensions": [".js", ".ts", ".jsx", ".tsx"],
                            "directory_patterns": ["*/src/*", "*/components/*"],
                            "types": ["project"]
                        },
                        "exclude": {
                            "file_name_patterns": ["*.test.*", "*.spec.*"]
                        }
                    }
                ],
                "2": [
                    {
                        "description": "All test files",
                        "match": {
                            "file_name_patterns": ["test_*.py", "*.test.js", "*.spec.ts", "*_test.go"],
                            "directory_patterns": ["*/tests/*", "*/test/*", "*/__tests__/*"],
                            "types": ["project"]
                        }
                    }
                ],
                "3": [
                    {
                        "description": "Project web files",
                        "match": {
                            "extensions": [".html", ".css", ".scss", ".vue", ".svelte"],
                            "types": ["project"]
                        }
                    }
                ],
                "4": [
                    {
                        "description": "Configuration and data files",
                        "match": {
                            "extensions": [".json", ".yaml", ".yml", ".toml", ".ini", ".cfg"],
                            "file_name_patterns": ["*.config.*", ".*rc", "Dockerfile*", "*.env*"]
                        }
                    }
                ],
                "5": [
                    {
                        "description": "Documentation files",
                        "match": {
                            "extensions": [".md", ".txt", ".rst"],
                            "directory_patterns": ["*/docs/*", "*/documentation/*"]
                        }
                    }
                ],
                "6": [
                    {
                        "description": "External dirty files (but not unsaved)",
                        "match": {
                            "types": ["external", "dirty"]
                        },
                        "exclude": {
                            "types": ["unsaved"]
                        }
                    }
                ],
                "7": [
                    {
                        "description": "Temporary and unsaved files",
                        "match": {
                            "types": ["unsaved", "scratch"]
                        }
                    },
                    {
                        "description": "Log and temporary files",
                        "match": {
                            "extensions": [".log", ".tmp", ".temp"],
                            "file_name_patterns": ["*.backup", "*.bak"]
                        }
                    }
                ]
            }
        }
    }
}
