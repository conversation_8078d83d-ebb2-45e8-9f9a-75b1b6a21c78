import sublime, sublime_plugin
import time

# If Sublime Text 3 or higher, import necessary modules.
if int(sublime.version()) >= 3000:
    import Jorn_SortTabs.Jorn_SortTabs as sort_tabs
    from Jorn_SortTabs.Jorn_SortTabs import settings, internal_settings

# -----------------------------------------------------------------------------

class AutoSortTabsListener(sublime_plugin.EventListener):
    """
    This class automatically sorts tabs in Sublime Text based on different
    triggers: loading a file, saving a file, and activating a tab. The sorting
    behavior is controlled by the user-defined settings in
    'Jorn_SortTabs.sublime-settings'.
    """
    def on_load(self, view):
        """
        Triggered when a file is loaded. If the 'autosort_on_load_save' setting
        is enabled, tabs will be sorted according to the 'autosort_command'.
        """
        if settings().get('autosort_on_load_save'):
            if not self._run_sort(view, 'autosort_on_load_save'):
                view.settings().set('sorttabs_tosort', True)

    def on_post_save(self, view):
        """
        Triggered after a file is saved. If the 'autosort_on_load_save' setting
        is enabled, tabs will be sorted according to the 'autosort_command'.
        """
        if settings().get('autosort_on_load_save'):
            self._run_sort(view, '')


    def on_activated(self, view):
      """
      Triggered when a tab is activated. If the 'autosort_on_tab_activated'
      setting is enabled, tabs will be sorted according to the
      'autosort_command' (or based on the previous sort if unspecified).
      """
      view.settings().set('sorttabs_lastactivated', time.time())
      if settings().get('autosort_on_tab_activated'):
          if not self._run_sort(view, 'autosort_on_tab_activated'):
              view.settings().set('sorttabs_tosort', True)

    def _run_sort(self, view, trigger):
        """
        Helper function to perform the sorting of tabs. It retrieves the
        sorting command from settings and runs it on the current view if the
        view is valid.
        """
        if view.window() and view.window().get_view_index(view)[1] != -1:
            current_view = view.window().active_view()  # Remember the active view
            cmd = settings().get('autosort_command')
            if not cmd:
                cmd = internal_settings().get('last_cmd')
            if cmd:
                view.window().run_command(cmd)
            view.window().focus_view(current_view)  # Refocus on the active view
            return True
        return False

    # def _run_sort(self, view, trigger):
    #     """
    #     Helper function to perform the sorting of tabs. It retrieves the
    #     sorting command from settings and runs it on the current view if the
    #     view is valid.
    #     """
    #     if view.window() and view.window().get_view_index(view)[1] != -1:
    #         cmd = settings().get('autosort_command')
    #         if not cmd:
    #             cmd = internal_settings().get('last_cmd')
    #         if cmd:
    #             view.window().run_command(cmd)
    #         return True
    #     return False
