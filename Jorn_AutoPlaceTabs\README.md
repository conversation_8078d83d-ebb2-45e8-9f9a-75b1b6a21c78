# Jorn AutoPlace Tabs

**Semantically-aware, layout-centric workspace intelligence for Sublime Text 4** - Automatically organize your tabs using intelligent layouts that understand file context and lifecycle.

## 🎯 **DEVELOPER'S ULTIMATE INTENT**

### **Core Vision: Workspace Intelligence That Feels Natural**
This plugin embodies a **sophisticated yet elegant** approach to tab organization that:
- **Understands file context semantically** (not just by extension)
- **Organizes workspaces by layout paradigms** (not just rules)
- **Adapts intelligently to actual usage** (compact mode)
- **Stays out of the way until needed** (reference-based configuration)
- **Provides power when required** (rich semantic matching)

### **"Compact Intelligence" Over "Hardcoded Behavior"**
Eliminates rigid, predetermined group structures in favor of:
- **Compact mode**: Only creates groups for tabs that actually exist
- **Literal mode**: Uses exact group numbers when explicitly specified
- **Adaptive layouts**: Workspaces that respond to real usage patterns

### **"Layout + Rules as Unified Entities"**
Each layout is a complete workspace definition containing:
- **Physical structure** (cols, rows, cells)
- **Semantic placement rules** (what goes where based on file state)
- **Behavior settings** (sorting, thresholds, timing)
- **Metadata** (name, description, use cases)

## ✨ Core Philosophy

### 🎯 **Layout-Centric Design**
Each layout is a complete workspace definition with its own rules. Rules are **children** of layouts, not standalone configurations.

### 📋 **Reference-Based Configuration**
The main settings file serves as a **template library and reference**. Real configuration happens in your `.sublime-project` files where you copy and customize layouts.

### 🧠 **Semantic State Awareness**
Rich file state detection: `project`, `external`, `dirty`, `unsaved`, `deleted`, `scratch`, `stale`, `recently_opened`, `short_lived`, `large_file`, and more.

### ⚡ **Progressive Enhancement Architecture**
- **Zero-configuration startup** - works with sensible defaults
- **Template-driven customization** - copy and modify layouts from library
- **Project-specific power** - each project can have its own workspace logic

## 🏗️ **Layout System Architecture**

### **Semantic File State Detection**
The plugin understands **17+ file states** including:
- **Lifecycle states**: `unsaved`, `dirty`, `saved`, `scratch`, `deleted`, `missing`
- **Location states**: `project`, `external` (relative to project folders)
- **Activity states**: `active`, `recently_opened`, `stale`, `short_lived`
- **Content states**: `empty`, `non_empty`, `large_file`, `small_file`
- **Visibility states**: `visible`, `background`, `readonly`

### **Complete Layout Definitions**
Each layout is a unified entity containing:
- **Physical structure** (cols, rows, cells) - how the workspace looks
- **Semantic rules** (what goes where) - based on file state combinations
- **Behavior settings** (sorting, thresholds) - how it behaves
- **Metadata** (name, description) - documentation and intent

### **Intelligent Group Management**
- **Compact mode**: Creates only the groups needed for actual tabs
- **Literal mode**: Uses exact group numbers as specified
- **Adaptive behavior**: Layouts respond to real usage patterns
- **Context awareness**: Rules consider file lifecycle and project relationship

### **Multi-Dimensional Rule Matching**
Rules can match on **combinations** of:
- **File extensions**: `.py`, `.js`, `.css`, `.vue`
- **Directory patterns**: `*/src/*`, `*/tests/*`, `*/components/*`
- **File name patterns**: `*.test.*`, `*.spec.*`, `*.config.*`
- **Semantic states**: `dirty + project`, `external + stale`, `unsaved + short_lived`
- **Exclusion patterns**: Explicitly exclude certain combinations

## 🚀 Quick Start

### **Understanding the Architecture**
1. **Main settings** = Template library and reference (not active rules)
2. **Project settings** = Where you copy and customize layouts for real use
3. **Layouts** = Complete workspace definitions (structure + rules + behavior)
4. **Rules** = Children of layouts that define semantic placement logic

### 1. Browse the Template Library
Explore `Preferences → Package Settings → Jorn AutoPlace Tabs → Settings` to see:
- **17+ semantic file types** with detailed explanations
- **3 complete layout templates** ready for customization
- **Usage examples** showing project-specific configuration

### 2. Configure Your Project
Copy a layout template to your `.sublime-project` file and customize:

```json
{
    "folders": [{"path": "."}],
    "settings": {
        "jorn_auto_place_tabs": {
            "active_layout": "web_development",
            "web_development": {
                "layout": {
                    "cols": [0.0, 0.4, 0.7, 1.0],
                    "rows": [0.0, 0.6, 1.0],
                    "cells": [
                        [0, 0, 1, 2],  // Main code (tall)
                        [1, 0, 2, 1],  // Tests
                        [2, 0, 3, 1],  // Styles
                        [1, 1, 3, 2]   // Scratch/external
                    ]
                },
                "rules": {
                    "0": {
                        "name": "Active Development",
                        "match": {
                            "extensions": [".js", ".ts", ".vue"],
                            "types": ["project", "dirty"],
                            "directory_patterns": ["*/src/*"]
                        }
                    },
                    "1": {
                        "name": "Tests",
                        "match": {
                            "file_name_patterns": ["*.test.*", "*.spec.*"]
                        }
                    },
                    "2": {
                        "name": "Styles",
                        "match": {
                            "extensions": [".css", ".scss"]
                        }
                    },
                    "3": {
                        "name": "Temporary",
                        "match": {
                            "types": ["unsaved", "external", "scratch"]
                        }
                    }
                }
            }
        }
    }
}
```

### 3. Activate and Use
- Files automatically place according to your layout's semantic rules
- Compact mode creates only the groups you actually need
- Rich semantic detection understands file context and lifecycle
- Manual override commands available for full user control

## 🧠 **Design Philosophy: Why This Approach**

### **Beyond Simple Pattern Matching**
Traditional tab management plugins match files by extension or path. This plugin understands **file context and lifecycle**:
- A `.js` file that's `dirty + project + recently_opened` goes to active development
- The same `.js` file that's `external + stale` goes to background/cleanup area
- An `unsaved + scratch` buffer goes to temporary workspace regardless of content

### **Layouts as Workspace Paradigms**
Instead of global rules that apply everywhere, each layout represents a **complete workspace paradigm**:
- **Web development layout**: Optimized for frontend workflows with test separation
- **Data science layout**: Designed for notebooks, source code, and data file organization
- **Simple columns**: Basic organization for general-purpose work

### **Reference-Based Configuration Philosophy**
The main settings file is intentionally **not active** - it's a template library:
- **Prevents bloat**: No unused rules cluttering your workspace
- **Encourages project-specific thinking**: Each project gets its own optimized layout
- **Maintains simplicity**: Copy what you need, ignore what you don't
- **Enables experimentation**: Try different layouts without affecting global settings

### **Compact Intelligence vs Hardcoded Behavior**
The plugin adapts to **actual usage** rather than forcing predetermined structures:
- **Compact mode**: "I have 3 types of files open, create 3 groups"
- **Literal mode**: "I want exactly these 5 groups regardless of what's open"
- **Adaptive thresholds**: Behavior changes based on project size and file activity

This creates workspace intelligence that **feels natural and unobtrusive** while providing sophisticated organizational capabilities for power users.

## 📋 Commands

| Command | Description |
|---------|-------------|
| `Jorn AutoPlace: Place Current Tab` | Manually place the active tab |
| `Jorn AutoPlace: Place All Tabs` | Place all open tabs |
| `Jorn AutoPlace: Toggle Auto-Placement` | Enable/disable automatic placement |
| `Jorn AutoPlace: Show Current Rules` | Display active rules and settings |
| `Jorn AutoPlace: Reload Settings` | Refresh configuration |

## ⚙️ Configuration

### Global Settings
Configure via `Preferences → Package Settings → Jorn AutoPlace Tabs → Settings`

### Project-Specific Settings
Add to your `.sublime-project` file:
```json
{
    "folders": [{"path": "."}],
    "settings": {
        "jorn_auto_place_tabs": {
            "auto_place_on_activation": true,
            "file_type_rules": {
                "0": [".py", ".pyw"],
                "1": [".js", ".ts"]
            }
        }
    }
}
```

## 🎨 Layout Templates

### Web Development
```json
"layout_configs": {
    "4": {
        "cols": [0.0, 0.4, 0.7, 1.0],
        "rows": [0.0, 0.6, 1.0],
        "cells": [
            [0, 0, 1, 2],  // Main code (tall)
            [1, 0, 2, 1],  // Tests
            [2, 0, 3, 1],  // Styles
            [1, 1, 3, 2]   // Scratch/external
        ]
    }
}
```

## 🔍 Rule Types

### 1. File Type Rules
Match by file extension or name patterns:
```json
"file_type_rules": {
    "0": [".py", ".pyw", "*.python"],
    "1": ["*.test.js", "*.spec.ts"]
}
```

### 2. Directory Rules
Match by folder patterns (supports glob):
```json
"directory_rules": {
    "0": ["*/src/*", "*/lib/*"],
    "1": ["*/tests/*", "*/__tests__/*"]
}
```

### 3. Semantic Rules
Match by file state:
```json
"semantic_rules": {
    "0": ["project", "saved"],     // Project files that are saved
    "1": ["external"],             // Files outside project
    "2": ["unsaved", "dirty"]      // Unsaved or modified files
}
```

### 4. Custom Rules
Advanced pattern matching with priorities:
```json
"custom_rules": [
    {
        "name": "Test files",
        "pattern": "*test*.py",
        "group": 1,
        "priority": 20
    }
]
```

## 🛡️ Safety Features

- **Rate limiting** - Maximum 5 placements per second
- **Recursion guards** - Prevents infinite placement loops
- **Placement history** - Tracks recent placements to prevent oscillation
- **Error resilience** - Graceful fallbacks for invalid configurations

## 🎯 Design Philosophy

**Simplicity First** - Works perfectly with zero configuration
**Progressive Enhancement** - Advanced features available when needed
**User Control** - Automatic behavior with manual override capability
**Performance** - Optimized for large projects with many files
**Standards Compliance** - Follows all Sublime Text package conventions
