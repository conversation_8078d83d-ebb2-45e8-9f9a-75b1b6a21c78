# Jorn AutoPlace Tabs

**Layout-centric tab placement for Sublime Text 4** - Automatically organize your tabs using intelligent layouts where rules are children of layout definitions.

## ✨ Core Philosophy

### 🎯 **Layout-Centric Design**
Each layout is a complete workspace definition with its own rules. Rules are **children** of layouts, not standalone configurations.

### 📋 **Reference-Based Settings**
The main settings file serves as a **reference and template library**. Real configuration happens in your `.sublime-project` files.

### 🧠 **Semantic State Awareness**
Rich file state detection: `project`, `external`, `dirty`, `unsaved`, `deleted`, `scratch`, `stale`, `recently_opened`, and more.

### ⚡ **Compact Intelligence**
Smart group creation - only creates the groups you actually need, not hardcoded numbers.

## 🏗️ **Layout System**

### **Complete Layout Definitions**
Each layout includes:
- **Physical layout** (cols, rows, cells)
- **Group rules** (what goes where)
- **Behavior settings** (sorting, thresholds)
- **Metadata** (name, description)

### **Smart Group Management**
- **Compact mode**: Creates minimal groups needed
- **Literal mode**: Uses exact group numbers specified
- **Auto-adjustment**: Layouts adapt to actual tab requirements

### **State-Aware Rules**
Rules can match on:
- **File extensions**: `.py`, `.js`, `.css`
- **Directory patterns**: `*/src/*`, `*/tests/*`
- **File states**: `dirty`, `unsaved`, `project`, `external`
- **Time-based states**: `recently_opened`, `stale`, `short_lived`
- **Size-based states**: `large_file`, `small_file`, `empty`

## 🚀 Quick Start

### 1. Reference the Template Library
Browse `Preferences → Package Settings → Jorn AutoPlace Tabs → Settings` to see available layout templates.

### 2. Configure Your Project
Add a layout to your `.sublime-project` file:

```json
{
    "folders": [{"path": "."}],
    "settings": {
        "jorn_auto_place_tabs": {
            "active_layout": "web_development",
            "web_development": {
                "layout": {
                    "cols": [0.0, 0.4, 0.7, 1.0],
                    "rows": [0.0, 0.6, 1.0],
                    "cells": [
                        [0, 0, 1, 2],  // Main code (tall)
                        [1, 0, 2, 1],  // Tests
                        [2, 0, 3, 1],  // Styles
                        [1, 1, 3, 2]   // Scratch/external
                    ]
                },
                "rules": {
                    "0": {
                        "name": "Active Development",
                        "match": {
                            "extensions": [".js", ".ts", ".vue"],
                            "types": ["project", "dirty"],
                            "directory_patterns": ["*/src/*"]
                        }
                    },
                    "1": {
                        "name": "Tests",
                        "match": {
                            "file_name_patterns": ["*.test.*", "*.spec.*"]
                        }
                    },
                    "2": {
                        "name": "Styles",
                        "match": {
                            "extensions": [".css", ".scss"]
                        }
                    },
                    "3": {
                        "name": "Temporary",
                        "match": {
                            "types": ["unsaved", "external", "scratch"]
                        }
                    }
                }
            }
        }
    }
}
```

### 3. Activate and Use
- Files automatically place according to your layout rules
- Use compact mode for minimal group creation
- Override manually when needed

## 📋 Commands

| Command | Description |
|---------|-------------|
| `Jorn AutoPlace: Place Current Tab` | Manually place the active tab |
| `Jorn AutoPlace: Place All Tabs` | Place all open tabs |
| `Jorn AutoPlace: Toggle Auto-Placement` | Enable/disable automatic placement |
| `Jorn AutoPlace: Show Current Rules` | Display active rules and settings |
| `Jorn AutoPlace: Reload Settings` | Refresh configuration |

## ⚙️ Configuration

### Global Settings
Configure via `Preferences → Package Settings → Jorn AutoPlace Tabs → Settings`

### Project-Specific Settings
Add to your `.sublime-project` file:
```json
{
    "folders": [{"path": "."}],
    "settings": {
        "jorn_auto_place_tabs": {
            "auto_place_on_activation": true,
            "file_type_rules": {
                "0": [".py", ".pyw"],
                "1": [".js", ".ts"]
            }
        }
    }
}
```

## 🎨 Layout Templates

### Web Development
```json
"layout_configs": {
    "4": {
        "cols": [0.0, 0.4, 0.7, 1.0],
        "rows": [0.0, 0.6, 1.0],
        "cells": [
            [0, 0, 1, 2],  // Main code (tall)
            [1, 0, 2, 1],  // Tests
            [2, 0, 3, 1],  // Styles
            [1, 1, 3, 2]   // Scratch/external
        ]
    }
}
```

## 🔍 Rule Types

### 1. File Type Rules
Match by file extension or name patterns:
```json
"file_type_rules": {
    "0": [".py", ".pyw", "*.python"],
    "1": ["*.test.js", "*.spec.ts"]
}
```

### 2. Directory Rules
Match by folder patterns (supports glob):
```json
"directory_rules": {
    "0": ["*/src/*", "*/lib/*"],
    "1": ["*/tests/*", "*/__tests__/*"]
}
```

### 3. Semantic Rules
Match by file state:
```json
"semantic_rules": {
    "0": ["project", "saved"],     // Project files that are saved
    "1": ["external"],             // Files outside project
    "2": ["unsaved", "dirty"]      // Unsaved or modified files
}
```

### 4. Custom Rules
Advanced pattern matching with priorities:
```json
"custom_rules": [
    {
        "name": "Test files",
        "pattern": "*test*.py",
        "group": 1,
        "priority": 20
    }
]
```

## 🛡️ Safety Features

- **Rate limiting** - Maximum 5 placements per second
- **Recursion guards** - Prevents infinite placement loops
- **Placement history** - Tracks recent placements to prevent oscillation
- **Error resilience** - Graceful fallbacks for invalid configurations

## 🎯 Design Philosophy

**Simplicity First** - Works perfectly with zero configuration
**Progressive Enhancement** - Advanced features available when needed
**User Control** - Automatic behavior with manual override capability
**Performance** - Optimized for large projects with many files
**Standards Compliance** - Follows all Sublime Text package conventions

## 📄 License

MIT License - See LICENSE file for details

## 🤝 Contributing

Contributions welcome! Please read CONTRIBUTING.md for guidelines.
