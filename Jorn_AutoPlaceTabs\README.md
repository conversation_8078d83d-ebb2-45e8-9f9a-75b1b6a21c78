# Jorn AutoPlace Tabs

Layout-centric tab placement for Sublime Text 4. Organizes tabs using semantic file state detection and project-specific layout definitions.

## Core Architecture

### Layout-Centric Design
- Layouts contain physical structure + rules + behavior settings
- Rules are children of layouts, not standalone configurations
- Each layout defines a complete workspace paradigm

### Reference-Based Configuration
- Main settings file = template library (inactive)
- Project settings = active configuration via `.sublime-project` files
- Copy and customize layouts per project

### Semantic State Detection
File states: `project`, `external`, `dirty`, `unsaved`, `deleted`, `scratch`, `stale`, `recently_opened`, `short_lived`, `large_file`, `small_file`, `empty`, `readonly`, `active`, `visible`, `background`

### Group Management Modes
- **Compact mode**: Creates only groups needed for existing tabs
- **Literal mode**: Creates exact group numbers specified

## Rule Matching

Rules match on combinations of:
- **Extensions**: `.py`, `.js`, `.css`, `.vue`
- **Directory patterns**: `*/src/*`, `*/tests/*`, `*/components/*`
- **File name patterns**: `*.test.*`, `*.spec.*`, `*.config.*`
- **Semantic states**: `dirty + project`, `external + stale`, `unsaved + short_lived`
- **Exclusion patterns**: Exclude specific combinations

## Configuration

### Template Library
Browse `Preferences → Package Settings → Jorn AutoPlace Tabs → Settings` for:
- Semantic file type definitions
- Layout templates
- Usage examples

### Project Setup
Add layout to `.sublime-project` file:

```json
{
    "folders": [{"path": "."}],
    "settings": {
        "jorn_auto_place_tabs": {
            "active_layout": "web_development",
            "web_development": {
                "layout": {
                    "cols": [0.0, 0.4, 0.7, 1.0],
                    "rows": [0.0, 0.6, 1.0],
                    "cells": [
                        [0, 0, 1, 2],  // Main code (tall)
                        [1, 0, 2, 1],  // Tests
                        [2, 0, 3, 1],  // Styles
                        [1, 1, 3, 2]   // Scratch/external
                    ]
                },
                "rules": {
                    "0": {
                        "name": "Active Development",
                        "match": {
                            "extensions": [".js", ".ts", ".vue"],
                            "types": ["project", "dirty"],
                            "directory_patterns": ["*/src/*"]
                        }
                    },
                    "1": {
                        "name": "Tests",
                        "match": {
                            "file_name_patterns": ["*.test.*", "*.spec.*"]
                        }
                    },
                    "2": {
                        "name": "Styles",
                        "match": {
                            "extensions": [".css", ".scss"]
                        }
                    },
                    "3": {
                        "name": "Temporary",
                        "match": {
                            "types": ["unsaved", "external", "scratch"]
                        }
                    }
                }
            }
        }
    }
}
```

## Usage

Files place automatically according to layout rules. Manual override commands available via Command Palette.

## Layout Examples

### Web Development
```json
"rules": {
    "0": {
        "match": {
            "extensions": [".js", ".ts", ".vue"],
            "types": ["project", "dirty"]
        }
    },
    "1": {
        "match": {
            "file_name_patterns": ["*.test.*", "*.spec.*"]
        }
    }
}
```

### Data Science
```json
"rules": {
    "0": {
        "match": {
            "extensions": [".py"],
            "directory_patterns": ["*/src/*"]
        }
    },
    "1": {
        "match": {
            "extensions": [".ipynb"]
        }
    }
}
```

## Commands

| Command | Function |
|---------|----------|
| `Jorn AutoPlace: Place Current Tab` | Place active tab |
| `Jorn AutoPlace: Place All Tabs` | Place all tabs |
| `Jorn AutoPlace: Toggle Auto-Placement` | Toggle automatic placement |
| `Jorn AutoPlace: Show Current Rules` | Display active rules |
| `Jorn AutoPlace: Reload Settings` | Reload configuration |

## Safety Features

- Rate limiting: 5 placements/second maximum
- Recursion guards prevent infinite loops
- Placement history prevents oscillation
- Graceful fallbacks for invalid configurations
