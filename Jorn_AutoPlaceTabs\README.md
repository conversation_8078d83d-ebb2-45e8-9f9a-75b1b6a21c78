# Jorn AutoPlace Tabs

Layout-centric tab placement for Sublime Text 4. Organizes tabs using semantic file state detection and project-specific layout definitions.

## Core Architecture

### Layout-Centric Design
- Layouts define physical structure, rules, and behavior settings.
- Rules are components of layouts, not standalone configurations.
- Each layout specifies a complete workspace.

### Configuration Approach
- Global settings file: Template library.
- Project settings (`.sublime-project`): Active layout configuration.
- Layouts are copied and customized per project.

### Semantic State Detection
File states detected: `project`, `external`, `dirty`, `unsaved`, `deleted`, `scratch`, `stale`, `recently_opened`, `short_lived`, `large_file`, `small_file`, `empty`, `readonly`, `active`, `visible`, `background`.

### Group Management Modes
- **Compact mode**: Creates groups only for existing tabs.
- **Literal mode**: Uses specified exact group numbers.

## Layout System Details

### Semantic File State Detection
The plugin identifies file states including:
- Lifecycle states: `unsaved`, `dirty`, `saved`, `scratch`, `deleted`, `missing`
- Location states: `project`, `external` (relative to project folders)
- Activity states: `active`, `recently_opened`, `stale`, `short_lived`
- Content states: `empty`, `non_empty`, `large_file`, `small_file`
- Visibility states: `visible`, `background`, `readonly`

### Layout Definitions
Each layout defines:
- Physical structure (cols, rows, cells): Workspace visual arrangement.
- Semantic rules: File placement based on state combinations.
- Behavior settings: Sorting, thresholds.
- Metadata: Name, description.

### Group Management
- Compact mode: Creates groups as needed for open tabs.
- Literal mode: Uses specified exact group numbers.
- Layout behavior adapts to usage patterns.
- Rules consider file lifecycle and project relationship.

### Rule Matching
Rules can match combinations of:
- File extensions: `.py`, `.js`, `.css`, `.vue`
- Directory patterns: `*/src/*`, `*/tests/*`, `*/components/*`
- File name patterns: `*.test.*`, `*.spec.*`, `*.config.*`
- Semantic states: `dirty + project`, `external + stale`, `unsaved + short_lived`
- Exclusion patterns

## Quick Start

1.  **Review Template Library:**
    Explore `Preferences → Package Settings → Jorn AutoPlace Tabs → Settings` for:
    - 17+ semantic file types explanations.
    - 3 layout templates for customization.
    - Project-specific configuration examples.

2.  **Configure Project:**
    Copy a layout template to your `.sublime-project` file and customize:

```json
{
    "folders": [{"path": "."}],
    "settings": {
        "jorn_auto_place_tabs": {
            "active_layout": "web_development",
            "web_development": {
                "layout": {
                    "cols": [0.0, 0.4, 0.7, 1.0],
                    "rows": [0.0, 0.6, 1.0],
                    "cells": [
                        [0, 0, 1, 2],  // Main code (tall)
                        [1, 0, 2, 1],  // Tests
                        [2, 0, 3, 1],  // Styles
                        [1, 1, 3, 2]   // Scratch/external
                    ]
                },
                "rules": {
                    "0": {
                        "name": "Active Development",
                        "match": {
                            "extensions": [".js", ".ts", ".vue"],
                            "types": ["project", "dirty"],
                            "directory_patterns": ["*/src/*"]
                        }
                    },
                    "1": {
                        "name": "Tests",
                        "match": {
                            "file_name_patterns": ["*.test.*", "*.spec.*"]
                        }
                    },
                    "2": {
                        "name": "Styles",
                        "match": {
                            "extensions": [".css", ".scss"]
                        }
                    },
                    "3": {
                        "name": "Temporary",
                        "match": {
                            "types": ["unsaved", "external", "scratch"]
                        }
                    }
                }
            }
        }
    }
}
```

3.  **Activate:**
    - Files place automatically based on the active layout's semantic rules.
    - Compact mode creates groups as needed.
    - Manual override commands are available.

## Commands

| Command | Function |
|---------|----------|
| `Jorn AutoPlace: Place Current Tab` | Place active tab |
| `Jorn AutoPlace: Place All Tabs` | Place all tabs |
| `Jorn AutoPlace: Toggle Auto-Placement` | Toggle automatic placement |
| `Jorn AutoPlace: Show Current Rules` | Display active rules |
| `Jorn AutoPlace: Reload Settings` | Reload configuration |

## Safety Features

- Rate limiting: 5 placements/second maximum
- Recursion guards prevent infinite loops
- Placement history prevents oscillation
- Graceful fallbacks for invalid configurations
