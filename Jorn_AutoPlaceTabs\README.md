# Jorn AutoPlace Tabs

**Intelligent tab placement for Sublime Text 4** - Automatically organize your tabs into logical groups based on file types, directories, and custom rules.

## ✨ Features

### 🎯 **Zero-Configuration Startup**
Works perfectly out of the box with sensible defaults. No setup required.

### 🏗️ **Flexible Layout System**
- **Predefined layouts** for 2, 3, 4+ groups
- **Auto-adjusting layouts** that adapt to your rules
- **Project-specific layouts** via `.sublime-project` files
- **Custom layout templates** for specialized workflows

### 🎛️ **Powerful Rule Engine**
- **File type rules** - Group by extensions (`.py`, `.js`, `.css`)
- **Directory rules** - Group by folder patterns (`*/src/*`, `*/tests/*`)
- **Semantic rules** - Group by file state (unsaved, dirty, project files)
- **Custom rules** - Advanced pattern matching with priorities
- **Exclude patterns** - Skip files you don't want auto-placed

### 🚀 **Smart Automation**
- **Automatic placement** on file open and tab activation
- **Rate limiting** prevents performance issues
- **Recursion guards** prevent infinite loops
- **Manual override** commands for full user control

### 🔧 **Project Integration**
- **Project-specific settings** override global defaults
- **Settings inheritance** (Global → Project → User)
- **Project folder detection** for membership rules
- **Settings caching** for optimal performance

## 🚀 Quick Start

### Basic Usage
1. Install the plugin
2. Open multiple files - they'll automatically organize into groups
3. Customize via `Preferences → Package Settings → Jorn AutoPlace Tabs`

### Example Configuration
```json
{
    "auto_place_on_load": true,
    "auto_place_on_activation": true,
    
    "file_type_rules": {
        "0": [".py", ".pyw"],           // Python files → Group 0
        "1": [".js", ".ts", ".jsx"],    // JavaScript → Group 1
        "2": [".html", ".css", ".vue"]  // Frontend → Group 2
    },
    
    "directory_rules": {
        "0": ["*/src/*", "*/lib/*"],    // Source code → Group 0
        "1": ["*/tests/*", "*/test/*"]  // Tests → Group 1
    }
}
```

## 📋 Commands

| Command | Description |
|---------|-------------|
| `Jorn AutoPlace: Place Current Tab` | Manually place the active tab |
| `Jorn AutoPlace: Place All Tabs` | Place all open tabs |
| `Jorn AutoPlace: Toggle Auto-Placement` | Enable/disable automatic placement |
| `Jorn AutoPlace: Show Current Rules` | Display active rules and settings |
| `Jorn AutoPlace: Reload Settings` | Refresh configuration |

## ⚙️ Configuration

### Global Settings
Configure via `Preferences → Package Settings → Jorn AutoPlace Tabs → Settings`

### Project-Specific Settings
Add to your `.sublime-project` file:
```json
{
    "folders": [{"path": "."}],
    "settings": {
        "jorn_auto_place_tabs": {
            "auto_place_on_activation": true,
            "file_type_rules": {
                "0": [".py", ".pyw"],
                "1": [".js", ".ts"]
            }
        }
    }
}
```

## 🎨 Layout Templates

### Web Development
```json
"layout_configs": {
    "4": {
        "cols": [0.0, 0.4, 0.7, 1.0],
        "rows": [0.0, 0.6, 1.0],
        "cells": [
            [0, 0, 1, 2],  // Main code (tall)
            [1, 0, 2, 1],  // Tests
            [2, 0, 3, 1],  // Styles
            [1, 1, 3, 2]   // Scratch/external
        ]
    }
}
```

## 🔍 Rule Types

### 1. File Type Rules
Match by file extension or name patterns:
```json
"file_type_rules": {
    "0": [".py", ".pyw", "*.python"],
    "1": ["*.test.js", "*.spec.ts"]
}
```

### 2. Directory Rules
Match by folder patterns (supports glob):
```json
"directory_rules": {
    "0": ["*/src/*", "*/lib/*"],
    "1": ["*/tests/*", "*/__tests__/*"]
}
```

### 3. Semantic Rules
Match by file state:
```json
"semantic_rules": {
    "0": ["project", "saved"],     // Project files that are saved
    "1": ["external"],             // Files outside project
    "2": ["unsaved", "dirty"]      // Unsaved or modified files
}
```

### 4. Custom Rules
Advanced pattern matching with priorities:
```json
"custom_rules": [
    {
        "name": "Test files",
        "pattern": "*test*.py",
        "group": 1,
        "priority": 20
    }
]
```

## 🛡️ Safety Features

- **Rate limiting** - Maximum 5 placements per second
- **Recursion guards** - Prevents infinite placement loops
- **Placement history** - Tracks recent placements to prevent oscillation
- **Error resilience** - Graceful fallbacks for invalid configurations

## 🎯 Design Philosophy

**Simplicity First** - Works perfectly with zero configuration
**Progressive Enhancement** - Advanced features available when needed
**User Control** - Automatic behavior with manual override capability
**Performance** - Optimized for large projects with many files
**Standards Compliance** - Follows all Sublime Text package conventions

## 📄 License

MIT License - See LICENSE file for details

## 🤝 Contributing

Contributions welcome! Please read CONTRIBUTING.md for guidelines.
