{
    // ============================================================================
    // JORN AUTO PLACE TABS - REFERENCE CONFIGURATION
    // ============================================================================
    // This file serves as a reference for all available settings.
    // For project-specific rules, copy relevant sections to your .sublime-project file
    // under "settings" -> "jorn_auto_place_tabs"
    // ============================================================================

    // CORE BEHAVIOR SETTINGS
    // ============================================================================

    // Enable automatic tab placement when files are opened
    "auto_place_on_load": false,

    // Enable automatic tab placement when tabs are activated
    "auto_place_on_activation": false,

    // Enable debug output to console (useful for testing rules)
    "enable_debug_prints": false,

    // How to sort tabs within each group: "append", "prepend", "alphabetical"
    "group_sort_method": "append",

    // Patterns to exclude from automatic placement
    "exclude_patterns": [
        // Examples - customize per project:
        // "*.tmp",
        // "*/temp/*",
        // "Untitled*"
    ],

    // LAYOUT MANAGEMENT
    // ============================================================================

    // Automatically create/adjust layout when placing tabs
    "auto_adjust_layout": false,

    // AVAILABLE OPTIONS REFERENCE
    // ============================================================================
    // Use these values in your project settings

    "layout_options": {
        "missing_group_behavior": {
            "skip": "Don't place tab if target group doesn't exist",
            "last_group": "Place in the rightmost existing group",
            "first_group": "Place in the leftmost existing group"
        },
        "layout_mode": {
            "compact": "Only create groups for tabs that actually exist (recommended)",
            "literal": "Create groups exactly as specified in rules"
        },
        "layout_type": {
            "columns": "Arrange groups as vertical columns (recommended)",
            "rows": "Arrange groups as horizontal rows",
            "grid": "Arrange groups in a grid pattern"
        }
    },

    // LAYOUT SETTINGS (defaults - override in project files)
    // ============================================================================

    "missing_group_behavior": "skip",
    "layout_mode": "compact",
    "layout_type": "columns",

    // CUSTOM LAYOUTS (optional)
    // ============================================================================
    // Define custom layouts for specific group counts

    "layout_configs": {
        // Example: Custom 3x3 grid layout for 8 groups
        // "8": {
        //     "cols": [0.0, 0.33, 0.66, 1.0],
        //     "rows": [0.0, 0.33, 0.66, 1.0],
        //     "cells": [
        //         [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],
        //         [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2],
        //         [0, 2, 1, 3], [1, 2, 2, 3]
        //     ]
        // }
    },

    // SEMANTIC TYPE SYSTEM
    // ============================================================================
    // Built-in semantic types (always available)

    "defined_types": {
        "unsaved":   "Tab has never been saved to disk",
        "dirty":     "Tab has unsaved changes",
        "project":   "File is inside a project folder",
        "external":  "File is outside all project folders",
        "scratch":   "Scratch buffer (not file-backed)",
        "readonly":  "Tab is read-only"
    },

    // RULE EXAMPLES (copy to your .sublime-project files)
    // ============================================================================
    // These are examples only - not active by default

    "group_rules_examples": {
        // BASIC EXAMPLES
        // ============================================================================

        // Simple file type separation
        "0": [
            {
                "description": "Python source files",
                "match": {
                    "extensions": [".py", ".pyw"]
                }
            }
        ],
        "1": [
            {
                "description": "JavaScript/TypeScript files",
                "match": {
                    "extensions": [".js", ".ts", ".jsx", ".tsx"]
                }
            }
        ],
        "2": [
            {
                "description": "Documentation files",
                "match": {
                    "extensions": [".md", ".txt", ".rst"]
                }
            }
        ],

        // ADVANCED EXAMPLES
        // ============================================================================

        // Complex rules with multiple conditions
        "10": [
            {
                "description": "Project Python source (not tests)",
                "match": {
                    "extensions": [".py", ".pyw"],
                    "directory_patterns": ["*/src/*", "*/lib/*", "*/app/*"],
                    "types": ["project"]
                },
                "exclude": {
                    "file_name_patterns": ["test_*.py", "__init__.py"],
                    "directory_patterns": ["*/tests/*", "*/test/*"]
                }
            }
        ],
        "11": [
            {
                "description": "All test files",
                "match": {
                    "file_name_patterns": ["test_*.py", "*.test.js", "*.spec.ts"],
                    "directory_patterns": ["*/tests/*", "*/test/*", "*/__tests__/*"],
                    "types": ["project"]
                }
            }
        ],
        "12": [
            {
                "description": "Configuration and data files",
                "match": {
                    "extensions": [".json", ".yaml", ".yml", ".toml", ".ini"],
                    "file_name_patterns": ["*.config.*", ".*rc", "Dockerfile*"]
                }
            }
        ],
        "13": [
            {
                "description": "External dirty files (not unsaved)",
                "match": {
                    "types": ["external", "dirty"]
                },
                "exclude": {
                    "types": ["unsaved"]
                }
            }
        ],
        "14": [
            {
                "description": "Temporary and unsaved files",
                "match": {
                    "types": ["unsaved", "scratch"]
                }
            }
        ]
    },

    // ACTUAL RULES (empty by default - configure in .sublime-project files)
    // ============================================================================
    // Copy examples above to your project settings to activate rules

    "group_rules": {
        // No global rules by default
        // Configure rules in your .sublime-project files under:
        // "settings" -> "jorn_auto_place_tabs" -> "group_rules"
    }
}
