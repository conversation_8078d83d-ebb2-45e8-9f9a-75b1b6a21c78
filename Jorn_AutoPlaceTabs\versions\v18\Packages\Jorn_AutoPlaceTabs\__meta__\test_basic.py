"""
Basic test script for Jorn_AutoPlaceTabs plugin.
This can be run in Sublime Text's console to verify basic functionality.
"""

import sys
import os

# Add the plugin directory to the path
plugin_dir = os.path.dirname(os.path.dirname(__file__))
if plugin_dir not in sys.path:
    sys.path.insert(0, plugin_dir)

try:
    # Import the main plugin module
    import Jorn_AutoPlaceTabs
    
    print("✓ Plugin module imported successfully")
    
    # Check if main classes exist
    if hasattr(<PERSON>rn_AutoPlaceTabs, 'Jorn_AutoPlaceTabsCommand'):
        print("✓ Main EventListener class found")
    else:
        print("✗ Main EventListener class not found")
    
    if hasattr(Jorn_AutoPlaceTabs, 'JornAutoPlaceTabsManualCommand'):
        print("✓ Manual command class found")
    else:
        print("✗ Manual command class not found")
    
    # Check plugin constants
    if hasattr(Jorn_AutoPlaceTabs, 'PLUGIN_NAME'):
        print(f"✓ Plugin name: {Jorn_AutoPlaceTabs.PLUGIN_NAME}")
    else:
        print("✗ PLUGIN_NAME constant not found")
    
    print("\nBasic plugin structure verification complete.")
    
except ImportError as e:
    print(f"✗ Failed to import plugin: {e}")
except Exception as e:
    print(f"✗ Unexpected error: {e}")

# Test settings loading
try:
    import sublime
    settings = sublime.load_settings("Jorn_AutoPlaceTabs.sublime-settings")
    if settings:
        print("✓ Settings file loaded successfully")
        
        # Check key settings
        auto_place = settings.get("auto_place_on_activation", None)
        if auto_place is not None:
            print(f"✓ auto_place_on_activation: {auto_place}")
        
        file_rules = settings.get("file_type_rules", {})
        if file_rules:
            print(f"✓ File type rules configured: {len(file_rules)} groups")
    else:
        print("✗ Failed to load settings")
        
except Exception as e:
    print(f"✗ Settings test failed: {e}")

print("\nTo run this test:")
print("1. Open Sublime Text")
print("2. Open the console (Ctrl+`)")
print("3. Run: exec(open(r'Packages/Jorn_AutoPlaceTabs/__meta__/test_basic.py').read())")
