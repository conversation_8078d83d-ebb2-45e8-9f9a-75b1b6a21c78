{
    "folders": [
        {
            "path": ".",
            "name": "My Project"
        }
    ],
    "settings": {
        "jorn_auto_place_tabs": {
            // Enable the plugin for this project
            "auto_place_on_activation": true,
            "auto_adjust_layout": true,
            "layout_mode": "compact",
            "layout_type": "columns",
            
            // Optional: Enable debug output while setting up rules
            "enable_debug_prints": true,
            
            // Project-specific rules (copy from main settings reference)
            "group_rules": {
                "0": [
                    {
                        "description": "Project Python source files",
                        "match": {
                            "extensions": [".py", ".pyw"],
                            "directory_patterns": ["*/src/*", "*/lib/*"],
                            "types": ["project"]
                        },
                        "exclude": {
                            "file_name_patterns": ["test_*.py", "__init__.py"]
                        }
                    }
                ],
                "1": [
                    {
                        "description": "Test files",
                        "match": {
                            "file_name_patterns": ["test_*.py", "*.test.js", "*.spec.ts"],
                            "directory_patterns": ["*/tests/*", "*/test/*"],
                            "types": ["project"]
                        }
                    }
                ],
                "2": [
                    {
                        "description": "Documentation",
                        "match": {
                            "extensions": [".md", ".txt", ".rst"],
                            "directory_patterns": ["*/docs/*"],
                            "types": ["project"]
                        }
                    }
                ],
                "3": [
                    {
                        "description": "Configuration files",
                        "match": {
                            "extensions": [".json", ".yaml", ".yml", ".toml"],
                            "file_name_patterns": ["*.config.*", ".*rc"]
                        }
                    }
                ],
                "4": [
                    {
                        "description": "External files",
                        "match": {
                            "types": ["external"]
                        }
                    }
                ],
                "5": [
                    {
                        "description": "Unsaved files",
                        "match": {
                            "types": ["unsaved", "scratch"]
                        }
                    }
                ]
            }
        }
    }
}
