# Jorn_AutoPlaceTabs

A Sublime Text 4 plugin that automatically places tabs in appropriate groups based on generalizable criteria such as file type, directory patterns, and project membership.

## Features

- **Automatic Tab Placement**: Tabs are automatically moved to appropriate groups based on configurable rules
- **File Type Rules**: Place tabs based on file extensions and patterns
- **Directory Rules**: Organize tabs by directory structure using glob patterns
- **Project Awareness**: Separate project files from external files
- **Rate Limiting**: Prevents infinite loops and excessive operations
- **Manual Controls**: Commands for manual placement and rule management
- **Flexible Configuration**: Extensive settings for customization

## Installation

1. Copy the `Jorn_AutoPlaceTabs` folder to your Sublime Text `Packages` directory
2. Restart Sublime Text or reload the plugin

## Usage

### Automatic Placement

The plugin automatically places tabs when:
- A tab is activated (if `auto_place_on_activation` is enabled)
- A file is loaded (if `auto_place_on_load` is enabled)

### Manual Commands

- **Place Current Tab**: `Ctrl+Alt+P` - Place the current tab according to rules
- **Place All Tabs**: `Ctrl+Alt+Shift+P` - Place all open tabs according to rules
- **Toggle Auto-Placement**: `Ctrl+Alt+T` - Enable/disable automatic placement

### Command Palette

- `Jorn AutoPlace: Place Current Tab`
- `Jorn AutoPlace: Place All Tabs`
- `Jorn AutoPlace: Toggle Auto-Placement`
- `Jorn AutoPlace: Show Current Rules`
- `Jorn AutoPlace: Reload Settings`

## Configuration

### Basic Settings

```json
{
    "auto_place_on_activation": true,
    "auto_place_on_load": true,
    "enable_debug_prints": false,
    "group_sort_method": "append"
}
```

### File Type Rules

Map file extensions to group indices:

```json
{
    "file_type_rules": {
        "0": [".py", ".pyw"],
        "1": [".js", ".ts", ".jsx", ".tsx"],
        "2": [".html", ".css", ".scss"],
        "3": [".md", ".txt", ".rst"]
    }
}
```

### Directory Rules

Use glob patterns to match directory structures:

```json
{
    "directory_rules": {
        "0": ["*/src/*", "*/lib/*"],
        "1": ["*/tests/*", "*/test/*"],
        "2": ["*/docs/*"],
        "3": ["*/config/*"]
    }
}
```

### Special Groups

```json
{
    "project_files_group": 0,
    "external_files_group": 1,
    "unsaved_files_group": 2
}
```

### Exclude Patterns

Prevent certain files from being auto-placed:

```json
{
    "exclude_patterns": [
        "*.tmp",
        "*/.git/*",
        "*/node_modules/*"
    ]
}
```

## Architecture

The plugin follows established patterns from the Jorn plugin ecosystem:

- **EventListener Pattern**: Uses `sublime_plugin.EventListener` for automatic behavior
- **Command Pattern**: Provides `WindowCommand` classes for manual operations
- **Settings-Driven**: All behavior controlled through `.sublime-settings` files
- **Rate Limiting**: Prevents excessive operations with frequency controls
- **Debug Support**: Configurable debug output for troubleshooting

## Integration

This plugin is designed to work alongside other Jorn tab management plugins:
- `Jorn_AutosortTabs` - For tab sorting within groups
- `Jorn_TabUtils` - For general tab utilities
- `Jorn_OrganizeViewsByDirectory` - For directory-based organization
- `Jorn_SortTabs` - For advanced tab sorting

## Development

The plugin maintains consistency with the established Jorn plugin patterns:
- Consistent naming conventions
- Shared architectural patterns
- Compatible settings structure
- Unified user experience
