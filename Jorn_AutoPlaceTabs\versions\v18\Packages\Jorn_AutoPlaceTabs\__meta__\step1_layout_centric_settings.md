# Step 1: Layout-Centric Settings Structure

## What We've Accomplished ✅

### 1. **Transformed Main Settings File**
- **From**: Rule-centric with active global rules
- **To**: Layout-centric reference documentation

### 2. **New Structure Overview**

```
Jorn_AutoPlaceTabs.sublime-settings
├── Global Settings (minimal defaults)
├── Expanded Semantic Types (comprehensive reference)
├── Layout Examples (complete layout definitions)
│   ├── web_development
│   ├── python_data_science  
│   └── cleanup_mode
└── Empty Active Settings (no global layouts)
```

### 3. **Key Innovations**

#### **Expanded Semantic Types**
- **Basic states**: unsaved, dirty, saved, scratch, readonly
- **Location states**: project, external
- **Existence states**: deleted, missing, exists
- **Content states**: empty, non_empty
- **Activity states**: active, inactive, visible, background
- **Time-based states**: recently_opened, short_lived, inactive_file, stale
- **Size-based states**: large_file, small_file
- **Syntax states**: has_syntax, plain_text

#### **Layout-Centric Structure**
Each layout is a complete unit containing:
```json
{
    "layout_name": {
        "metadata": {
            "name": "Human-readable name",
            "description": "Purpose and use case",
            "author": "Creator",
            "tags": ["categorization"]
        },
        "layout": {
            "cols": [...],
            "rows": [...], 
            "cells": [...]  // Sublime layout definition
        },
        "rules": {
            "0": {
                "name": "Group name",
                "description": "What goes here",
                "match": { /* conditions */ },
                "exclude": { /* exclusions */ }
            }
        },
        "settings": {
            "recently_opened_threshold": 300,
            "large_file_threshold": 1048576,
            "group_sort_method": "alphabetical"
        }
    }
}
```

### 4. **Three Complete Layout Examples**

#### **Web Development Layout**
- **Purpose**: Frontend development workflow
- **Groups**: Main code (tall), Tests, Styles, Temp/scratch, External/problematic
- **Features**: State-aware organization, size-based thresholds

#### **Python Data Science Layout**  
- **Purpose**: ML and data analysis projects
- **Groups**: Source code (large), Notebooks, Tests, Data files
- **Features**: Optimized for data science workflow

#### **Cleanup Mode Layout**
- **Purpose**: Tab cleanup and organization
- **Groups**: Clean files, Dirty files, Deleted/missing, External, Empty/scratch
- **Features**: Problem-focused organization

### 5. **Project Template**
- Shows how to copy layouts from reference to project files
- Demonstrates `_settings` section for active layout selection
- Includes project-specific customization

## Benefits Achieved

### 1. **Reference-Driven**
- Main settings file is pure documentation
- All options visible with descriptions
- Easy to discover capabilities

### 2. **Self-Contained Layouts**
- Each layout is complete and portable
- No orphaned rules or layouts
- Easy to share between projects

### 3. **Rich Semantic System**
- 17 different semantic types
- Time and size-based intelligence
- Activity and state awareness

### 4. **Project-Specific**
- No global interference
- Each project optimized for its workflow
- Easy switching between layout modes

## Usage Workflow

### **For Users**:
1. **Browse reference**: Open main settings to see all options
2. **Copy layout**: Choose layout example that fits workflow
3. **Paste to project**: Add to `.sublime-project` file
4. **Customize**: Adjust rules and thresholds for project
5. **Activate**: Set as active layout in `_settings`

### **For Developers** (Next Steps):
1. **Parse new structure**: Update plugin to read layout definitions
2. **Implement semantic detection**: Add expanded type detection
3. **Layout management**: Add switching and application logic
4. **Migration**: Convert old format to new format

## File Changes

### **Modified**:
- `Jorn_AutoPlaceTabs.sublime-settings` - Complete restructure
- `project_template_semantic.sublime-project` - Updated example

### **Created**:
- `step1_layout_centric_settings.md` - This documentation

## Next Steps Preview

**Step 2** will focus on:
- Creating `LayoutDefinition` class
- Implementing `SemanticTypeDetector` 
- Refactoring main plugin class to use layout-centric approach

The foundation is now solid for the layout-centric architecture! 🎯
