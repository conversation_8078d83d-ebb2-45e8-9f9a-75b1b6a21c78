# Layout Behavior Options

## Overview

The plugin now provides fine-grained control over how it handles situations where the target group for a tab doesn't exist in the current layout.

## Settings

### `auto_adjust_layout` (boolean)
- **`true`**: Automatically create new groups as needed
- **`false`**: Respect the existing layout (recommended)

### `missing_group_behavior` (string)
Only applies when `auto_adjust_layout` is `false`. Options:

- **`"skip"`**: Don't place the tab if target group doesn't exist (default)
- **`"last_group"`**: Place in the rightmost existing group
- **`"first_group"`**: Place in the leftmost existing group

## Behavior Matrix

| auto_adjust_layout | missing_group_behavior | Result when target group doesn't exist |
|-------------------|------------------------|----------------------------------------|
| `true` | (ignored) | Creates new groups up to `max_groups` |
| `false` | `"skip"` | Tab stays in current group |
| `false` | `"last_group"` | Tab moves to rightmost existing group |
| `false` | `"first_group"` | Tab moves to leftmost existing group |

## Use Cases

### Scenario 1: Respect Existing Layout (Recommended)
**Problem**: You have a carefully arranged 2-group layout, but rules reference groups 0-3. You don't want the plugin to mess up your layout.

**Solution**:
```json
{
    "auto_adjust_layout": false,
    "missing_group_behavior": "skip"
}
```

**Result**: Tabs only get placed if their target group exists. Your layout stays intact.

### Scenario 2: Fallback to Available Groups
**Problem**: You want organization but within your existing layout constraints.

**Solution**:
```json
{
    "auto_adjust_layout": false,
    "missing_group_behavior": "last_group"
}
```

**Result**: Tabs get placed in the rightmost group if their target doesn't exist.

### Scenario 3: Dynamic Layout Creation
**Problem**: You want maximum organization and don't mind layout changes.

**Solution**:
```json
{
    "auto_adjust_layout": true,
    "max_groups": 4
}
```

**Result**: Plugin creates groups as needed up to the maximum.

## Examples

### Web Development with 2-Group Layout
You prefer a simple 2-group layout (code | preview) but have rules for 4 groups:

```json
{
    "auto_adjust_layout": false,
    "missing_group_behavior": "skip",
    "file_type_rules": {
        "0": [".js", ".ts", ".vue"],     // Would go to group 0 if it exists
        "1": [".css", ".scss"],          // Would go to group 1 if it exists  
        "2": [".html"],                  // Skipped (group 2 doesn't exist)
        "3": [".md", ".txt"]             // Skipped (group 3 doesn't exist)
    }
}
```

### Multi-Project Workflow
You work on different projects with different layouts:

```json
{
    "auto_adjust_layout": false,
    "missing_group_behavior": "last_group",
    "file_type_rules": {
        "0": [".py"],
        "1": [".js"], 
        "2": [".css"],
        "3": [".md"]
    }
}
```

- 2-group layout: Python in group 0, everything else in group 1
- 4-group layout: Each file type in its designated group
- 3-group layout: Python in group 0, JS in group 1, CSS and MD in group 2

## Debug Output

Enable `"enable_debug_prints": true` to see placement decisions:

```
[Jorn_AutoPlaceTabs] Target group 3 doesn't exist (only 2 groups), skipping
[Jorn_AutoPlaceTabs] Target group doesn't exist, using last group (1)
[Jorn_AutoPlaceTabs] Moving tab from group 0 to group 1, index 2
```

## Migration from Previous Behavior

**Previous behavior**: Always created groups if they didn't exist
**New default**: Respects existing layout (`auto_adjust_layout: false`, `missing_group_behavior: "skip"`)

To restore previous behavior:
```json
{
    "auto_adjust_layout": true
}
```
