{
    "folders": [
        {
            "path": ".",
            "name": "My Sublime Packages"
        }
    ],
    "settings": {
        "jorn_auto_place_tabs": {
            // Enable automatic placement and layout adjustment
            "auto_place_on_activation": true,
            "auto_adjust_layout": true,
            "layout_mode": "compact",
            "layout_type": "grid",
            
            // SEMANTIC RULE SYSTEM
            "group_rules": {
                "0": [
                    {
                        "description": "Project Python files",
                        "match": {
                            "extensions": [".py", ".pyw"],
                            "types": ["project"]
                        }
                    },
                    {
                        "description": "Project source files in src/lib directories",
                        "match": {
                            "directory_patterns": ["*/src/*", "*/lib/*"],
                            "types": ["project"]
                        }
                    }
                ],
                "1": [
                    {
                        "description": "Project JavaScript/TypeScript files",
                        "match": {
                            "extensions": [".js", ".ts", ".jsx", ".tsx"],
                            "types": ["project"]
                        }
                    },
                    {
                        "description": "Project test files",
                        "match": {
                            "directory_patterns": ["*/tests/*", "*/test/*"],
                            "types": ["project"]
                        }
                    }
                ],
                "2": [
                    {
                        "description": "Project HTML/CSS/Vue files",
                        "match": {
                            "extensions": [".html", ".css", ".vue"],
                            "types": ["project"]
                        }
                    },
                    {
                        "description": "Project documentation",
                        "match": {
                            "directory_patterns": ["*/docs/*"],
                            "types": ["project"]
                        }
                    },
                    {
                        "description": "External files",
                        "match": {
                            "types": ["external"]
                        }
                    }
                ],
                "3": [
                    {
                        "description": "Project config files",
                        "match": {
                            "directory_patterns": ["*/config/*"],
                            "types": ["project"]
                        }
                    }
                ],
                "7": [
                    {
                        "description": "Documentation and data files (any location)",
                        "match": {
                            "extensions": [".md", ".txt", ".json"]
                        }
                    }
                ]
            }
        }
    }
}
