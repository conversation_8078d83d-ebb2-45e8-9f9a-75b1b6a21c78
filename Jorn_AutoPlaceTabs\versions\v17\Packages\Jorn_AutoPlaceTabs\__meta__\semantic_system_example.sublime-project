{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        "jorn_auto_place_tabs": {
            // Enable automatic layout adjustment
            "auto_adjust_layout": true,
            "layout_mode": "compact",
            "layout_type": "grid",
            
            // NEW SEMANTIC RULE SYSTEM
            "defined_types": {
                "unsaved":   "Tab has never been saved to disk",
                "dirty":     "Tab has unsaved changes",
                "project":   "File is inside a project folder", 
                "external":  "File is outside all project folders",
                "scratch":   "Scratch buffer (not file-backed)",
                "readonly":  "Tab is read-only"
            },
            
            "group_rules": {
                "0": [
                    {
                        "description": "Project Python source (not tests/docs/init)",
                        "match": {
                            "extensions": [".py", ".pyw"],
                            "directory_patterns": ["*/src/*", "*/lib/*"],
                            "types": ["project"]
                        },
                        "exclude": {
                            "file_name_patterns": ["test_*.py", "__init__.py"],
                            "directory_patterns": ["*/tests/*", "*/docs/*"]
                        }
                    }
                ],
                "1": [
                    {
                        "description": "Project JavaScript/TypeScript source",
                        "match": {
                            "extensions": [".js", ".ts", ".jsx", ".tsx"],
                            "types": ["project"]
                        },
                        "exclude": {
                            "file_name_patterns": ["*.test.*", "*.spec.*"]
                        }
                    }
                ],
                "2": [
                    {
                        "description": "Project Python test files",
                        "match": {
                            "file_name_patterns": ["test_*.py"],
                            "types": ["project"]
                        }
                    },
                    {
                        "description": "Project JS/TS test files", 
                        "match": {
                            "file_name_patterns": ["*.test.ts", "*.spec.js", "*.test.jsx"],
                            "types": ["project"]
                        }
                    }
                ],
                "3": [
                    {
                        "description": "Project documentation and config",
                        "match": {
                            "extensions": [".md", ".txt", ".json", ".yaml", ".yml"],
                            "types": ["project"]
                        }
                    }
                ],
                "4": [
                    {
                        "description": "External dirty files (but not unsaved)",
                        "match": { "types": ["external", "dirty"] },
                        "exclude": { "types": ["unsaved"] }
                    },
                    {
                        "description": "External markdown files",
                        "match": {
                            "extensions": [".md"],
                            "types": ["external"]
                        }
                    }
                ],
                "5": [
                    {
                        "description": "Unsaved buffers",
                        "match": { "types": ["unsaved"] }
                    },
                    {
                        "description": "Scratch buffers",
                        "match": { "types": ["scratch"] }
                    },
                    {
                        "description": "Read-only files",
                        "match": { "types": ["readonly"] }
                    }
                ]
            }
        }
    }
}
