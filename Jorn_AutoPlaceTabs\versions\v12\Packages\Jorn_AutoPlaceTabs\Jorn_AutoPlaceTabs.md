# Jorn_AutoPlaceTabs

A Sublime Text 4 plugin that automatically places tabs in appropriate groups based on generalizable criteria such as file type, directory patterns, and project membership.

## Features

- **Automatic Tab Placement**: Tabs are automatically moved to appropriate groups based on configurable rules
- **File Type Rules**: Place tabs based on file extensions and patterns
- **Directory Rules**: Organize tabs by directory structure using glob patterns
- **Project Awareness**: Separate project files from external files
- **Rate Limiting**: Prevents infinite loops and excessive operations
- **Manual Controls**: Commands for manual placement and rule management
- **Flexible Configuration**: Extensive settings for customization

## Installation

1. Copy the `Jorn_AutoPlaceTabs` folder to your Sublime Text `Packages` directory
2. Restart Sublime Text or reload the plugin

## Usage

### Automatic Placement

The plugin automatically places tabs when:
- A tab is activated (if `auto_place_on_activation` is enabled)
- A file is loaded (if `auto_place_on_load` is enabled)

### Manual Commands

- **Place Current Tab**: `Ctrl+Alt+P` - Place the current tab according to rules
- **Place All Tabs**: `Ctrl+Alt+Shift+P` - Place all open tabs according to rules
- **Toggle Auto-Placement**: `Ctrl+Alt+T` - Enable/disable automatic placement

### Command Palette

- `Jorn AutoPlace: Place Current Tab`
- `Jorn AutoPlace: Place All Tabs`
- `Jorn AutoPlace: Toggle Auto-Placement`
- `Jorn AutoPlace: Show Current Rules`
- `Jorn AutoPlace: Reload Settings`

## Configuration

The plugin supports both global and project-specific settings. Global settings are defined in `Jorn_AutoPlaceTabs.sublime-settings`, while project-specific settings can be added to your `.sublime-project` file.

### Project-Specific Settings (Recommended)

For maximum flexibility, add a `"jorn_auto_place_tabs"` section to your project file:

```json
{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        "jorn_auto_place_tabs": {
            "auto_place_on_activation": true,
            "file_type_rules": {
                "0": [".py", ".pyw"],
                "1": [".js", ".ts", ".jsx", ".tsx"],
                "2": [".html", ".css", ".vue"],
                "3": [".md", ".txt", ".json"]
            },
            "directory_rules": {
                "0": ["*/src/*", "*/lib/*"],
                "1": ["*/tests/*", "*/test/*"],
                "2": ["*/docs/*"],
                "3": ["*/config/*"]
            },
            "project_files_group": 0,
            "external_files_group": 2,
            "auto_adjust_layout": true,
            "max_groups": 4
        }
    }
}
```

Use the command `Jorn AutoPlace: Create Project Settings` to generate a template.

### Global Settings

Basic global settings in `Jorn_AutoPlaceTabs.sublime-settings`:

```json
{
    "auto_place_on_activation": true,
    "auto_place_on_load": true,
    "enable_debug_prints": false,
    "group_sort_method": "append"
}
```

### File Type Rules

Map file extensions to group indices:

```json
{
    "file_type_rules": {
        "0": [".py", ".pyw"],
        "1": [".js", ".ts", ".jsx", ".tsx"],
        "2": [".html", ".css", ".scss"],
        "3": [".md", ".txt", ".rst"]
    }
}
```

### Directory Rules

Use glob patterns to match directory structures:

```json
{
    "directory_rules": {
        "0": ["*/src/*", "*/lib/*"],
        "1": ["*/tests/*", "*/test/*"],
        "2": ["*/docs/*"],
        "3": ["*/config/*"]
    }
}
```

### Special Groups

```json
{
    "project_files_group": 0,
    "external_files_group": 1,
    "unsaved_files_group": 2
}
```

### Layout Management

Control how the plugin handles missing groups:

```json
{
    "auto_adjust_layout": false,
    "missing_group_behavior": "skip"
}
```

**Layout Options:**
- `auto_adjust_layout`: `true` = create groups as needed, `false` = respect existing layout
- `missing_group_behavior`: What to do when target group doesn't exist:
  - `"skip"`: Don't place the tab (respects existing layout)
  - `"last_group"`: Place in the rightmost existing group
  - `"first_group"`: Place in the leftmost existing group

### Exclude Patterns

Prevent certain files from being auto-placed:

```json
{
    "exclude_patterns": [
        "*.tmp",
        "*/.git/*",
        "*/node_modules/*"
    ]
}
```

## Architecture

The plugin follows established patterns from the Jorn plugin ecosystem:

- **EventListener Pattern**: Uses `sublime_plugin.EventListener` for automatic behavior
- **Command Pattern**: Provides `WindowCommand` classes for manual operations
- **Settings-Driven**: All behavior controlled through `.sublime-settings` files
- **Rate Limiting**: Prevents excessive operations with frequency controls
- **Debug Support**: Configurable debug output for troubleshooting

## Integration

This plugin is designed to work alongside other Jorn tab management plugins:
- `Jorn_AutosortTabs` - For tab sorting within groups
- `Jorn_TabUtils` - For general tab utilities
- `Jorn_OrganizeViewsByDirectory` - For directory-based organization
- `Jorn_SortTabs` - For advanced tab sorting

## Development

The plugin maintains consistency with the established Jorn plugin patterns:
- Consistent naming conventions
- Shared architectural patterns
- Compatible settings structure
- Unified user experience
