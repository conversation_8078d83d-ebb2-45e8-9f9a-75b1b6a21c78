{
    // ============================================================================
    // JORN AUTO PLACE TABS - LAYOUT TEMPLATE LIBRARY
    // ============================================================================
    // Reference and template library. Copy layouts to .sublime-project files.
    // ============================================================================

    // ============================================================================
    // GLOBAL SETTINGS
    // ============================================================================
    "auto_place_on_load": false,
    "auto_place_on_activation": false,
    "enable_debug_prints": false,
    "exclude_patterns": [],

    // ============================================================================
    // SEMANTIC TYPES REFERENCE
    // ============================================================================
    "defined_types": {
        // Basic states
        "unsaved": "Tab has never been saved to disk",
        "dirty": "Tab has unsaved changes",
        "saved": "Tab is saved (not dirty)",
        "scratch": "Scratch buffer (not file-backed)",
        "readonly": "Tab is read-only",

        // Location states
        "project": "File is inside a project folder",
        "external": "File is outside all project folders",

        // File existence states
        "deleted": "File no longer exists on disk",
        "missing": "File path exists but file is missing",
        "exists": "File exists on disk",

        // Content states
        "empty": "Tab has no content or only whitespace",
        "non_empty": "Tab has meaningful content",

        // Activity states
        "active": "Currently active/focused tab",
        "inactive": "Not the currently active tab",
        "visible": "Tab is visible in current group",
        "background": "Tab is in a background group",

        // Time-based states
        "recently_opened": "Opened within recent time threshold",
        "short_lived": "Open for less than short_lived_threshold",
        "inactive_file": "File not modified recently (based on file system)",
        "stale": "Tab hasn't been focused for a long time",

        // Size-based states
        "large_file": "File size exceeds large_file_threshold",
        "small_file": "File size below small_file_threshold"
    },

    // ============================================================================
    // LAYOUT TEMPLATE LIBRARY
    // ============================================================================
    // Copy to .sublime-project files and customize

    "web_development": {
        "metadata": {
            "name": "Web Development Layout",
            "description": "Frontend development layout with state-aware organization"
        },
        "layout": {
            "cols": [0.0, 0.4, 0.7, 1.0],
            "rows": [0.0, 0.6, 1.0],
            "cells": [
                [0, 0, 1, 2],  // Group 0: Main code (tall)
                [1, 0, 2, 1],  // Group 1: Tests
                [2, 0, 3, 1],  // Group 2: Styles
                [1, 1, 2, 2],  // Group 3: Temp/scratch
                [2, 1, 3, 2]   // Group 4: External/deleted
            ]
        },
        "rules": {
            "0": {
                "name": "Development",
                "match": {
                    "extensions": [".js", ".ts", ".jsx", ".tsx", ".vue"],
                    "types": ["project", "exists"],
                    "directory_patterns": ["*/src/*", "*/components/*"]
                },
                "exclude": {
                    "types": ["deleted", "empty"],
                    "file_name_patterns": ["*.test.*", "*.spec.*"]
                }
            },
            "1": {
                "name": "Tests",
                "match": {
                    "file_name_patterns": ["*.test.*", "*.spec.*"],
                    "directory_patterns": ["*/tests/*", "*/__tests__/*"],
                    "types": ["exists"]
                }
            },
            "2": {
                "name": "Styles",
                "match": {
                    "extensions": [".css", ".scss", ".sass", ".less"],
                    "types": ["project", "exists"]
                }
            },
            "3": {
                "name": "Temporary",
                "match": {
                    "types": ["unsaved", "scratch", "empty", "short_lived"]
                }
            },
            "4": {
                "name": "External",
                "match": {
                    "types": ["external", "deleted", "stale"]
                }
            }
        },
        "settings": {
            "layout_mode": "compact",
            "recently_opened_threshold": 300,
            "short_lived_threshold": 10,
            "stale_threshold": 3600,
            "group_sort_method": "alphabetical"
        }
    },

    "python_data_science": {
        "metadata": {
            "name": "Python Data Science",
            "description": "Layout for ML and data projects"
        },
        "layout": {
            "cols": [0.0, 0.6, 1.0],
            "rows": [0.0, 0.5, 1.0],
            "cells": [
                [0, 0, 1, 1],  // Group 0: Source (large)
                [1, 0, 2, 1],  // Group 1: Notebooks
                [0, 1, 1, 2],  // Group 2: Tests
                [1, 1, 2, 2]   // Group 3: Data
            ]
        },
        "rules": {
            "0": {
                "name": "Source Code",
                "match": {
                    "extensions": [".py"],
                    "directory_patterns": ["*/src/*", "*/lib/*"],
                    "types": ["project"]
                },
                "exclude": {
                    "file_name_patterns": ["test_*"]
                }
            },
            "1": {
                "name": "Notebooks",
                "match": {
                    "extensions": [".ipynb"]
                }
            },
            "2": {
                "name": "Tests",
                "match": {
                    "file_name_patterns": ["test_*", "*_test.py"],
                    "directory_patterns": ["*/tests/*"]
                }
            },
            "3": {
                "name": "Data Files",
                "match": {
                    "extensions": [".csv", ".json", ".parquet", ".pkl"],
                    "directory_patterns": ["*/data/*", "*/datasets/*"]
                }
            }
        },
        "settings": {
            "layout_mode": "compact",
            "group_sort_method": "append"
        }
    },

    "simple_columns": {
        "metadata": {
            "name": "Simple 3 Columns",
            "description": "Basic three-column layout"
        },
        "layout": {
            "cols": [0.0, 0.33, 0.66, 1.0],
            "rows": [0.0, 1.0],
            "cells": [
                [0, 0, 1, 1],  // Group 0
                [1, 0, 2, 1],  // Group 1
                [2, 0, 3, 1]   // Group 2
            ]
        },
        "rules": {
            "0": {
                "name": "Source Files",
                "match": {
                    "extensions": [".py", ".js", ".ts"],
                    "types": ["project"]
                }
            },
            "1": {
                "name": "Documentation",
                "match": {
                    "extensions": [".md", ".txt"]
                }
            },
            "2": {
                "name": "External Files",
                "match": {
                    "types": ["external"]
                }
            }
        },
        "settings": {
            "layout_mode": "literal",
            "group_sort_method": "append"
        }
    }

    // ============================================================================
    // PROJECT USAGE EXAMPLE
    // ============================================================================
    // Add this to your .sublime-project file:
    //
    // {
    //     "folders": [{"path": "."}],
    //     "settings": {
    //         "jorn_auto_place_tabs": {
    //             "auto_place_on_activation": true,
    //             "active_layout": "web_development",
    //             "web_development": {
    //                 // Copy and customize any layout from above
    //             }
    //         }
    //     }
    // }
}
