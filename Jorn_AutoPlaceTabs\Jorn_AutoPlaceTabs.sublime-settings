{
    // ============================================================================
    // JORN AUTO PLACE TABS - CONFIGURATION
    // ============================================================================
    // Intelligent tab placement for Sublime Text 4
    // Automatically organize tabs into logical groups based on rules and layouts
    // ============================================================================

    // ============================================================================
    // CORE BEHAVIOR
    // ============================================================================
    
    // Enable automatic tab placement when files are opened
    "auto_place_on_load": true,
    
    // Enable automatic tab placement when tabs are activated
    "auto_place_on_activation": true,
    
    // Enable debug output to console (for troubleshooting)
    "enable_debug_prints": false,
    
    // How to sort tabs within each group: "append", "prepend", "alphabetical"
    "group_sort_method": "append",

    // ============================================================================
    // RULE ENGINE - PRIORITY ORDER
    // ============================================================================
    // Rules are evaluated in this order:
    // 1. Semantic rules (file state)
    // 2. Custom rules (priority-based)
    // 3. File type rules (extensions)
    // 4. Directory rules (paths)
    // 5. Project membership rules
    // ============================================================================

    // FILE TYPE RULES - Group by file extensions and patterns
    // Map group indices (0, 1, 2...) to file patterns
    "file_type_rules": {
        "0": [".py", ".pyw", "*.python"],                    // Python files
        "1": [".js", ".ts", ".jsx", ".tsx", ".json"],        // JavaScript/TypeScript
        "2": [".html", ".htm", ".css", ".scss", ".sass", ".vue"], // Frontend
        "3": [".md", ".txt", ".rst", "*.markdown"]           // Documentation
    },
    
    // DIRECTORY RULES - Group by folder patterns (supports glob)
    // Useful for organizing by project structure
    "directory_rules": {
        "0": ["*/src/*", "*/lib/*", "*/app/*"],             // Source code
        "1": ["*/tests/*", "*/test/*", "*/__tests__/*"],    // Test files
        "2": ["*/docs/*", "*/documentation/*"],             // Documentation
        "3": ["*/config/*", "*/settings/*", "*/conf/*"]     // Configuration
    },
    
    // SEMANTIC RULES - Group by file state and properties
    // Available types: project, external, unsaved, dirty, saved, scratch, readonly
    "semantic_rules": {
        "0": ["project", "saved"],        // Project files that are saved
        "1": ["external"],                // Files outside project folders
        "2": ["unsaved", "dirty"],        // Unsaved or modified files
        "3": ["scratch", "readonly"]      // Scratch buffers and read-only files
    },
    
    // CUSTOM RULES - Advanced pattern matching with priorities
    // Higher priority rules are checked first
    "custom_rules": [
        {
            "name": "Test files (high priority)",
            "pattern": "*test*.py",
            "group": 1,
            "priority": 20
        },
        {
            "name": "Configuration files",
            "pattern": "*.{json,yaml,yml,toml,ini,cfg,conf}",
            "group": 3,
            "priority": 15
        },
        {
            "name": "Documentation files",
            "pattern": "*.{md,rst,txt}",
            "group": 2,
            "priority": 10
        }
    ],

    // ============================================================================
    // PROJECT MEMBERSHIP RULES
    // ============================================================================
    
    // Default group for files that are part of the current project
    "project_files_group": 0,
    
    // Default group for files outside the current project
    "external_files_group": 1,
    
    // Default group for unsaved/untitled files
    "unsaved_files_group": 2,

    // ============================================================================
    // EXCLUSION PATTERNS
    // ============================================================================
    
    // Files matching these patterns won't be auto-placed
    "exclude_patterns": [
        "*.tmp", "*.temp", "*.cache",
        "*/.git/*", "*/node_modules/*", "*/__pycache__/*",
        "*.pyc", "*.pyo", "*.class",
        "Untitled*", "untitled*"
    ],

    // ============================================================================
    // LAYOUT MANAGEMENT
    // ============================================================================
    
    // Automatically adjust layout when placing tabs
    "auto_adjust_layout": true,
    
    // Minimum number of groups to maintain
    "min_groups": 2,
    
    // Maximum number of groups to create
    "max_groups": 4,
    
    // Behavior when target group doesn't exist: "create", "skip", "fallback"
    "missing_group_behavior": "create",

    // ============================================================================
    // LAYOUT CONFIGURATIONS
    // ============================================================================
    // Define custom layouts for different group counts
    // Each layout specifies columns, rows, and cell arrangements
    
    "layout_configs": {
        "2": {
            "cols": [0.0, 0.5, 1.0],
            "rows": [0.0, 1.0],
            "cells": [[0, 0, 1, 1], [1, 0, 2, 1]]
        },
        "3": {
            "cols": [0.0, 0.33, 0.66, 1.0],
            "rows": [0.0, 1.0],
            "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1]]
        },
        "4": {
            "cols": [0.0, 0.5, 1.0],
            "rows": [0.0, 0.5, 1.0],
            "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]]
        }
    },

    // ============================================================================
    // LAYOUT TEMPLATES (OPTIONAL)
    // ============================================================================
    // Predefined layout templates for common workflows
    // Uncomment and customize as needed
    
    // "layout_templates": {
    //     "web_development": {
    //         "description": "Optimized for frontend development",
    //         "layout": {
    //             "cols": [0.0, 0.4, 0.7, 1.0],
    //             "rows": [0.0, 0.6, 1.0],
    //             "cells": [
    //                 [0, 0, 1, 2],  // Main code (tall)
    //                 [1, 0, 2, 1],  // Tests
    //                 [2, 0, 3, 1],  // Styles
    //                 [1, 1, 3, 2]   // Scratch/external
    //             ]
    //         },
    //         "rules": {
    //             "0": [".js", ".ts", ".jsx", ".tsx", ".vue"],
    //             "1": ["*.test.*", "*.spec.*"],
    //             "2": [".css", ".scss", ".sass"],
    //             "3": [".md", ".txt", ".json"]
    //         }
    //     }
    // },

    // ============================================================================
    // PERFORMANCE & SAFETY
    // ============================================================================
    
    // Maximum placements per second (prevents performance issues)
    "max_placements_per_second": 5,
    
    // Enable placement history tracking (prevents oscillation)
    "track_placement_history": true,
    
    // Cache project settings for performance
    "cache_project_settings": true

    // ============================================================================
    // PROJECT-SPECIFIC OVERRIDES
    // ============================================================================
    // Any of the above settings can be overridden in .sublime-project files:
    //
    // {
    //     "folders": [{"path": "."}],
    //     "settings": {
    //         "jorn_auto_place_tabs": {
    //             "auto_place_on_activation": true,
    //             "file_type_rules": {
    //                 "0": [".py", ".pyw"],
    //                 "1": [".js", ".ts"]
    //             },
    //             "layout_configs": {
    //                 "2": {
    //                     "cols": [0.0, 0.6, 1.0],
    //                     "rows": [0.0, 1.0],
    //                     "cells": [[0, 0, 1, 1], [1, 0, 2, 1]]
    //                 }
    //             }
    //         }
    //     }
    // }
}
