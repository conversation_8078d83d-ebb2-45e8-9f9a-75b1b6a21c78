import sublime
import sublime_plugin
import os
import time
import fnmatch
from collections import defaultdict, deque

PLUGIN_NAME = "Jorn_AutoPlaceTabs"
MAX_PLACEMENTS_PER_SECOND = 5


class Jorn_AutoPlaceTabsCommand(sublime_plugin.EventListener):
    """
    Main plugin that automatically places tabs in appropriate groups based on:
    - File type/extension patterns
    - Directory patterns  
    - Project membership
    - Custom user-defined rules
    
    Prevents infinite loops via:
    1) Recursion guard (_is_placing)
    2) Frequency-based limit (MAX_PLACEMENTS_PER_SECOND)
    3) Placement history tracking
    """

    _instance = None

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        self._is_placing = False
        self._placement_timestamps = deque()
        self._last_placements = defaultdict(lambda: defaultdict(tuple))
        self._project_settings_cache = {}  # Cache project settings by window ID
        Jorn_AutoPlaceTabsCommand._instance = self

    @classmethod
    def instance(cls):
        """Used by manual placement commands to reference this plugin instance."""
        return cls._instance

    def _debug_print(self, message):
        """Print debug message only if debug mode is enabled."""
        if self.settings.get("enable_debug_prints", False):
            print(f"[{PLUGIN_NAME}] {message}")

    def _get_effective_settings(self, window):
        """Get effective settings combining global and project-specific settings."""
        if not window:
            return self.settings

        window_id = window.id()

        # Check cache first
        if window_id in self._project_settings_cache:
            return self._project_settings_cache[window_id]

        # Start with global settings
        effective_settings = {}

        # Copy all global settings
        for key in ["auto_place_on_activation", "auto_place_on_load", "enable_debug_prints",
                   "group_sort_method", "file_type_rules", "directory_rules", "project_files_group",
                   "external_files_group", "unsaved_files_group", "exclude_patterns", "custom_rules",
                   "auto_adjust_layout", "missing_group_behavior", "layout_mode", "layout_type", "layout_configs"]:
            effective_settings[key] = self.settings.get(key)

        # Get project-specific settings
        project_data = window.project_data()
        if project_data and "settings" in project_data:
            project_settings = project_data["settings"].get("jorn_auto_place_tabs", {})
            if project_settings:
                self._debug_print(f"Found project-specific settings: {list(project_settings.keys())}")
                # Override global settings with project-specific ones
                effective_settings.update(project_settings)

        # Cache the result
        self._project_settings_cache[window_id] = effective_settings

        return effective_settings

    def _clear_settings_cache(self, window_id=None):
        """Clear settings cache for a specific window or all windows."""
        if window_id:
            self._project_settings_cache.pop(window_id, None)
        else:
            self._project_settings_cache.clear()

    def _get_setting(self, key, default=None, window=None):
        """Get a setting value from effective settings (global + project-specific)."""
        if window:
            effective_settings = self._get_effective_settings(window)
            return effective_settings.get(key, default)
        else:
            return self.settings.get(key, default)

    def _check_placement_frequency(self):
        """Rate limiting to prevent excessive placements."""
        now = time.time()
        self._placement_timestamps.append(now)
        
        # Remove timestamps older than 1 second
        while (self._placement_timestamps and 
               now - self._placement_timestamps[0] > 1.0):
            self._placement_timestamps.popleft()
        
        return len(self._placement_timestamps) <= MAX_PLACEMENTS_PER_SECOND

    def on_activated_async(self, view):
        """Handle tab activation for auto-placement."""
        if not view or not view.window() or self._is_placing:
            return

        window = view.window()
        if not self._get_setting("auto_place_on_activation", True, window):
            return

        if not self._should_auto_place(view):
            return

        if not self._check_placement_frequency():
            self._debug_print("Rate limit exceeded, skipping placement")
            return

        self._place_tab(view)

    def on_load_async(self, view):
        """Handle file load for auto-placement."""
        if not view or not view.window() or self._is_placing:
            return

        window = view.window()
        if not self._get_setting("auto_place_on_load", True, window):
            return

        if not self._should_auto_place(view):
            return

        if not self._check_placement_frequency():
            return

        # Small delay to ensure file is fully loaded
        sublime.set_timeout_async(lambda: self._place_tab(view), 100)

    def on_window_command(self, window, command_name, args):
        """Handle window commands that might change project settings."""
        if command_name in ["open_project", "close_project", "switch_project"]:
            # Clear settings cache when project changes
            self._clear_settings_cache(window.id())

    def on_post_window_command(self, window, command_name, args):
        """Handle post-window commands that might change project settings."""
        if command_name in ["open_project", "close_project", "switch_project"]:
            # Clear settings cache when project changes
            self._clear_settings_cache(window.id())

    def _should_auto_place(self, view):
        """Determine if a view should be auto-placed."""
        if not view or not view.window():
            return False

        # Skip if already in correct group
        target_group = self._determine_target_group(view)
        if target_group is None:
            return False

        current_group, _ = view.window().get_view_index(view)
        return current_group != target_group

    def _determine_target_group(self, view):
        """Determine the target group for a view based on placement rules."""
        window = view.window()
        if not window:
            return None

        file_path = view.file_name()
        if not file_path:
            return self._get_group_for_unsaved(view)

        # Check if file should be excluded
        if self._should_exclude_file(file_path, window):
            return None

        # Check custom rules first (highest priority)
        target_group = self._check_custom_rules(file_path, window)
        if target_group is not None:
            return target_group

        # Check file type rules
        target_group = self._check_file_type_rules(file_path, window)
        if target_group is not None:
            return target_group

        # Check directory rules
        target_group = self._check_directory_rules(file_path, window)
        if target_group is not None:
            return target_group

        # Check project membership
        target_group = self._check_project_rules(view, file_path, window)
        if target_group is not None:
            return target_group

        return None

    def _check_file_type_rules(self, file_path, window=None):
        """Check file type/extension rules."""
        file_ext = os.path.splitext(file_path)[1].lower()
        file_name = os.path.basename(file_path)

        type_rules = self._get_setting("file_type_rules", {}, window)

        for group_index, patterns in type_rules.items():
            try:
                group_num = int(group_index)
                for pattern in patterns:
                    if (pattern.startswith('.') and file_ext == pattern.lower()) or \
                       fnmatch.fnmatch(file_name.lower(), pattern.lower()):
                        return group_num
            except (ValueError, TypeError):
                continue

        return None

    def _check_directory_rules(self, file_path, window=None):
        """Check directory-based rules."""
        dir_path = os.path.dirname(file_path)
        dir_rules = self._get_setting("directory_rules", {}, window)

        for group_index, patterns in dir_rules.items():
            try:
                group_num = int(group_index)
                for pattern in patterns:
                    if fnmatch.fnmatch(dir_path, pattern):
                        return group_num
            except (ValueError, TypeError):
                continue

        return None

    def _check_project_rules(self, view, file_path, window=None):
        """Check project membership rules."""
        if not window:
            window = view.window()
        project_folders = window.folders() if window else []

        is_in_project = any(file_path.startswith(folder) for folder in project_folders)

        if is_in_project:
            return self._get_setting("project_files_group", None, window)
        else:
            return self._get_setting("external_files_group", None, window)

    def _get_group_for_unsaved(self, view):
        """Determine group for unsaved files."""
        window = view.window()
        return self._get_setting("unsaved_files_group", None, window)

    def _should_exclude_file(self, file_path, window=None):
        """Check if file should be excluded from auto-placement."""
        exclude_patterns = self._get_setting("exclude_patterns", [], window)
        file_name = os.path.basename(file_path)

        for pattern in exclude_patterns:
            if fnmatch.fnmatch(file_path, pattern) or fnmatch.fnmatch(file_name, pattern):
                return True
        return False

    def _check_custom_rules(self, file_path, window=None):
        """Check custom rules with priority ordering."""
        custom_rules = self._get_setting("custom_rules", [], window)
        if not custom_rules:
            return None

        # Sort by priority (higher first)
        sorted_rules = sorted(custom_rules, key=lambda r: r.get("priority", 0), reverse=True)

        file_name = os.path.basename(file_path)
        for rule in sorted_rules:
            pattern = rule.get("pattern", "")
            if pattern and (fnmatch.fnmatch(file_name, pattern) or fnmatch.fnmatch(file_path, pattern)):
                return rule.get("group")

        return None

    def _place_tab(self, view):
        """Place a tab in its target group."""
        if self._is_placing:
            return

        window = view.window()
        if not window:
            return

        target_group = self._determine_target_group(view)
        if target_group is None:
            return

        current_group, current_index = window.get_view_index(view)

        # Check layout mode to determine how to handle groups
        layout_mode = self._get_setting("layout_mode", "compact", window)

        if layout_mode == "compact":
            # Compact mode: only create groups for tabs that actually exist
            target_group = self._get_compact_group_mapping(window, target_group)

        # Ensure target group exists, create if needed
        if target_group >= window.num_groups():
            if self._get_setting("auto_adjust_layout", False, window):
                if layout_mode == "compact":
                    self._create_compact_layout(window)
                else:
                    self._create_layout_for_groups(window, target_group + 1)
            else:
                # Target group doesn't exist and auto-layout is disabled
                fallback_behavior = self._get_setting("missing_group_behavior", "skip", window)

                if fallback_behavior == "skip":
                    self._debug_print(f"Target group {target_group} doesn't exist (only {window.num_groups()} groups), skipping")
                    return
                elif fallback_behavior == "last_group":
                    target_group = window.num_groups() - 1
                    self._debug_print(f"Target group doesn't exist, using last group ({target_group})")
                elif fallback_behavior == "first_group":
                    target_group = 0
                    self._debug_print(f"Target group doesn't exist, using first group ({target_group})")
                else:
                    self._debug_print(f"Target group {target_group} doesn't exist, skipping (unknown fallback behavior)")
                    return

        if current_group == target_group:
            return

        self._is_placing = True
        try:
            # Determine target index within group
            target_index = self._get_target_index(view, target_group, window)

            self._debug_print(f"Moving tab from group {current_group} to group {target_group}, index {target_index}")
            window.set_view_index(view, target_group, target_index)

            # Track this placement
            self._last_placements[window.id()][view.id()] = (target_group, target_index, time.time())

        finally:
            self._is_placing = False

    def _get_target_index(self, view, target_group, window=None):
        """Determine the target index within a group."""
        if not window:
            window = view.window()
        views_in_group = window.views_in_group(target_group)

        sort_method = self._get_setting("group_sort_method", "append", window)

        if sort_method == "prepend":
            return 0
        elif sort_method == "append":
            return len(views_in_group)
        elif sort_method == "alphabetical":
            return self._get_alphabetical_index(view, views_in_group)
        else:
            return len(views_in_group)

    def _get_alphabetical_index(self, view, views_in_group):
        """Get index for alphabetical insertion."""
        view_name = os.path.basename(view.file_name() or view.name() or "")
        
        for i, existing_view in enumerate(views_in_group):
            existing_name = os.path.basename(existing_view.file_name() or existing_view.name() or "")
            if view_name.lower() < existing_name.lower():
                return i
        
        return len(views_in_group)

    def _get_compact_group_mapping(self, window, logical_group):
        """Map logical group numbers to compact physical group positions."""
        # Get all views and determine which logical groups are actually used
        used_groups = set()
        for view in window.views():
            view_logical_group = self._determine_target_group(view)
            if view_logical_group is not None:
                used_groups.add(view_logical_group)

        # Add the current logical group to the used set
        used_groups.add(logical_group)

        # Create sorted mapping from logical groups to compact positions
        sorted_groups = sorted(used_groups)
        group_mapping = {logical: physical for physical, logical in enumerate(sorted_groups)}

        physical_group = group_mapping.get(logical_group, 0)
        self._debug_print(f"Compact mapping: logical group {logical_group} -> physical group {physical_group}")
        self._debug_print(f"Used logical groups: {sorted_groups}")

        return physical_group

    def _create_compact_layout(self, window):
        """Create a layout with only the groups that are actually needed."""
        # Determine which logical groups are actually used
        used_groups = set()
        for view in window.views():
            logical_group = self._determine_target_group(view)
            if logical_group is not None:
                used_groups.add(logical_group)

        if not used_groups:
            self._debug_print("No groups needed for compact layout")
            return

        needed_groups = len(used_groups)
        self._debug_print(f"Creating compact layout for {needed_groups} groups (logical groups: {sorted(used_groups)})")

        # Check for custom layout for this group count
        layout_configs = self._get_setting("layout_configs", {}, window)
        custom_layout = layout_configs.get(str(needed_groups))

        if custom_layout:
            self._debug_print(f"Using custom layout for {needed_groups} groups")
            self._apply_layout(window, custom_layout)
            return

        # Generate layout based on layout type
        layout_type = self._get_setting("layout_type", "columns", window)

        if layout_type == "columns":
            layout = self._create_columns_layout(needed_groups)
        elif layout_type == "rows":
            layout = self._create_rows_layout(needed_groups)
        elif layout_type == "grid":
            layout = self._create_grid_layout(needed_groups)
        else:
            layout = self._create_columns_layout(needed_groups)

        self._debug_print(f"Creating compact {layout_type} layout for {needed_groups} groups")
        self._apply_layout(window, layout)

    def _create_layout_for_groups(self, window, num_groups):
        """Create a layout with the specified number of groups."""
        current_groups = window.num_groups()
        self._debug_print(f"Need {num_groups} groups, current: {current_groups}")

        if num_groups <= current_groups:
            self._debug_print(f"Already have enough groups")
            return

        # Check if we have a custom layout for this group count
        layout_configs = self._get_setting("layout_configs", {}, window)
        custom_layout = layout_configs.get(str(num_groups))

        if custom_layout:
            self._debug_print(f"Using custom layout for {num_groups} groups")
            self._apply_layout(window, custom_layout)
            return

        # Generate layout based on layout type
        layout_type = self._get_setting("layout_type", "columns", window)

        if layout_type == "columns":
            layout = self._create_columns_layout(num_groups)
        elif layout_type == "rows":
            layout = self._create_rows_layout(num_groups)
        elif layout_type == "grid":
            layout = self._create_grid_layout(num_groups)
        else:
            # Default to columns
            layout = self._create_columns_layout(num_groups)

        self._debug_print(f"Creating {layout_type} layout for {num_groups} groups")
        self._apply_layout(window, layout)

    def _create_columns_layout(self, num_groups):
        """Create a simple columns layout."""
        cols = [i / num_groups for i in range(num_groups + 1)]
        rows = [0.0, 1.0]
        cells = [[i, 0, i + 1, 1] for i in range(num_groups)]
        return {"cols": cols, "rows": rows, "cells": cells}

    def _create_rows_layout(self, num_groups):
        """Create a simple rows layout."""
        cols = [0.0, 1.0]
        rows = [i / num_groups for i in range(num_groups + 1)]
        cells = [[0, i, 1, i + 1] for i in range(num_groups)]
        return {"cols": cols, "rows": rows, "cells": cells}

    def _create_grid_layout(self, num_groups):
        """Create a grid layout."""
        import math
        num_columns = int(math.sqrt(num_groups))
        num_rows = (num_groups + num_columns - 1) // num_columns

        if num_columns == 0:
            num_columns = 1

        cols = [i / num_columns for i in range(num_columns + 1)]
        rows = [i / num_rows for i in range(num_rows + 1)]

        cells = []
        group = 0
        for row in range(num_rows):
            for col in range(num_columns):
                if group < num_groups:
                    cells.append([col, row, col + 1, row + 1])
                    group += 1

        return {"cols": cols, "rows": rows, "cells": cells}

    def _apply_layout(self, window, layout):
        """Apply a layout to the window."""
        try:
            window.set_layout(layout)
            self._debug_print(f"Applied layout: {layout}")
        except Exception as e:
            self._debug_print(f"Failed to apply layout: {e}")





class JornAutoPlaceTabsManualCommand(sublime_plugin.WindowCommand):
    """Manual command to place current tab according to rules."""
    
    def run(self):
        view = self.window.active_view()
        if not view:
            return
        
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if plugin:
            plugin._place_tab(view)
            sublime.status_message("Tab placed according to rules")


class JornAutoPlaceTabsPlaceAllCommand(sublime_plugin.WindowCommand):
    """Command to place all tabs according to rules."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        # Force compact layout creation if enabled
        layout_mode = plugin._get_setting("layout_mode", "compact", self.window)
        if layout_mode == "compact" and plugin._get_setting("auto_adjust_layout", False, self.window):
            plugin._create_compact_layout(self.window)

        placed_count = 0
        for view in self.window.views():
            if plugin._should_auto_place(view):
                plugin._place_tab(view)
                placed_count += 1

        sublime.status_message(f"Placed {placed_count} tabs according to rules")


class JornAutoPlaceTabsToggleCommand(sublime_plugin.WindowCommand):
    """Toggle auto-placement on/off."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        current = plugin.settings.get("auto_place_on_activation", True)
        plugin.settings.set("auto_place_on_activation", not current)
        sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")

        status = "enabled" if not current else "disabled"
        sublime.status_message(f"Auto-placement {status}")


class JornAutoPlaceTabsReloadSettingsCommand(sublime_plugin.WindowCommand):
    """Reload plugin settings and clear project settings cache."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        plugin.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        plugin._clear_settings_cache()  # Clear all cached project settings
        sublime.status_message("AutoPlace settings reloaded")


class JornAutoPlaceTabsShowRulesCommand(sublime_plugin.WindowCommand):
    """Show current placement rules in a new view."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        view = self.window.new_file()
        view.set_name("AutoPlace Rules")
        view.set_scratch(True)

        rules_text = self._format_rules(plugin.settings)
        view.run_command("append", {"characters": rules_text})
        view.set_read_only(True)

    def _format_rules(self, settings):
        """Format current rules for display."""
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return "Plugin not available"

        # Get effective settings for this window
        effective_settings = plugin._get_effective_settings(self.window)

        lines = ["# Jorn AutoPlace Tabs - Current Rules\n\n"]

        # Check if project-specific settings are active
        project_data = self.window.project_data()
        has_project_settings = (project_data and "settings" in project_data and
                               "jorn_auto_place_tabs" in project_data["settings"])

        if has_project_settings:
            lines.append("## Project-Specific Settings Active\n")
            project_settings = project_data["settings"]["jorn_auto_place_tabs"]
            lines.append(f"Project overrides: {', '.join(project_settings.keys())}\n\n")
        else:
            lines.append("## Using Global Settings Only\n\n")

        # Auto-placement status
        auto_on_activation = effective_settings.get("auto_place_on_activation", True)
        auto_on_load = effective_settings.get("auto_place_on_load", True)
        lines.append(f"Auto-placement on activation: {auto_on_activation}\n")
        lines.append(f"Auto-placement on load: {auto_on_load}\n\n")

        # File type rules
        file_type_rules = effective_settings.get("file_type_rules", {})
        if file_type_rules:
            lines.append("## File Type Rules\n")
            for group, patterns in file_type_rules.items():
                lines.append(f"Group {group}: {', '.join(patterns)}\n")
            lines.append("\n")

        # Directory rules
        dir_rules = effective_settings.get("directory_rules", {})
        if dir_rules:
            lines.append("## Directory Rules\n")
            for group, patterns in dir_rules.items():
                lines.append(f"Group {group}: {', '.join(patterns)}\n")
            lines.append("\n")

        # Custom rules
        custom_rules = effective_settings.get("custom_rules", [])
        if custom_rules:
            lines.append("## Custom Rules\n")
            for rule in sorted(custom_rules, key=lambda r: r.get("priority", 0), reverse=True):
                name = rule.get("name", "Unnamed")
                pattern = rule.get("pattern", "")
                group = rule.get("group", "?")
                priority = rule.get("priority", 0)
                lines.append(f"{name}: {pattern} → Group {group} (Priority: {priority})\n")
            lines.append("\n")

        # Special groups
        project_group = effective_settings.get("project_files_group")
        external_group = effective_settings.get("external_files_group")
        unsaved_group = effective_settings.get("unsaved_files_group")

        lines.append("## Special Groups\n")
        if project_group is not None:
            lines.append(f"Project files: Group {project_group}\n")
        if external_group is not None:
            lines.append(f"External files: Group {external_group}\n")
        if unsaved_group is not None:
            lines.append(f"Unsaved files: Group {unsaved_group}\n")

        # Layout settings
        auto_adjust = effective_settings.get("auto_adjust_layout", False)
        missing_behavior = effective_settings.get("missing_group_behavior", "skip")
        layout_mode = effective_settings.get("layout_mode", "compact")
        layout_type = effective_settings.get("layout_type", "columns")
        sort_method = effective_settings.get("group_sort_method", "append")

        lines.append("\n## Layout Settings\n")
        lines.append(f"Auto-adjust layout: {auto_adjust}\n")
        lines.append(f"Missing group behavior: {missing_behavior}\n")
        lines.append(f"Layout mode: {layout_mode}\n")
        lines.append(f"Layout type: {layout_type}\n")
        lines.append(f"Sort method: {sort_method}\n")

        # Custom layouts
        layout_configs = effective_settings.get("layout_configs", {})
        if layout_configs:
            lines.append("\n## Custom Layouts\n")
            for group_count, layout in layout_configs.items():
                group_count_actual = len(layout.get("cells", []))
                lines.append(f"Groups {group_count}: Custom layout with {group_count_actual} groups\n")

        return "".join(lines)


class JornAutoPlaceTabsCreateProjectSettingsCommand(sublime_plugin.WindowCommand):
    """Create project-specific settings template."""

    def run(self):
        project_data = self.window.project_data()
        if not project_data:
            sublime.error_message("No project file is currently open. Please save your project first.")
            return

        # Check if project settings already exist
        if "settings" not in project_data:
            project_data["settings"] = {}

        if "jorn_auto_place_tabs" in project_data["settings"]:
            if not sublime.ok_cancel_dialog(
                "Project-specific AutoPlace settings already exist. Overwrite?",
                "Overwrite"
            ):
                return

        # Create template settings
        template_settings = {
            "auto_place_on_activation": True,
            "file_type_rules": {
                "0": [".py", ".pyw"],
                "1": [".js", ".ts", ".jsx", ".tsx"],
                "2": [".html", ".css", ".scss"],
                "3": [".md", ".txt", ".json"]
            },
            "directory_rules": {
                "0": ["*/src/*", "*/lib/*"],
                "1": ["*/tests/*", "*/test/*"],
                "2": ["*/docs/*"],
                "3": ["*/config/*"]
            },
            "project_files_group": 0,
            "external_files_group": 1,
            "auto_adjust_layout": False,
            "missing_group_behavior": "skip",
            "layout_mode": "compact",
            "layout_type": "columns"
        }

        # Add to project data
        project_data["settings"]["jorn_auto_place_tabs"] = template_settings
        self.window.set_project_data(project_data)

        # Clear cache to pick up new settings
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if plugin:
            plugin._clear_settings_cache(self.window.id())

        sublime.status_message("Project-specific AutoPlace settings created")

    def is_enabled(self):
        return self.window.project_data() is not None


class JornAutoPlaceTabsTestLayoutCommand(sublime_plugin.WindowCommand):
    """Test a specific layout by applying it to the current window."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            sublime.error_message("AutoPlace plugin not available")
            return

        # Get available custom layouts
        layout_configs = plugin._get_setting("layout_configs", {}, self.window)

        # Create list of layout options
        layout_options = []

        # Add custom layouts
        for group_count, layout in layout_configs.items():
            actual_groups = len(layout.get("cells", []))
            layout_options.append([f"Custom: {group_count} groups", f"({actual_groups} groups)"])

        # Add generated layout options
        layout_options.extend([
            ["Generated: 2 Columns", "columns"],
            ["Generated: 3 Columns", "columns"],
            ["Generated: 4 Columns", "columns"],
            ["Generated: 8 Columns", "columns"],
            ["Generated: 2 Rows", "rows"],
            ["Generated: 3 Rows", "rows"],
            ["Generated: 4 Rows", "rows"],
            ["Generated: 2x2 Grid", "grid"],
            ["Generated: 3x3 Grid", "grid"]
        ])

        def on_select(index):
            if index == -1:
                return

            selected = layout_options[index]
            layout_name = selected[0]

            if layout_name.startswith("Custom:"):
                # Handle custom layouts
                group_count = layout_name.split(":")[1].strip().split()[0]
                layout_config = layout_configs.get(group_count)
                if layout_config:
                    plugin._apply_layout(self.window, layout_config)
                    sublime.status_message(f"Applied custom layout for {group_count} groups")
                else:
                    sublime.error_message(f"Custom layout for {group_count} groups not found")

            elif layout_name.startswith("Generated:"):
                # Handle generated layouts
                parts = layout_name.split()
                if "Columns" in layout_name:
                    num_groups = int(parts[1])
                    layout = plugin._create_columns_layout(num_groups)
                    plugin._apply_layout(self.window, layout)
                    sublime.status_message(f"Applied {num_groups} columns layout")
                elif "Rows" in layout_name:
                    num_groups = int(parts[1])
                    layout = plugin._create_rows_layout(num_groups)
                    plugin._apply_layout(self.window, layout)
                    sublime.status_message(f"Applied {num_groups} rows layout")
                elif "Grid" in layout_name:
                    if "2x2" in layout_name:
                        layout = plugin._create_grid_layout(4)
                        plugin._apply_layout(self.window, layout)
                        sublime.status_message("Applied 2x2 grid layout")
                    elif "3x3" in layout_name:
                        layout = plugin._create_grid_layout(9)
                        plugin._apply_layout(self.window, layout)
                        sublime.status_message("Applied 3x3 grid layout")

        if not layout_options:
            sublime.error_message("No layouts available to test")
            return

        self.window.show_quick_panel(layout_options, on_select)
