"""
<PERSON>rn AutoPlace Tabs - Layout-centric tab placement for Sublime Text 4

Core implementation of semantic file state detection and layout-based tab organization.
Rules are children of layouts, not standalone configurations.
"""

import sublime
import sublime_plugin
import os
import time
import fnmatch
from collections import defaultdict, deque


class Jorn<PERSON><PERSON><PERSON>laceTabsEventListener(sublime_plugin.EventListener):
    """
    Main event listener for automatic tab placement.
    Implements layout-centric design with semantic state detection.
    """
    
    def __init__(self):
        self.settings = None
        self.project_settings_cache = {}
        self.placement_history = deque(maxlen=50)
        self.last_placement_time = 0
        self.placement_count = 0
        self._is_placing = False
        self.load_settings()
    
    def load_settings(self):
        """Load global settings and clear project cache."""
        self.settings = sublime.load_settings("Jorn_AutoPlaceTabs.sublime-settings")
        self.project_settings_cache.clear()
    
    def get_project_settings(self, window):
        """Get project-specific settings with caching."""
        if not window:
            return {}
        
        project_file = window.project_file_name()
        if not project_file:
            return {}
        
        # Check cache
        if project_file in self.project_settings_cache:
            return self.project_settings_cache[project_file]
        
        # Load project settings
        project_data = window.project_data() or {}
        project_settings = project_data.get("settings", {}).get("jorn_auto_place_tabs", {})
        
        # Cache and return
        self.project_settings_cache[project_file] = project_settings
        return project_settings
    
    def get_active_layout_config(self, window):
        """Get the active layout configuration for the window."""
        project_settings = self.get_project_settings(window)
        
        # Get active layout name
        active_layout = project_settings.get("active_layout")
        if not active_layout:
            return None
        
        # Get layout configuration
        layout_config = project_settings.get(active_layout)
        if not layout_config:
            # Fallback to global settings template
            layout_config = self.settings.get(active_layout)
        
        return layout_config
    
    def detect_file_states(self, view):
        """
        Detect semantic states for a file/view.
        Returns list of applicable state strings.
        """
        if not view or not view.window():
            return ["invalid"]
        
        states = []
        file_name = view.file_name()
        
        # Basic states
        if view.is_dirty():
            states.append("dirty")
        else:
            states.append("saved")
        
        if view.is_scratch():
            states.append("scratch")
        
        if view.is_read_only():
            states.append("readonly")
        
        # File existence states
        if file_name:
            if os.path.exists(file_name):
                states.append("exists")
            else:
                states.append("deleted")
        else:
            states.append("unsaved")
        
        # Content states
        if view.size() == 0 or not view.substr(sublime.Region(0, view.size())).strip():
            states.append("empty")
        else:
            states.append("non_empty")
        
        # Project relationship
        if file_name and view.window():
            project_folders = view.window().folders()
            is_project_file = False
            
            for folder in project_folders:
                if file_name.startswith(folder):
                    is_project_file = True
                    break
            
            if is_project_file:
                states.append("project")
            else:
                states.append("external")
        else:
            states.append("external")
        
        # Activity states
        if view == view.window().active_view():
            states.append("active")
        else:
            states.append("inactive")
        
        # Size-based states (placeholder - would need configuration)
        if view.size() > 100000:  # 100KB threshold
            states.append("large_file")
        elif view.size() < 1000:  # 1KB threshold
            states.append("small_file")
        
        return states
    
    def match_rule(self, view, rule_config):
        """
        Check if a view matches a rule configuration.
        Returns True if the view matches the rule.
        """
        if not rule_config or "match" not in rule_config:
            return False
        
        match_config = rule_config["match"]
        exclude_config = rule_config.get("exclude", {})
        
        # Get file info
        file_name = view.file_name() or ""
        file_basename = os.path.basename(file_name)
        file_states = self.detect_file_states(view)
        
        # Check exclusions first
        if self._matches_criteria(file_name, file_basename, file_states, exclude_config):
            return False
        
        # Check match criteria
        return self._matches_criteria(file_name, file_basename, file_states, match_config)
    
    def _matches_criteria(self, file_name, file_basename, file_states, criteria):
        """Check if file matches the given criteria."""
        if not criteria:
            return False
        
        # Check extensions
        extensions = criteria.get("extensions", [])
        if extensions:
            file_ext = os.path.splitext(file_basename)[1].lower()
            if not any(file_ext == ext.lower() for ext in extensions):
                return False
        
        # Check file name patterns
        name_patterns = criteria.get("file_name_patterns", [])
        if name_patterns:
            if not any(fnmatch.fnmatch(file_basename, pattern) for pattern in name_patterns):
                return False
        
        # Check directory patterns
        dir_patterns = criteria.get("directory_patterns", [])
        if dir_patterns:
            if not any(fnmatch.fnmatch(file_name, pattern) for pattern in dir_patterns):
                return False
        
        # Check semantic types
        types = criteria.get("types", [])
        if types:
            if not any(state in file_states for state in types):
                return False
        
        return True
    
    def find_target_group(self, view, layout_config):
        """
        Find the target group for a view based on layout rules.
        Returns group index or None if no match.
        """
        if not layout_config or "rules" not in layout_config:
            return None
        
        rules = layout_config["rules"]
        
        # Check rules in order (0, 1, 2, ...)
        for group_str in sorted(rules.keys(), key=lambda x: int(x) if x.isdigit() else float('inf')):
            rule_config = rules[group_str]
            
            if self.match_rule(view, rule_config):
                return int(group_str)
        
        return None
    
    def rate_limit_check(self):
        """Check if placement is rate limited."""
        current_time = time.time()
        
        # Reset counter if more than 1 second has passed
        if current_time - self.last_placement_time > 1.0:
            self.placement_count = 0
        
        # Check rate limit (5 placements per second)
        max_placements = 5
        if self.placement_count >= max_placements:
            return False
        
        # Update counters
        self.placement_count += 1
        self.last_placement_time = current_time
        return True
    
    def place_view_in_group(self, view, target_group):
        """Place a view in the specified group."""
        if not view or not view.window():
            return False
        
        window = view.window()
        current_group = window.get_view_index(view)[0]
        
        # Don't move if already in target group
        if current_group == target_group:
            return False
        
        # Ensure target group exists
        layout = window.get_layout()
        num_groups = len(layout.get("cells", []))
        
        if target_group >= num_groups:
            # Create additional groups if needed (compact mode logic would go here)
            return False
        
        # Move view to target group
        window.set_view_index(view, target_group, len(window.views_in_group(target_group)))
        return True
    
    def on_activated_async(self, view):
        """Handle view activation for automatic placement."""
        if not self._should_auto_place(view, "activation"):
            return
        
        self._place_view_if_needed(view)
    
    def on_load_async(self, view):
        """Handle view loading for automatic placement."""
        if not self._should_auto_place(view, "load"):
            return
        
        self._place_view_if_needed(view)
    
    def _should_auto_place(self, view, trigger):
        """Check if automatic placement should occur."""
        if not view or not view.window():
            return False
        
        # Check recursion guard
        if self._is_placing:
            return False
        
        # Check rate limiting
        if not self.rate_limit_check():
            return False
        
        # Check project settings
        project_settings = self.get_project_settings(view.window())
        
        if trigger == "activation":
            return project_settings.get("auto_place_on_activation", False)
        elif trigger == "load":
            return project_settings.get("auto_place_on_load", False)
        
        return False
    
    def _place_view_if_needed(self, view):
        """Place view according to active layout rules."""
        if not view or not view.window():
            return
        
        # Set recursion guard
        self._is_placing = True
        
        try:
            layout_config = self.get_active_layout_config(view.window())
            if not layout_config:
                return
            
            target_group = self.find_target_group(view, layout_config)
            if target_group is not None:
                self.place_view_in_group(view, target_group)
        
        finally:
            # Clear recursion guard
            self._is_placing = False


class JornAutoPlaceTabsManualCommand(sublime_plugin.WindowCommand):
    """Manual tab placement command."""
    
    def run(self, scope="current"):
        """Execute manual placement."""
        listener = None
        for callback in sublime_plugin.all_callbacks['on_activated_async']:
            if isinstance(callback, JornAutoPlaceTabsEventListener):
                listener = callback
                break
        
        if not listener:
            return
        
        if scope == "current":
            view = self.window.active_view()
            if view:
                listener._place_view_if_needed(view)
        elif scope == "all":
            for view in self.window.views():
                listener._place_view_if_needed(view)
        elif scope == "group":
            group, _ = self.window.get_view_index(self.window.active_view())
            for view in self.window.views_in_group(group):
                listener._place_view_if_needed(view)


class JornAutoPlaceTabsToggleCommand(sublime_plugin.WindowCommand):
    """Toggle automatic placement."""
    
    def run(self, enable=None):
        """Toggle or set auto-placement state."""
        # Implementation would modify project settings
        pass


class JornAutoPlaceTabsShowRulesCommand(sublime_plugin.WindowCommand):
    """Show current rules and configuration."""
    
    def run(self):
        """Display active rules in a panel."""
        # Implementation would show current layout and rules
        pass


class JornAutoPlaceTabsReloadSettingsCommand(sublime_plugin.WindowCommand):
    """Reload settings command."""
    
    def run(self):
        """Reload plugin settings."""
        for callback in sublime_plugin.all_callbacks['on_activated_async']:
            if isinstance(callback, JornAutoPlaceTabsEventListener):
                callback.load_settings()
                break
