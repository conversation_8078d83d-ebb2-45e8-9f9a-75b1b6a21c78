{
    "folders": [
        {
            "path": ".",
            "name": "My Project"
        }
    ],
    "settings": {
        "tab_size": 4,
        "translate_tabs_to_spaces": true,
        
        // Project-specific AutoPlace settings
        "jorn_auto_place_tabs": {
            // Enable auto-placement
            "auto_place_on_activation": true,
            "auto_place_on_load": true,
            
            // File type rules - organize by language/technology
            "file_type_rules": {
                "0": [".py", ".pyw", "*.python"],           // Python files → Group 0
                "1": [".js", ".ts", ".jsx", ".tsx", ".vue"], // Frontend → Group 1  
                "2": [".html", ".css", ".scss", ".sass"],    // Styling → Group 2
                "3": [".md", ".txt", ".rst", "*.markdown"]   // Documentation → Group 3
            },
            
            // Directory rules - organize by project structure
            "directory_rules": {
                "0": ["*/src/*", "*/lib/*", "*/app/*"],      // Source code → Group 0
                "1": ["*/tests/*", "*/test/*", "*/__tests__/*"], // Tests → Group 1
                "2": ["*/docs/*", "*/documentation/*"],      // Documentation → Group 2
                "3": ["*/config/*", "*/settings/*", "*/conf/*"] // Configuration → Group 3
            },
            
            // Custom rules with priority (higher priority = checked first)
            "custom_rules": [
                {
                    "name": "Test files (high priority)",
                    "pattern": "*test*.py",
                    "group": 1,
                    "priority": 20
                },
                {
                    "name": "Configuration files",
                    "pattern": "*.{json,yaml,yml,toml,ini,cfg,conf}",
                    "group": 3,
                    "priority": 15
                },
                {
                    "name": "Build files",
                    "pattern": "{Makefile,*.mk,*.cmake,CMakeLists.txt}",
                    "group": 3,
                    "priority": 10
                },
                {
                    "name": "Package files",
                    "pattern": "{package.json,requirements.txt,Pipfile,setup.py,pyproject.toml}",
                    "group": 3,
                    "priority": 12
                }
            ],
            
            // Special group assignments
            "project_files_group": 0,    // Files within project folders
            "external_files_group": 2,   // Files outside project folders
            "unsaved_files_group": 1,    // Untitled/unsaved files
            
            // Exclude certain files from auto-placement
            "exclude_patterns": [
                "*.tmp",
                "*.temp",
                "*/.git/*",
                "*/node_modules/*",
                "*/__pycache__/*",
                "*.pyc",
                "*.log"
            ],
            
            // Layout management
            "auto_adjust_layout": false,  // Don't create new groups automatically
            "missing_group_behavior": "skip", // Skip placement if group doesn't exist
            // Alternative options: "last_group", "first_group"
            "max_groups": 4,              // Don't create more than 4 groups (when auto_adjust_layout is true)
            "group_sort_method": "append", // How to order tabs within groups
            
            // Layout configurations for different group counts
            "layout_configs": {
                "2": {
                    "cols": [0.0, 0.6, 1.0],
                    "rows": [0.0, 1.0],
                    "cells": [[0, 0, 1, 1], [1, 0, 2, 1]]
                },
                "3": {
                    "cols": [0.0, 0.4, 0.7, 1.0],
                    "rows": [0.0, 1.0],
                    "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1]]
                },
                "4": {
                    "cols": [0.0, 0.5, 1.0],
                    "rows": [0.0, 0.5, 1.0],
                    "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]]
                }
            },
            
            // Debug output (useful during setup)
            "enable_debug_prints": false
        }
    }
}
