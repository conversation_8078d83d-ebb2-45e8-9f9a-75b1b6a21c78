import sublime
import sublime_plugin
import os
import time
from operator import itemgetter
from itertools import groupby
import datetime


def settings():
    """Loads global settings"""
    return sublime.load_settings('Preferences.sublime-settings')

def internal_settings():
    """
    This class manages the plugin's internal settings, providing methods to get
    and set these settings.
    """
    return InternalSettings('SortTabs.sublime-settings')

class TabInfo:
    """
    This class stores information about each tab, like its view, group,
    activation time, duration, syntax, file size, file name, file path, etc.
    """

    def __init__(self, view, group):
        self.view = view
        self.group = group
        self.tab_activation_time = view.settings().get('tab_activation_time', 0)
        self.tab_activation_duration = view.settings().get('tab_activation_duration', 0)
        self.tab_dirty = view.is_dirty()
        self.tab_syntax = view.settings().get('syntax', '')
        self.tab_added = view.file_name() is None and self.tab_dirty
        self.tab_size = view.size()
        self.tab_empty = self.tab_size <= 1
        self.filename = os.path.basename(view.file_name() if view.file_name() else '')
        self.filepath = os.path.dirname(view.file_name() if view.file_name() else '')
        self.filetype = os.path.splitext(self.filename)[1] if self.filename else ''
        self.filedeleted = True if view.file_name() and not os.path.exists(view.file_name()) else False
        self.filedate = self._get_file_date(view)
        self.modified_today = self._check_modified_today(view)

    @staticmethod
    def _get_file_date(view):
        """Returns the file modification date if file exists, else returns 0."""
        try:
            return 0 if not os.path.exists(view.file_name()) else os.path.getmtime(view.file_name())
        except Exception:
            return 0

    def _check_modified_today(self, view):
        if view.file_name():
            today = datetime.date.today()
            try:
                modified_date = datetime.date.fromtimestamp(os.path.getmtime(view.file_name()))
                return modified_date == today
            except Exception:
                return 0
        return False



class TabSorter:
    def sorting_key(self):
        """Sorting key for a tab."""
        return (self.group, self.tab_empty, self.filedeleted, self.tab_added,
                self.filepath, self.tab_syntax, self.filetype, self.tab_dirty,
                self.filename, -self.filedate, self.tab_size)


class InternalSettings:
    """Manages internal settings."""
    def __init__(self, filename):
        self.settings = sublime.load_settings(filename)
        self.filename = filename

    def set(self, name, value):
        if self.settings.get(name) != value:
            self.settings.set(name, value)
            sublime.save_settings(self.filename)

    def get(self, name, default=None):
        return self.settings.get(name, default)

    def __getattr__(self, name):
        return getattr(self.settings, name)


class UpdateTabActivationTimeCommand(sublime_plugin.EventListener):
    """Updates activation time and duration when a tab is activated."""

    def on_activated(self, view):
        current_time = int(time.time())
        previous_time = view.settings().get('tab_activation_time', current_time)
        activation_duration = view.settings().get('tab_activation_duration', 0) + (current_time - previous_time)
        view.settings().set('tab_activation_time', current_time)
        view.settings().set('tab_activation_duration', activation_duration)


class ResetTabActivationTimeCommand(sublime_plugin.EventListener):
    """Resets activation time and duration when a tab is closed."""

    def on_close(self, view):
        view.settings().set('tab_activation_time', 0)
        view.settings().set('tab_activation_duration', 0)


class SortTabs(object):
    """Sort tabs in Sublime Text based on the defined strategy in subclasses"""
    def __init__(self, *args, **kwargs):
        super(SortTabs, self).__init__(*args, **kwargs)
        # register command in the menu
        SortTabsMenuCommand.register(self.name(), self.description())


    def run(self, current_grp_only=None):
        # clear console
        self.clear_console()

        # close all empty tabs before doing anything else
        if GLOBAL_SETTINGS.get('close_empty_tabs', False):
            closed_tabs = []
            for view in self.window.views():
                content = view.substr(sublime.Region(0, view.size()))
                non_whitespace_count = len(''.join(content.split()))
                if non_whitespace_count <= 1:
                    view.set_scratch(True)
                    view.close()
                    closed_tabs.append(content.split())
            if len(closed_tabs):
                tabs_closed_str = ('[SortTabs]: Closed %s empty tabs: %s' % (len(closed_tabs), closed_tabs))
                print(tabs_closed_str)
                sublime.status_message(tabs_closed_str)


        # save active view to restore it latter
        self.current_view = self.window.active_view()

        # store the last sort-command
        INTERNAL_SETTINGS.set('last_cmd', self.name())

        # if only the active group should be affected
        self.current_grp_only = current_grp_only
        if current_grp_only is None:
            self.current_grp_only = GLOBAL_SETTINGS.get('current_group_only', False)

        # init and sort list_views
        list_views = []
        self.init_file_views(list_views)
        self.sort_list_views(list_views)

        # reorder views
        self.sort_views(list_views)

        # message = '%s' % (self.description(), )
        # sublime.status_message(message.strip())

        # restore initially active view
        self.window.focus_view(self.current_view)

    def init_file_views(self, list_views):
        if self.current_grp_only:
            current_grp, _ = self.window.get_view_index(self.current_view)
        for view in self.window.views():
            group, _ = self.window.get_view_index(view)
            if not self.current_grp_only or (group == current_grp):
                tab_info = TabInfo(view, group)
                list_views.append(tab_info)

    def fill_list_views(self, list_views):
        pass

    # def sort_list_views(self, list_views):
    #     # sort list_views using TabInfo.sorting_key
    #     list_views.sort(key=lambda tab_info: tab_info.sorting_key())


    # def sort_list_views(self, list_views):
    #     # sort list_views using get_sort_key
    #     list_views.sort(key=self.get_sort_key)

    def sort_list_views(self, list_views):
        # If get_sort_key method is present in the subclass, use it for sorting.
        # Otherwise, use TabInfo.sorting_key for sorting.
        sort_func = getattr(self, "get_sort_key", lambda tab_info: tab_info.sorting_key())
        list_views.sort(key=sort_func)


    def sort_views(self, list_views):
        # sort views according to list_views
        for group, groupviews in groupby(list_views, lambda tab_info: tab_info.group):
            for index, tab_info in enumerate(groupviews):
                view = tab_info.view
                # remove flag for auto sorting
                view.settings().erase('sorttabs_tosort')
                if self.window.get_view_index(view) != (group, index):
                    self.window.set_view_index(view, group, index)


    def description(self, *args):
        # use class __doc__ for description
        return self.__doc__


    def clear_console(self, clear_console=True, number_of_newlines=1, insert_separator=False):
        # Load preferences settings
        default_pref = sublime.load_settings('Preferences.sublime-settings')

        # Retrieve or set default settings
        if clear_console is None:
            clear_console = console_settings.get('clear_console', False)
        if insert_separator is None:
            insert_separator = console_settings.get('insert_separator', True)
        if number_of_newlines is None:
            number_of_newlines = console_settings.get('number_of_newlines', 5)

        # Clear console if set to True
        if clear_console:
            scrollback = default_pref.get('console_max_history_lines')
            default_pref.set('console_max_history_lines', 1)
            print("")
            default_pref.set('console_max_history_lines', scrollback)


        # Insert separator if set to True
        if insert_separator:
            current_datetime = datetime.now()
            formatted_datetime = current_datetime.strftime("%d.%m.%Y - %H.%M")
            print('-' * 100)
            print("^ " + formatted_datetime)
            print('-' * 100)

        # Print newlines
        print('\n' * number_of_newlines)

# =============================================================================
# 15.10.2023 - Kl.16:48:
class SortTabsByNameCommand(SortTabs, sublime_plugin.WindowCommand):
    """
    Command to sort tabs by file name in reverse order
    """
    def get_sort_key(self, tab_info):
        return ("".join(chr(255 - ord(c)) for c in tab_info.filename.lower()),)

# class SortTabsByNameCommand(SortTabs, sublime_plugin.WindowCommand):
#     """
#     Command to sort tabs by file name
#     """
#     def get_sort_key(self, tab_info):
#         return (tab_info.filename.lower(),)


class SortTabsByFilePathCommand(SortTabs, sublime_plugin.WindowCommand):
    """
    Command to sort tabs by file path
    """
    def get_sort_key(self, tab_info):
        return (tab_info.filepath.lower(), tab_info.filename.lower())


class SortTabsBySyntaxCommand(SortTabs, sublime_plugin.WindowCommand):
    """
    Command to sort tabs by tab syntax
    """
    def get_sort_key(self, tab_info):
        return (tab_info.tab_syntax.lower(), tab_info.filename.lower())


class SortTabsByDateCommand(SortTabs, sublime_plugin.WindowCommand):
    """
    Command to sort tabs by modification date
    """
    def get_sort_key(self, tab_info):
        return (not tab_info.tab_dirty, -tab_info.filedate, tab_info.filename.lower())


class SortTabsByLastActivationCommand(SortTabs, sublime_plugin.WindowCommand):
    """
    Command to sort tabs by last activation
    """
    def get_sort_key(self, tab_info):
        current_time = int(time.time())

        # ensure the currently active tab is the most recent
        if tab_info.view.id() == self.current_view.id():
            current_tab_time = tab_info.tab_activation_time
            tab_info.tab_activation_time = current_time
            current_tab_duration = tab_info.tab_activation_duration
            tab_info.tab_activation_duration = current_tab_duration + (current_time - current_tab_time)

        # Active Within X Seconds (e.g. input of 3600 will get active within last hour)
        tab_active_within_seconds = GLOBAL_SETTINGS.get('exclude_filters').get('tab_active_within_seconds', 3600)

        # tab_active_within_seconds: calculate seconds since last activated
        tab_seconds_since_activated = current_time - tab_info.tab_activation_time
        is_active_recently = tab_seconds_since_activated < tab_active_within_seconds

        return (-tab_info.tab_activation_time)


class SortTabsByContentSizeCommand(SortTabs, sublime_plugin.WindowCommand):
    """
    Command to sort tabs by the number of characters in the content
    """
    def get_sort_key(self, tab_info):
        return (-tab_info.tab_size,)



class SortTabsByPathTypeDateCommand(SortTabs, sublime_plugin.WindowCommand):
    """
    Sort Tabs by file path, then type, then name, and finally date
    """
    def get_sort_key(self, tab_info):
        return (tab_info.filepath.lower(), tab_info.tab_syntax.lower(), not tab_info.tab_dirty, -tab_info.filedate, tab_info.filename.lower())



class SortTabsCustom1Command(SortTabs, sublime_plugin.WindowCommand):
    """
    # [Directory, Filetype, Filename] -> [Unsaved, Deleted, Empty]
    """
    def get_sort_key(self, tab_info):
        return (tab_info.filedeleted, tab_info.tab_added, tab_info.filepath.lower(), tab_info.tab_syntax.lower(), not tab_info.tab_dirty, -tab_info.filedate, tab_info.filename.lower())



class SortTabsCustom2Command(SortTabs, sublime_plugin.WindowCommand):
    """
    # [Directory, Filetype, Filename] -> [Unsaved, Deleted, Empty]
    """
    def get_sort_key(self, tab_info):
        return (tab_info.filetype, tab_info.filedeleted, tab_info.tab_added, tab_info.filepath.lower(), tab_info.tab_syntax.lower(), not tab_info.tab_dirty, -tab_info.filedate, tab_info.filename.lower())



class SortTabsCustom3Command(SortTabs, sublime_plugin.WindowCommand):
    """
    ModifiedToday then modified-date
    """
    def get_sort_key(self, tab_info):
        return (-tab_info.modified_today, tab_info.filedeleted, tab_info.tab_added, tab_info.filetype, tab_info.filepath.lower(), tab_info.tab_syntax.lower(), not tab_info.tab_dirty, -tab_info.filedate, tab_info.filename.lower())
        # return (-tab_info.modified_today, -tab_info.filedate)



class SortTabsMenuCommand(sublime_plugin.WindowCommand):
    """Command to provide a quick selection menu for registered sort commands"""
    registered_sort_commands = []

    def run(self, *args, **kwargs):
        self.args, self.kwargs = args, kwargs
        listcommands = []
        index = 0
        for pos, (desc, cmd) in enumerate(self.registered_sort_commands):
            listcommands.append(desc)
            if cmd == INTERNAL_SETTINGS.get('last_cmd'):
                index = pos
        self.window.show_quick_panel(listcommands, self._callback, 0, index)

    def _callback(self, index):
        if index != -1:
            self.window.run_command(self.registered_sort_commands[index][1], self.kwargs)

    @classmethod
    def register(cls, cmd, description):
        # add cmd to the available commands and sort them
        commands = set(cls.registered_sort_commands)
        commands.add((description, cmd))
        cls.registered_sort_commands = list(commands)
        cls.registered_sort_commands.sort()


class SortTabsRepeatLastCommand(sublime_plugin.WindowCommand):
    """Command to repeat the last sort command"""
    def run(self, *args, **kwargs):
        cmd = INTERNAL_SETTINGS.get('last_cmd')
        if cmd:
            self.window.run_command(cmd, kwargs)


def plugin_loaded():
    """Loads global and internal settings on plugin load"""
    global GLOBAL_SETTINGS, INTERNAL_SETTINGS
    GLOBAL_SETTINGS = sublime.load_settings('Jorn_SortTabs.sublime-settings')
    INTERNAL_SETTINGS = InternalSettings('Jorn_SortTabsInternal.sublime-settings')

