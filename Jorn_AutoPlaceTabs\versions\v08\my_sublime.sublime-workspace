{"auto_complete": {"selected_items": []}, "buffers": [{"file": "my_sublime.sublime-project", "settings": {"buffer_size": 309, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[2, 1, "paste", null, "AgAAAAAAAAAAAAAAMwEAAAAAAAAAAAAAMwEAAAAAAAAzAQAAAAAAAC0AAAB7CgkiZm9sZGVycyI6CglbCgkJewoJCQkicGF0aCI6ICIuIgoJCX0KCV0KfQo", "AQAAAAAAAAABAAAAAAAAAAAAAAAtAAAAAAAAAAAAAAAAAPC/"], [7, 1, "insert", {"characters": "My"}, "AwAAAE8AAAAAAAAAUAAAAAAAAAAAAAAAUAAAAAAAAABQAAAAAAAAABEAAABKb3JuX0FwcEludGVyZmFjZVAAAAAAAAAAUQAAAAAAAAAAAAAA", "AQAAAAAAAAABAAAATwAAAAAAAABgAAAAAAAAAAAAAAAAAPC/"], [8, 1, "insert", {"characters": " Sublime"}, "CAAAAFEAAAAAAAAAUgAAAAAAAAAAAAAAUgAAAAAAAABTAAAAAAAAAAAAAABTAAAAAAAAAFQAAAAAAAAAAAAAAFQAAAAAAAAAVQAAAAAAAAAAAAAAVQAAAAAAAABWAAAAAAAAAAAAAABWAAAAAAAAAFcAAAAAAAAAAAAAAFcAAAAAAAAAWAAAAAAAAAAAAAAAWAAAAAAAAABZAAAAAAAAAAAAAAA", "AQAAAAAAAAABAAAAUQAAAAAAAABRAAAAAAAAAAAAAAAAAPC/"], [9, 1, "insert", {"characters": " Packages"}, "CQAAAFkAAAAAAAAAWgAAAAAAAAAAAAAAWgAAAAAAAABbAAAAAAAAAAAAAABbAAAAAAAAAFwAAAAAAAAAAAAAAFwAAAAAAAAAXQAAAAAAAAAAAAAAXQAAAAAAAABeAAAAAAAAAAAAAABeAAAAAAAAAF8AAAAAAAAAAAAAAF8AAAAAAAAAYAAAAAAAAAAAAAAAYAAAAAAAAABhAAAAAAAAAAAAAABhAAAAAAAAAGIAAAAAAAAAAAAAAA", "AQAAAAAAAAABAAAAWQAAAAAAAABZAAAAAAAAAAAAAAAAAPC/"]]}, {"file": "Packages/Jorn_AutoPlaceTabs/.python-version", "redo_stack": [[3, 1, "insert", {"characters": "13"}, "AwAAAAMAAAAAAAAAAwAAAAAAAAABAAAAMwMAAAAAAAAABAAAAAAAAAAAAAAAAgAAAAAAAAACAAAAAAAAAAEAAAAx", "AQAAAAAAAAABAAAABAAAAAAAAAAEAAAAAAAAAAAAAAAAAPC/"]], "settings": {"buffer_size": 4, "line_ending": "Windows"}, "undo_stack": []}, {"file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoSortTabs.py", "settings": {"buffer_size": 3185, "line_ending": "Windows"}}], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["min", "Jorn - Minify Text"], ["unm", "Jorn - Unminify Text"], ["tab", "Table Editor: Enable for current view"], ["org", "Jorn: Organize Views by Directory"], ["MIN", "Jorn - Minify Text"], ["previe", "Markdown Preview: Preview in Browser"], ["prev", "Permute Lines: Reverse"]], "width": 0.0}, "console": {"height": 290.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/Packages", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/Packages/Jorn_AutoPlaceTabs", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs"], "file_history": ["/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/src/main.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.30-kl.11.44.32--sequence-3034--gpt-4.1.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3042-context--intent_amplifier.5.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/project_files/py.codeguidelines.jinja-md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0001--hybrid/src/playground/litellm_list_all_models.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/md/8980-a-runway_prompt_generator.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/ai_systems.0011.sublime-workspace", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/techstack.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/debug_system_instruction.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/ai_systems.0011.sublime-project", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/.vscode/Augment-Memories.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/working_systems/working_systems.dirtree.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9298-f-quality_compliance_final_formatter.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9298-d-compose_80_tegn_keyword_optimizer.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9298-a-company_reference_injection.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9299-a-service_reference.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9299-b-parse_and_map.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9300-d-engagement_enhancer.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9300-a-seo_localizer.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/md/3034-b-service_taxonomy_integration.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_SublimeTabOrganizer/tab_organization.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3034-context--ringerikelandskap_seo.x.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3031-expanders--problem_exploder.1.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3033-translator--norwegian_english_translator.1.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3032-translator--english_norwegian_translator.1.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3035-context--seo_convergence.x.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/lvl1.md.templates.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/lvl1_md_to_json.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-001-b.kl09__len[37].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-001-a.kl09__len[1254].py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-002-b.kl09__len[39].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-002-a.kl09__len[70].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-003-d.kl11__len[83].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-003-c.kl11__len[26].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-003-b.kl11__len[40].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-003-a.kl11__len[38].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3002-compressors.good--title_extractor.4.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3030-compressors.great--singular_value.1.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.history.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3020-enhancers--wip.x.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/md/3005-a-instruction_converter.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0007--wip/src/templates/stage2/md/2002-a-norwegian_translator.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage2/generators/2000-2099.todo.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/md/3020-a-problem_exploder.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0001--hybrid/src/templates/lvl1/md/0041-a-singular_value_maximizer.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.30-kl.09.45.02--sequence-3003+3100-a-c+3022+3005--gpt-4.1.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Chrome/wip_chrome_ext 1.md", "/C/Users/<USER>/AppData/Local/Temp/tmpBAAC.tmp.tsx", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.06.30-kl.08.27--rl-website-v2-006/docs/.new_hashes.py", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.06.30-kl.08.27--rl-website-v2-006/docs/complete-architecture-analysis.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/sub-prompts-notes.py", "/C/Users/<USER>/AppData/Local/Temp/tmpC05B.tmp.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.28-kl.12.16.35--sequence-2701-a+3017+3016+3003-a+3001-a+3003-b+3001-a+3003+3017+3016--gpt-4.1.good.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3004-compressors.good--form_classifier.4.history.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9200-c-framework_mapper.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3011-rephrasers.good--precision_refiner.6.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3015-transformers.--prompt_generalizer.5.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9200-d-generalization_amplifier.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9200-e-template_synthesizer.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.06.28-kl.10.28--/lvl1_sequence_executor.py", "/?NAS/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.sync-conflict-20250628-101836-IOCXSFF.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.sync-conflict-20250622-211334-IEE7KQY.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.sync-conflict-20250622-210857-IEE7KQY.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.27-kl.16.45.31--sequence-3001-a+3003-a+3001-a+3003-b+3001-a+3003-c-d+3100-a-c+3001-a+3000+3100-a-c--gpt-4.1.GREAT.GREAT.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_LOG.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/firma/arbeidskontrakt/arbeidskontrakt_ringerike_landskap.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.22-kl.20.22.09--sequence-sequence-3001--gpt-4.1.json", "/C/Users/<USER>/Desktop/my/flow/android/prj/helserelatert/mcp/Full Process for Coding with AI Coding Assistants - Agent Edition.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_SCRATCHPAD.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_NOTES.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage1/generators/1500-1599.innovators.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0004--hybrid-instructions/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/JORN_ENVIRONMENT_VARIABLES.bat", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0007--wip/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0005--hybrid/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0007--wip/src/output/history--2025.06.15-kl.01.00.03--sequence-sequence-1100+1300+1204+1100+1300+1204+1100--gpt-4.1.good.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Batch/todo/choco-install-my.bat", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Batch/Cmd - Personal - Preferences/bat__SetCustomPrefs_Win11.bat"], "find": {"height": 55.3333333333}, "find_in_files": {"height": 184.0, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": [], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "my_sublime.sublime-project", "semi_transient": false, "settings": {"buffer_size": 309, "regions": {}, "selection": [[98, 98]], "settings": {"apply_syntax_touched": true, "color_helper.scan": {"allow_scanning": false, "color_class": [{"class": "css-level-4", "scopes": ""}], "color_trigger": "(?xi)\n(?:\n    \\b(?<![-#&$])(?:\n        color\\((?!\\s*-)|(?:hsla?|(?:ok)?(?:lch|lab)|hwb|rgba?)\\(\n) |\n\\b(?<![-#&$])[\\w]{3,}(?![(-])\\b|(?<![&])\\#)\n", "current_ext": ".sublime-project", "current_syntax": "JSON/JSON", "enabled": true, "last_updated": 1753181817.95, "scanning": "-comment"}, "sorttabs_lastactivated": 1753181880.21, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 0, "tab_activation_time": 1753181880, "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 2, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "Packages/Jorn_AutoPlaceTabs/.python-version", "selected": true, "semi_transient": false, "settings": {"buffer_size": 4, "regions": {}, "selection": [[2, 3]], "settings": {"apply_syntax_touched": true, "color_helper.scan": {"allow_scanning": false, "color_class": [{"class": "css-level-4", "scopes": ""}], "color_trigger": "(?xi)\n(?:\n    \\b(?<![-#&$])(?:\n        color\\((?!\\s*-)|(?:hsla?|(?:ok)?(?:lch|lab)|hwb|rgba?)\\(\n) |\n\\b(?<![-#&$])[\\w]{3,}(?![(-])\\b|(?<![&])\\#)\n", "current_ext": "", "current_syntax": "Text/Plain text", "enabled": true, "last_updated": 1753181817.95, "scanning": "-comment"}, "sorttabs_lastactivated": 1753182063.3, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 147, "tab_activation_time": 1753182063}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 2, "file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoSortTabs.py", "semi_transient": true, "settings": {"buffer_size": 3185, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.scan": {"allow_scanning": false, "color_class": [{"class": "css-level-4", "scopes": ""}], "color_trigger": "(?xi)\n(?:\n    \\b(?<![-#&$])(?:\n        color\\((?!\\s*-)|(?:hsla?|(?:ok)?(?:lch|lab)|hwb|rgba?)\\(\n) |\n\\b(?<![-#&$])[\\w]{3,}(?![(-])\\b|(?<![&])\\#)\n", "current_ext": ".py", "current_syntax": "Python/Python", "enabled": true, "last_updated": 1753181817.95, "scanning": "-comment"}, "sorttabs_lastactivated": 1753182027.79, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 6, "tab_activation_time": 1753182027, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 31.3333333333}, "input": {"height": 64.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.find_results": {"height": 0.0}, "output.project_environment_log": {"height": 0.0}, "pinned_build_system": "", "project": "my_sublime.sublime-project", "replace": {"height": 58.6666666667}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["8980", "ai_systems.0008--wip\\src\\templates\\stage3\\md\\8980-a-runway_prompt_generator.md"], ["litellm_list_all_models.py", "ai_systems.0001--hybrid\\src\\playground\\litellm_list_all_models.py"], ["litellm_list_all_models", "ai_systems.0001--hybrid\\src\\playground\\litellm_list_all_models.py"], ["929", "ai_systems.0010--consolidated\\src\\lvl1\\templates\\md\\9299-a-service_reference.md"], ["transl", "ai_systems.0007--wip\\src\\templates\\stage2\\md\\2002-a-norwegian_translator.md"], ["explo", "ai_systems.0008--wip\\src\\templates\\stage3\\md\\3020-a-problem_exploder.md"], ["value", "ai_systems.0001--hybrid\\src\\templates\\lvl1\\md\\0041-a-singular_value_maximizer.md"], ["9300", "ai_systems.0010--consolidated\\src\\lvl1\\templates\\md\\9300-a-seo_localizer.md"]], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": false, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 740.0, "status_bar_visible": true, "template_settings": {}}