{
    // Enable automatic tab placement when files are opened
    "auto_place_on_load": true,

    // Enable automatic tab placement when tabs are activated
    "auto_place_on_activation": true,

    // Enable debug output to console
    "enable_debug_prints": true,

    // How to sort tabs within each group
    "group_sort_method": "append",



    // Patterns to exclude from automatic placement
    "exclude_patterns": [
        "*.tmp",
        "*/temp/*",
        "Untitled*"
    ],

    // Layout management
    "auto_adjust_layout": true,

    // Available options for layout settings
    "layout_options": {
        "missing_group_behavior": {
            "skip": "Don't place tab if target group doesn't exist",
            "last_group": "Place in the rightmost existing group",
            "first_group": "Place in the leftmost existing group"
        },
        "layout_mode": {
            "compact": "Only create groups for tabs that actually exist",
            "literal": "Create groups exactly as specified in rules"
        },
        "layout_type": {
            "columns": "Arrange groups as vertical columns",
            "rows": "Arrange groups as horizontal rows",
            "grid": "Arrange groups in a grid pattern"
        }
    },

    // Current layout settings (choose from options above)
    "missing_group_behavior": "skip",
    "layout_mode": "compact",
    "layout_type": "columns",

    // Custom layouts for specific group counts
    "layout_configs": {
        "8": {
            "cols": [0.0, 0.33, 0.66, 1.0],
            "rows": [0.0, 0.33, 0.66, 1.0],
            "cells": [
                [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],
                [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2],
                [0, 2, 1, 3], [1, 2, 2, 3]
            ]
        }
    },

    // SEMANTIC RULE SYSTEM (primary)
    "defined_types": {
        "unsaved":   "Tab has never been saved to disk",
        "dirty":     "Tab has unsaved changes",
        "project":   "File is inside a project folder",
        "external":  "File is outside all project folders",
        "scratch":   "Scratch buffer (not file-backed)",
        "readonly":  "Tab is read-only"
    },

    "group_rules": {
        // "0": [
        //     {
        //         "description": "Project Python source files",
        //         "match": {
        //             "extensions": [".py", ".pyw"],
        //             "directory_patterns": ["*/src/*", "*/lib/*"],
        //             "types": ["project"]
        //         },
        //         "exclude": {
        //             "file_name_patterns": ["test_*.py", "__init__.py"]
        //         }
        //     }
        // ],
        // "1": [
        //     {
        //         "description": "Project JavaScript/TypeScript files",
        //         "match": {
        //             "extensions": [".js", ".ts", ".jsx", ".tsx"],
        //             "types": ["project"]
        //         }
        //     },
        //     {
        //         "description": "Project test files",
        //         "match": {
        //             "file_name_patterns": ["test_*.py", "*.test.js", "*.spec.ts"],
        //             "directory_patterns": ["*/tests/*", "*/test/*"],
        //             "types": ["project"]
        //         }
        //     }
        // ],
        // "2": [
        //     {
        //         "description": "Project HTML/CSS/Vue files",
        //         "match": {
        //             "extensions": [".html", ".css", ".vue"],
        //             "types": ["project"]
        //         }
        //     },
        //     {
        //         "description": "Project documentation",
        //         "match": {
        //             "extensions": [".md", ".txt"],
        //             "directory_patterns": ["*/docs/*", "*/documentation/*"],
        //             "types": ["project"]
        //         }
        //     },
        //     {
        //         "description": "External files",
        //         "match": {
        //             "types": ["external"]
        //         }
        //     }
        // ],
        // "3": [
        //     {
        //         "description": "Configuration files",
        //         "match": {
        //             "extensions": [".json", ".yaml", ".yml", ".toml", ".ini"],
        //             "file_name_patterns": ["*.config.*", ".*rc"]
        //         }
        //     },
        //     {
        //         "description": "Unsaved and scratch files",
        //         "match": {
        //             "types": ["unsaved", "scratch"]
        //         }
        //     }
        // ]
        "3": [
            {
                "description": "test_md",
                "match": {
                    "extensions": [".md"]
                }
            }
        ],
        "5": [
            {
                "description": "test_py",
                "match": {
                    "extensions": [".py"]
                }
            }
        ],
        "6": [
            {
                "description": "test_txt",
                "match": {
                    "extensions": [".txt"]
                }
            }
        ]
    }
}
