# Project-Specific Settings Guide

## Overview

The `Jorn_AutoPlaceTabs` plugin now supports project-specific settings, allowing you to customize tab placement rules for each project while maintaining sensible global defaults.

## How It Works

1. **Global Settings**: Defined in `Jorn_AutoPlaceTabs.sublime-settings`
2. **Project Settings**: Defined in your `.sublime-project` file under the `"jorn_auto_place_tabs"` key
3. **Precedence**: Project settings override global settings for that specific project

## Setting Up Project-Specific Rules

### Method 1: Use the Command (Recommended)

1. Open your project in Sublime Text
2. Run `Jorn AutoPlace: Create Project Settings` from the Command Palette
3. This creates a template in your project file that you can customize

### Method 2: Manual Setup

Add a `"jorn_auto_place_tabs"` section to your `.sublime-project` file:

```json
{
    "folders": [
        {
            "path": "."
        }
    ],
    "settings": {
        "jorn_auto_place_tabs": {
            "auto_place_on_activation": true,
            "file_type_rules": {
                "0": [".py", ".pyw"],
                "1": [".js", ".ts", ".jsx", ".tsx"],
                "2": [".html", ".css", ".scss"],
                "3": [".md", ".txt", ".json"]
            },
            "directory_rules": {
                "0": ["*/src/*", "*/lib/*"],
                "1": ["*/tests/*", "*/test/*"],
                "2": ["*/docs/*"],
                "3": ["*/config/*"]
            }
        }
    }
}
```

## Common Use Cases

### Web Development Project
```json
"jorn_auto_place_tabs": {
    "file_type_rules": {
        "0": [".js", ".ts", ".jsx", ".tsx"],
        "1": [".html", ".vue", ".svelte"],
        "2": [".css", ".scss", ".sass", ".less"],
        "3": [".json", ".md", ".txt"]
    },
    "directory_rules": {
        "0": ["*/src/*", "*/components/*"],
        "1": ["*/tests/*", "*/__tests__/*"],
        "2": ["*/public/*", "*/assets/*"],
        "3": ["*/config/*", "*/docs/*"]
    }
}
```

### Python Project
```json
"jorn_auto_place_tabs": {
    "file_type_rules": {
        "0": [".py", ".pyw"],
        "1": [".yaml", ".yml", ".json", ".toml"],
        "2": [".md", ".rst", ".txt"],
        "3": [".sh", ".bat", "Makefile"]
    },
    "custom_rules": [
        {
            "name": "Test files",
            "pattern": "*test*.py",
            "group": 1,
            "priority": 20
        },
        {
            "name": "Config files",
            "pattern": "{setup.py,pyproject.toml,requirements.txt}",
            "group": 2,
            "priority": 15
        }
    ]
}
```

### Multi-Language Project
```json
"jorn_auto_place_tabs": {
    "directory_rules": {
        "0": ["*/backend/*", "*/api/*"],
        "1": ["*/frontend/*", "*/web/*"],
        "2": ["*/mobile/*", "*/app/*"],
        "3": ["*/docs/*", "*/config/*", "*/scripts/*"]
    },
    "auto_adjust_layout": true,
    "max_groups": 4
}
```

## Advanced Features

### Custom Rules with Priority
```json
"custom_rules": [
    {
        "name": "Critical config files",
        "pattern": "{package.json,Dockerfile,docker-compose.yml}",
        "group": 0,
        "priority": 25
    },
    {
        "name": "Test files",
        "pattern": "*{test,spec}*",
        "group": 2,
        "priority": 20
    }
]
```

### Layout Management
```json
// Option 1: Respect existing layout (recommended)
"auto_adjust_layout": false,
"missing_group_behavior": "skip"

// Option 2: Create groups as needed
"auto_adjust_layout": true,
"max_groups": 4

// Option 3: Fallback to existing groups
"auto_adjust_layout": false,
"missing_group_behavior": "last_group"
```

**Missing Group Behaviors:**
- `"skip"`: Don't place tabs if target group doesn't exist (respects your layout)
- `"last_group"`: Place in rightmost existing group
- `"first_group"`: Place in leftmost existing group

### Layout Customization
```json
"layout_configs": {
    "3": {
        "cols": [0.0, 0.3, 0.7, 1.0],
        "rows": [0.0, 1.0],
        "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1]]
    }
}
```

## Commands

- **Show Current Rules**: View effective settings (global + project overrides)
- **Create Project Settings**: Generate a template in your project file
- **Reload Settings**: Refresh settings after manual changes

## Common Scenarios

### Respect Existing Layout (Recommended)
When working with many files/types/paths across different Sublime layouts, prevent tabs from always appearing in the last active group:

```json
"jorn_auto_place_tabs": {
    "auto_adjust_layout": false,
    "missing_group_behavior": "skip",
    "file_type_rules": {
        "0": [".py", ".js", ".ts"],
        "1": [".html", ".css", ".vue"],
        "2": [".md", ".txt", ".json"],
        "3": [".yaml", ".yml", ".toml"]
    }
}
```

This keeps your layout organized without forcing new groups when you have a specific layout preference.

### Dynamic Layout Creation
For projects where you want automatic group creation:

```json
"jorn_auto_place_tabs": {
    "auto_adjust_layout": true,
    "max_groups": 4,
    "file_type_rules": {
        "0": [".py"], "1": [".js"], "2": [".css"], "3": [".md"]
    }
}
```

### Fallback to Existing Groups
When you want placement but within existing groups only:

```json
"jorn_auto_place_tabs": {
    "auto_adjust_layout": false,
    "missing_group_behavior": "last_group",
    "file_type_rules": {
        "0": [".py"], "1": [".js"], "2": [".css"]
    }
}
```

## Tips

1. **Start Simple**: Begin with basic file type rules, then add complexity
2. **Use Priorities**: Higher priority custom rules override lower priority ones
3. **Test Incrementally**: Add rules gradually and test with `Show Current Rules`
4. **Debug Mode**: Enable `"enable_debug_prints": true` to see placement decisions
5. **Layout Respect**: Use `"auto_adjust_layout": false` to respect your existing layout
6. **Fallback Strategy**: Choose appropriate `missing_group_behavior` for your workflow

## Troubleshooting

- **Settings Not Applied**: Run `Reload Settings` after manual changes
- **Unexpected Placement**: Check rule priority and use `Show Current Rules`
- **Performance Issues**: Reduce complex patterns or disable debug prints
- **Layout Problems**: Verify layout configurations are valid JSON

## Integration with Other Plugins

Project-specific settings work seamlessly with other Jorn plugins:
- `Jorn_AutosortTabs`: Handles sorting within groups
- `Jorn_TabUtils`: Provides additional tab management
- `Jorn_OrganizeViewsByDirectory`: Complements directory-based organization
