# Compact vs Literal Layout Mode

## The Problem

You have these rules defined:
```json
{
    "project_files_group": 0,
    "external_files_group": 1, 
    "unsaved_files_group": 2,
    "file_type_rules": {
        "7": [".md", ".txt", ".json"]
    }
}
```

But you currently only have:
- Some Python files (project_files_group = 0)
- Some unsaved files (unsaved_files_group = 2)

## Layout Mode Comparison

### Literal Mode (`"layout_mode": "literal"`)
**Creates groups exactly as specified:**
```
[Group 0: Python files] [Group 1: EMPTY] [Group 2: Unsaved files] [Group 3: EMPTY] ... [Group 7: EMPTY]
```
- **Result**: 8 groups total, 5 of them empty
- **Problem**: Wastes screen space with empty groups

### Compact Mode (`"layout_mode": "compact"`) ✅
**Only creates groups for tabs that actually exist:**
```
[Group 0: Python files] [Group 1: Unsaved files]
```
- **Result**: 2 groups total, both used
- **Benefit**: Efficient use of screen space

## How Compact Mode Works

### 1. Analyze Current Tabs
```python
# Plugin scans all open tabs and finds:
used_groups = {0, 2}  # Only groups 0 and 2 have tabs
```

### 2. Create Compact Mapping
```python
# Maps logical groups to physical positions:
logical_group_0 -> physical_group_0  # Python files
logical_group_2 -> physical_group_1  # Unsaved files
```

### 3. Create Minimal Layout
```python
# Creates layout with only 2 groups instead of 8
layout = create_columns_layout(2)  # Just what's needed
```

## Configuration Examples

### Compact Mode (Recommended)
```json
{
    "jorn_auto_place_tabs": {
        "project_files_group": 0,
        "external_files_group": 1,
        "unsaved_files_group": 2,
        "file_type_rules": {
            "7": [".md", ".txt", ".json"]
        },
        "auto_adjust_layout": true,
        "layout_mode": "compact",  // Only create needed groups
        "layout_type": "columns"
    }
}
```

### Literal Mode (When You Want Exact Positions)
```json
{
    "jorn_auto_place_tabs": {
        "project_files_group": 0,
        "external_files_group": 1,
        "unsaved_files_group": 2,
        "file_type_rules": {
            "7": [".md", ".txt", ".json"]
        },
        "auto_adjust_layout": true,
        "layout_mode": "literal",  // Create all specified groups
        "layout_type": "columns"
    }
}
```

## Use Cases

### When to Use Compact Mode ✅
- **Most scenarios** - efficient screen usage
- **Dynamic workflows** - tab types change frequently  
- **Small screens** - maximize usable space
- **Flexible layouts** - adapt to current content

### When to Use Literal Mode
- **Fixed workflows** - always use the same tab types
- **Consistent positioning** - want group 7 to always be in position 7
- **Muscle memory** - used to specific group positions
- **Large screens** - empty groups don't matter

## Real-World Example

**Your Scenario:**
```json
{
    "project_files_group": 0,    // Currently has: main.py, utils.py
    "external_files_group": 1,   // Currently has: (nothing)
    "unsaved_files_group": 2     // Currently has: Untitled-1
}
```

**Compact Mode Result:**
```
[Group 0: main.py, utils.py] [Group 1: Untitled-1]
```
- 2 groups, both useful
- Clean, efficient layout

**Literal Mode Result:**
```
[Group 0: main.py, utils.py] [Group 1: EMPTY] [Group 2: Untitled-1]
```
- 3 groups, 1 empty
- Wastes middle column

## Commands

- **Place All Tabs**: Automatically uses compact layout if enabled
- **Show Current Rules**: Displays current layout mode
- **Test Layout**: Preview both compact and literal layouts

The compact mode makes the plugin **drastically more usable** by adapting the layout to your actual workflow instead of forcing you into predefined group structures!
