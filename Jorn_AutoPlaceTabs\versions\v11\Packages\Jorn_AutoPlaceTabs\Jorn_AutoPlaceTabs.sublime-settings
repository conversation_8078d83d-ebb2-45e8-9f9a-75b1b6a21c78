{
    // ========================================================================
    // GLOBAL SETTINGS
    // These settings apply to all projects unless overridden in project files
    // ========================================================================

    // Enable/disable auto-placement when tabs are activated
    "auto_place_on_activation": true,

    // Enable/disable auto-placement when files are loaded
    "auto_place_on_load": true,

    // Enable/disable debug print statements
    "enable_debug_prints": false,
    
    // How to sort tabs within groups: "append", "prepend", "alphabetical"
    "group_sort_method": "append",
    
    // File type rules: map group indices to file patterns
    // Group indices start from 0
    "file_type_rules": {
        "0": [".py", ".pyw", "*.python"],
        "1": [".js", ".ts", ".jsx", ".tsx", ".json"],
        "2": [".html", ".htm", ".xml", ".css", ".scss", ".sass"],
        "3": [".md", ".txt", ".rst", "*.markdown"]
    },
    
    // Directory rules: map group indices to directory patterns
    // Supports glob patterns like */tests/*, */src/*, etc.
    "directory_rules": {
        "0": ["*/src/*", "*/lib/*"],
        "1": ["*/tests/*", "*/test/*"],
        "2": ["*/docs/*", "*/documentation/*"],
        "3": ["*/config/*", "*/settings/*"]
    },
    
    // Group for files that are part of the current project
    "project_files_group": 0,
    
    // Group for files outside the current project
    "external_files_group": 1,
    
    // Group for unsaved/untitled files
    "unsaved_files_group": 2,
    
    // Exclude patterns: files matching these patterns won't be auto-placed
    "exclude_patterns": [
        "*.tmp",
        "*.temp",
        "*/.git/*",
        "*/node_modules/*",
        "*/__pycache__/*",
        "*.pyc"
    ],
    
    // Custom rules: advanced pattern matching
    // Each rule has: pattern, group, priority (higher priority = checked first)
    "custom_rules": [
        {
            "name": "Configuration files",
            "pattern": "*.{json,yaml,yml,toml,ini,cfg,conf}",
            "group": 3,
            "priority": 10
        },
        {
            "name": "Documentation",
            "pattern": "*.{md,rst,txt}",
            "group": 2,
            "priority": 5
        },
        {
            "name": "Test files",
            "pattern": "*test*.py",
            "group": 1,
            "priority": 15
        }
    ],
    
    // Layout management: automatically adjust layout when placing tabs
    "auto_adjust_layout": false,

    // What to do when target group doesn't exist and auto_adjust_layout is false
    // Options: "skip", "last_group", "first_group"
    // - "skip": Don't place the tab (respects existing layout)
    // - "last_group": Place in the rightmost existing group
    // - "first_group": Place in the leftmost existing group
    "missing_group_behavior": "skip",

    // Minimum number of groups to maintain
    "min_groups": 2,

    // Maximum number of groups to create (total count, not highest index)
    // Note: To use group index 7, you need max_groups of at least 8
    "max_groups": 4,
    
    // Layout generation strategy: "auto", "columns", "rows", "grid"
    // - "auto": Automatically choose the best layout for the group count
    // - "columns": Always use horizontal columns
    // - "rows": Always use vertical rows
    // - "grid": Always use grid layouts when possible
    "layout_strategy": "auto",

    // Preferred layout configurations for specific group counts
    // These take precedence over generated layouts
    "layout_configs": {
        "2": {
            "cols": [0.0, 0.5, 1.0],
            "rows": [0.0, 1.0],
            "cells": [[0, 0, 1, 1], [1, 0, 2, 1]],
            "nickname": "Two Columns"
        },
        "3": {
            "cols": [0.0, 0.33, 0.66, 1.0],
            "rows": [0.0, 1.0],
            "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1]],
            "nickname": "Three Columns"
        },
        "4": {
            "cols": [0.0, 0.5, 1.0],
            "rows": [0.0, 0.5, 1.0],
            "cells": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],
            "nickname": "2x2 Grid"
        }
    },

    // Named layouts that can be used regardless of group count
    // The plugin will automatically select appropriate ones based on group requirements
    "named_layouts": {
        "counterclockwise": {
            "cells": [[0, 0, 1, 1], [1, 0, 2, 2], [0, 1, 1, 2]],
            "cols": [0.0, 0.5, 1.0],
            "rows": [0.0, 0.114307153577, 1.0],
            "nickname": "Counterclockwise"
        },
        "special_ringlandskap": {
            "cells": [
                [0, 0, 1, 2],
                [1, 1, 2, 3],
                [1, 0, 3, 1],
                [2, 1, 3, 3],
                [0, 2, 1, 3]
            ],
            "cols": [0.0, 0.469470218497, 0.75, 1.0],
            "rows": [0.0, 0.364111498258, 0.621118012422, 1.0],
            "nickname": "Special Ringlandskap"
        },
        "special_small": {
            "cells": [
                [0, 0, 2, 1],
                [2, 0, 3, 4],
                [0, 1, 1, 3],
                [0, 3, 2, 4],
                [1, 1, 2, 2],
                [1, 2, 2, 3]
            ],
            "cols": [0.0, 0.43973442288, 0.879468845761, 1.0],
            "rows": [0.0, 0.0533049040512, 0.5, 0.946695095949, 1.0],
            "nickname": "Special Small"
        },
        "special_large": {
            "cells": [
                [0, 0, 1, 1],
                [0, 1, 1, 3],
                [0, 3, 1, 4],
                [1, 0, 3, 1],
                [1, 3, 3, 4],
                [3, 0, 4, 1],
                [3, 1, 4, 3],
                [3, 3, 4, 4],
                [1, 1, 2, 3],
                [2, 1, 3, 2],
                [2, 2, 3, 3]
            ],
            "cols": [0.0, 0.0228519195612, 0.524246192105, 0.977148080439, 1.0],
            "rows": [0.0, 0.0378979282466, 0.476755937342, 0.836280949974, 1.0],
            "nickname": "Special Large"
        }
    }

    // ========================================================================
    // PROJECT-SPECIFIC SETTINGS
    // ========================================================================
    //
    // You can override any of the above settings on a per-project basis by
    // adding a "jorn_auto_place_tabs" section to your .sublime-project file:
    //
    // {
    //     "folders": [
    //         {
    //             "path": "."
    //         }
    //     ],
    //     "settings": {
    //         "jorn_auto_place_tabs": {
    //             "auto_place_on_activation": true,
    //             "file_type_rules": {
    //                 "0": [".py", ".pyw"],
    //                 "1": [".js", ".ts", ".jsx", ".tsx"],
    //                 "2": [".html", ".css", ".vue"],
    //                 "3": [".md", ".txt", ".json"]
    //             },
    //             "directory_rules": {
    //                 "0": ["*/src/*", "*/lib/*"],
    //                 "1": ["*/tests/*", "*/test/*", "*/__tests__/*"],
    //                 "2": ["*/docs/*", "*/documentation/*"],
    //                 "3": ["*/config/*", "*/settings/*"]
    //             },
    //             "custom_rules": [
    //                 {
    //                     "name": "Test files",
    //                     "pattern": "*test*.py",
    //                     "group": 1,
    //                     "priority": 20
    //                 },
    //                 {
    //                     "name": "Config files",
    //                     "pattern": "*.{json,yaml,yml,toml}",
    //                     "group": 3,
    //                     "priority": 15
    //                 }
    //             ],
    //             "project_files_group": 0,
    //             "external_files_group": 2,
    //             "auto_adjust_layout": true,
    //             "max_groups": 4
    //         }
    //     }
    // }
    //
    // Project-specific settings take precedence over global settings.
    // This allows you to customize tab placement rules for each project
    // while maintaining sensible defaults globally.
}
