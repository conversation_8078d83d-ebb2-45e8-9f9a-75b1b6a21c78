import sublime
import sublime_plugin
import os
import time
import fnmatch
from collections import defaultdict, deque

PLUGIN_NAME = "Jorn_AutoPlaceTabs"
MAX_PLACEMENTS_PER_SECOND = 5


class Jorn_AutoPlaceTabsCommand(sublime_plugin.EventListener):
    """
    Main plugin that automatically places tabs in appropriate groups based on:
    - File type/extension patterns
    - Directory patterns  
    - Project membership
    - Custom user-defined rules
    
    Prevents infinite loops via:
    1) Recursion guard (_is_placing)
    2) Frequency-based limit (MAX_PLACEMENTS_PER_SECOND)
    3) Placement history tracking
    """

    _instance = None

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        self._is_placing = False
        self._placement_timestamps = deque()
        self._last_placements = defaultdict(lambda: defaultdict(tuple))
        self._project_settings_cache = {}  # Cache project settings by window ID
        Jorn_AutoPlaceTabsCommand._instance = self

    @classmethod
    def instance(cls):
        """Used by manual placement commands to reference this plugin instance."""
        return cls._instance

    def _debug_print(self, message):
        """Print debug message only if debug mode is enabled."""
        if self.settings.get("enable_debug_prints", False):
            print(f"[{PLUGIN_NAME}] {message}")

    def _get_effective_settings(self, window):
        """Get effective settings combining global and project-specific settings."""
        if not window:
            return self.settings

        window_id = window.id()

        # Check cache first
        if window_id in self._project_settings_cache:
            return self._project_settings_cache[window_id]

        # Start with global settings
        effective_settings = {}

        # Copy all global settings
        for key in ["auto_place_on_activation", "auto_place_on_load", "enable_debug_prints",
                   "group_sort_method", "file_type_rules", "directory_rules", "project_files_group",
                   "external_files_group", "unsaved_files_group", "exclude_patterns", "custom_rules",
                   "auto_adjust_layout", "missing_group_behavior", "min_groups", "max_groups",
                   "layout_strategy", "layout_configs", "named_layouts"]:
            effective_settings[key] = self.settings.get(key)

        # Get project-specific settings
        project_data = window.project_data()
        if project_data and "settings" in project_data:
            project_settings = project_data["settings"].get("jorn_auto_place_tabs", {})
            if project_settings:
                self._debug_print(f"Found project-specific settings: {list(project_settings.keys())}")
                # Override global settings with project-specific ones
                effective_settings.update(project_settings)

        # Cache the result
        self._project_settings_cache[window_id] = effective_settings

        return effective_settings

    def _clear_settings_cache(self, window_id=None):
        """Clear settings cache for a specific window or all windows."""
        if window_id:
            self._project_settings_cache.pop(window_id, None)
        else:
            self._project_settings_cache.clear()

    def _get_setting(self, key, default=None, window=None):
        """Get a setting value from effective settings (global + project-specific)."""
        if window:
            effective_settings = self._get_effective_settings(window)
            return effective_settings.get(key, default)
        else:
            return self.settings.get(key, default)

    def _check_placement_frequency(self):
        """Rate limiting to prevent excessive placements."""
        now = time.time()
        self._placement_timestamps.append(now)
        
        # Remove timestamps older than 1 second
        while (self._placement_timestamps and 
               now - self._placement_timestamps[0] > 1.0):
            self._placement_timestamps.popleft()
        
        return len(self._placement_timestamps) <= MAX_PLACEMENTS_PER_SECOND

    def on_activated_async(self, view):
        """Handle tab activation for auto-placement."""
        if not view or not view.window() or self._is_placing:
            return

        window = view.window()
        if not self._get_setting("auto_place_on_activation", True, window):
            return

        if not self._should_auto_place(view):
            return

        if not self._check_placement_frequency():
            self._debug_print("Rate limit exceeded, skipping placement")
            return

        self._place_tab(view)

    def on_load_async(self, view):
        """Handle file load for auto-placement."""
        if not view or not view.window() or self._is_placing:
            return

        window = view.window()
        if not self._get_setting("auto_place_on_load", True, window):
            return

        if not self._should_auto_place(view):
            return

        if not self._check_placement_frequency():
            return

        # Small delay to ensure file is fully loaded
        sublime.set_timeout_async(lambda: self._place_tab(view), 100)

    def on_window_command(self, window, command_name, args):
        """Handle window commands that might change project settings."""
        if command_name in ["open_project", "close_project", "switch_project"]:
            # Clear settings cache when project changes
            self._clear_settings_cache(window.id())

    def on_post_window_command(self, window, command_name, args):
        """Handle post-window commands that might change project settings."""
        if command_name in ["open_project", "close_project", "switch_project"]:
            # Clear settings cache when project changes
            self._clear_settings_cache(window.id())

    def _should_auto_place(self, view):
        """Determine if a view should be auto-placed."""
        if not view or not view.window():
            return False

        # Skip if already in correct group
        target_group = self._determine_target_group(view)
        if target_group is None:
            return False

        current_group, _ = view.window().get_view_index(view)
        return current_group != target_group

    def _determine_target_group(self, view):
        """Determine the target group for a view based on placement rules."""
        window = view.window()
        if not window:
            return None

        file_path = view.file_name()
        if not file_path:
            return self._get_group_for_unsaved(view)

        # Check if file should be excluded
        if self._should_exclude_file(file_path, window):
            return None

        # Check custom rules first (highest priority)
        target_group = self._check_custom_rules(file_path, window)
        if target_group is not None:
            return target_group

        # Check file type rules
        target_group = self._check_file_type_rules(file_path, window)
        if target_group is not None:
            return target_group

        # Check directory rules
        target_group = self._check_directory_rules(file_path, window)
        if target_group is not None:
            return target_group

        # Check project membership
        target_group = self._check_project_rules(view, file_path, window)
        if target_group is not None:
            return target_group

        return None

    def _check_file_type_rules(self, file_path, window=None):
        """Check file type/extension rules."""
        file_ext = os.path.splitext(file_path)[1].lower()
        file_name = os.path.basename(file_path)

        type_rules = self._get_setting("file_type_rules", {}, window)

        for group_index, patterns in type_rules.items():
            try:
                group_num = int(group_index)
                for pattern in patterns:
                    if (pattern.startswith('.') and file_ext == pattern.lower()) or \
                       fnmatch.fnmatch(file_name.lower(), pattern.lower()):
                        return group_num
            except (ValueError, TypeError):
                continue

        return None

    def _check_directory_rules(self, file_path, window=None):
        """Check directory-based rules."""
        dir_path = os.path.dirname(file_path)
        dir_rules = self._get_setting("directory_rules", {}, window)

        for group_index, patterns in dir_rules.items():
            try:
                group_num = int(group_index)
                for pattern in patterns:
                    if fnmatch.fnmatch(dir_path, pattern):
                        return group_num
            except (ValueError, TypeError):
                continue

        return None

    def _check_project_rules(self, view, file_path, window=None):
        """Check project membership rules."""
        if not window:
            window = view.window()
        project_folders = window.folders() if window else []

        is_in_project = any(file_path.startswith(folder) for folder in project_folders)

        if is_in_project:
            return self._get_setting("project_files_group", None, window)
        else:
            return self._get_setting("external_files_group", None, window)

    def _get_group_for_unsaved(self, view):
        """Determine group for unsaved files."""
        window = view.window()
        return self._get_setting("unsaved_files_group", None, window)

    def _should_exclude_file(self, file_path, window=None):
        """Check if file should be excluded from auto-placement."""
        exclude_patterns = self._get_setting("exclude_patterns", [], window)
        file_name = os.path.basename(file_path)

        for pattern in exclude_patterns:
            if fnmatch.fnmatch(file_path, pattern) or fnmatch.fnmatch(file_name, pattern):
                return True
        return False

    def _check_custom_rules(self, file_path, window=None):
        """Check custom rules with priority ordering."""
        custom_rules = self._get_setting("custom_rules", [], window)
        if not custom_rules:
            return None

        # Sort by priority (higher first)
        sorted_rules = sorted(custom_rules, key=lambda r: r.get("priority", 0), reverse=True)

        file_name = os.path.basename(file_path)
        for rule in sorted_rules:
            pattern = rule.get("pattern", "")
            if pattern and (fnmatch.fnmatch(file_name, pattern) or fnmatch.fnmatch(file_path, pattern)):
                return rule.get("group")

        return None

    def _place_tab(self, view):
        """Place a tab in its target group."""
        if self._is_placing:
            return

        window = view.window()
        if not window:
            return

        target_group = self._determine_target_group(view)
        if target_group is None:
            return

        current_group, current_index = window.get_view_index(view)
        
        # Ensure target group exists, create if needed
        if target_group >= window.num_groups():
            if self._get_setting("auto_adjust_layout", False, window):
                self._ensure_group_exists(window, target_group)
            else:
                # Target group doesn't exist and auto-layout is disabled
                fallback_behavior = self._get_setting("missing_group_behavior", "skip", window)

                if fallback_behavior == "skip":
                    self._debug_print(f"Target group {target_group} doesn't exist (only {window.num_groups()} groups), skipping")
                    return
                elif fallback_behavior == "last_group":
                    target_group = window.num_groups() - 1
                    self._debug_print(f"Target group doesn't exist, using last group ({target_group})")
                elif fallback_behavior == "first_group":
                    target_group = 0
                    self._debug_print(f"Target group doesn't exist, using first group ({target_group})")
                else:
                    self._debug_print(f"Target group {target_group} doesn't exist, skipping (unknown fallback behavior)")
                    return

        if current_group == target_group:
            return

        self._is_placing = True
        try:
            # Determine target index within group
            target_index = self._get_target_index(view, target_group, window)
            
            self._debug_print(f"Moving tab from group {current_group} to group {target_group}, index {target_index}")
            window.set_view_index(view, target_group, target_index)
            
            # Track this placement
            self._last_placements[window.id()][view.id()] = (target_group, target_index, time.time())
            
        finally:
            self._is_placing = False

    def _get_target_index(self, view, target_group, window=None):
        """Determine the target index within a group."""
        if not window:
            window = view.window()
        views_in_group = window.views_in_group(target_group)

        sort_method = self._get_setting("group_sort_method", "append", window)

        if sort_method == "prepend":
            return 0
        elif sort_method == "append":
            return len(views_in_group)
        elif sort_method == "alphabetical":
            return self._get_alphabetical_index(view, views_in_group)
        else:
            return len(views_in_group)

    def _get_alphabetical_index(self, view, views_in_group):
        """Get index for alphabetical insertion."""
        view_name = os.path.basename(view.file_name() or view.name() or "")
        
        for i, existing_view in enumerate(views_in_group):
            existing_name = os.path.basename(existing_view.file_name() or existing_view.name() or "")
            if view_name.lower() < existing_name.lower():
                return i
        
        return len(views_in_group)

    def _ensure_group_exists(self, window, target_group):
        """Ensure the target group exists by adjusting layout if needed."""
        current_groups = window.num_groups()
        self._debug_print(f"Target group: {target_group}, Current groups: {current_groups}")

        if target_group < current_groups:
            self._debug_print(f"Target group {target_group} already exists")
            return

        needed_groups = target_group + 1
        max_groups = self._get_setting("max_groups", 4, window)

        self._debug_print(f"Need {needed_groups} groups, max allowed: {max_groups}")

        if needed_groups > max_groups:
            self._debug_print(f"Cannot create group {target_group}, exceeds max_groups ({max_groups}). Note: max_groups should be at least {target_group + 1} to accommodate group {target_group}")
            return

        # Apply appropriate layout for the needed number of groups
        self._debug_print(f"Creating layout for {needed_groups} groups")
        self._apply_layout_for_groups(window, needed_groups)

    def _apply_layout_for_groups(self, window, num_groups):
        """Apply a layout configuration for the specified number of groups."""
        layout_manager = LayoutManager(window, self)

        # Try to find and apply an appropriate layout
        success = layout_manager.apply_layout_for_groups(num_groups)

        if not success:
            self._debug_print(f"Failed to apply any layout for {num_groups} groups")


class LayoutManager:
    """Manages layout selection and application for AutoPlaceTabs."""

    def __init__(self, window, plugin):
        self.window = window
        self.plugin = plugin
        self.layouts = self._load_available_layouts()

    def _load_available_layouts(self):
        """Load all available layouts from settings."""
        # Get layout configurations from settings
        layout_configs = self.plugin._get_setting("layout_configs", {}, self.window)

        # Get named layouts from settings
        named_layouts = self.plugin._get_setting("named_layouts", {}, self.window)

        # Combine both types
        all_layouts = {}
        all_layouts.update(layout_configs)
        all_layouts.update(named_layouts)

        return all_layouts

    def apply_layout_for_groups(self, num_groups):
        """Find and apply the best layout for the specified number of groups."""
        # Strategy 1: Look for exact group count match
        layout = self._find_layout_by_group_count(num_groups)
        if layout:
            return self._apply_layout(layout, f"exact match for {num_groups} groups")

        # Strategy 2: Look for named layout that can accommodate the groups
        layout = self._find_named_layout_for_groups(num_groups)
        if layout:
            return self._apply_layout(layout, f"named layout for {num_groups} groups")

        # Strategy 3: Generate a layout using layout strategy
        layout_strategy = self.plugin._get_setting("layout_strategy", "auto", self.window)
        layout = self._generate_layout_by_strategy(num_groups, layout_strategy)
        if layout:
            return self._apply_layout(layout, f"generated {layout_strategy} layout for {num_groups} groups")

        # Strategy 4: Fallback to simple columns
        layout = self._generate_simple_columns(num_groups)
        return self._apply_layout(layout, f"fallback columns layout for {num_groups} groups")

    def _find_layout_by_group_count(self, num_groups):
        """Find a layout by exact group count."""
        return self.layouts.get(str(num_groups))

    def _find_named_layout_for_groups(self, num_groups):
        """Find a named layout that can accommodate the required groups."""
        for layout_name, layout_config in self.layouts.items():
            if not layout_config.get("cells"):
                continue

            # Check if this layout has enough groups
            max_group_in_layout = max(max(cell) for cell in layout_config["cells"]) if layout_config["cells"] else 0
            layout_group_count = max_group_in_layout + 1

            if layout_group_count >= num_groups:
                # Check if it's a reasonable match (not too many extra groups)
                if layout_group_count <= num_groups + 2:  # Allow up to 2 extra groups
                    self.plugin._debug_print(f"Found named layout '{layout_name}' with {layout_group_count} groups for {num_groups} needed")
                    return layout_config

        return None

    def _generate_layout_by_strategy(self, num_groups, strategy):
        """Generate a layout using the specified strategy."""
        if strategy == "columns":
            return self._generate_simple_columns(num_groups)
        elif strategy == "rows":
            return self._generate_simple_rows(num_groups)
        elif strategy == "grid":
            return self._generate_grid_layout(num_groups)
        elif strategy == "auto":
            return self._generate_auto_layout(num_groups)
        else:
            self.plugin._debug_print(f"Unknown layout strategy: {strategy}")
            return None

    def _generate_simple_columns(self, num_groups):
        """Generate a simple horizontal columns layout."""
        if num_groups <= 1:
            return {"cols": [0.0, 1.0], "rows": [0.0, 1.0], "cells": [[0, 0, 1, 1]]}

        cols = [i / num_groups for i in range(num_groups + 1)]
        cells = [[i, 0, i + 1, 1] for i in range(num_groups)]

        return {
            "cols": cols,
            "rows": [0.0, 1.0],
            "cells": cells,
            "nickname": f"{num_groups} Columns"
        }

    def _generate_simple_rows(self, num_groups):
        """Generate a simple vertical rows layout."""
        if num_groups <= 1:
            return {"cols": [0.0, 1.0], "rows": [0.0, 1.0], "cells": [[0, 0, 1, 1]]}

        rows = [i / num_groups for i in range(num_groups + 1)]
        cells = [[0, i, 1, i + 1] for i in range(num_groups)]

        return {
            "cols": [0.0, 1.0],
            "rows": rows,
            "cells": cells,
            "nickname": f"{num_groups} Rows"
        }

    def _generate_grid_layout(self, num_groups):
        """Generate an optimal grid layout."""
        if num_groups <= 1:
            return self._generate_simple_columns(1)
        elif num_groups <= 4:
            return self._generate_2x2_grid(num_groups)
        elif num_groups <= 6:
            return self._generate_2x3_grid(num_groups)
        elif num_groups <= 9:
            return self._generate_3x3_grid(num_groups)
        else:
            # For very large numbers, fall back to columns
            return self._generate_simple_columns(num_groups)

    def _generate_auto_layout(self, num_groups):
        """Generate the best layout automatically based on group count."""
        if num_groups <= 2:
            return self._generate_simple_columns(num_groups)
        elif num_groups <= 4:
            return self._generate_2x2_grid(num_groups)
        elif num_groups <= 6:
            return self._generate_2x3_grid(num_groups)
        elif num_groups <= 9:
            return self._generate_3x3_grid(num_groups)
        else:
            # For many groups, use columns to avoid tiny panes
            return self._generate_simple_columns(num_groups)

    def _generate_2x2_grid(self, num_groups):
        """Generate a 2x2 grid layout."""
        cols = [0.0, 0.5, 1.0]
        rows = [0.0, 0.5, 1.0]
        cells = [
            [0, 0, 1, 1], [1, 0, 2, 1],  # Top row
            [0, 1, 1, 2], [1, 1, 2, 2]   # Bottom row
        ]

        return {
            "cols": cols,
            "rows": rows,
            "cells": cells[:num_groups],
            "nickname": f"2x2 Grid ({num_groups} groups)"
        }

    def _generate_2x3_grid(self, num_groups):
        """Generate a 2x3 grid layout."""
        cols = [0.0, 0.33, 0.66, 1.0]
        rows = [0.0, 0.5, 1.0]
        cells = [
            [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],  # Top row
            [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2]   # Bottom row
        ]

        return {
            "cols": cols,
            "rows": rows,
            "cells": cells[:num_groups],
            "nickname": f"2x3 Grid ({num_groups} groups)"
        }

    def _generate_3x3_grid(self, num_groups):
        """Generate a 3x3 grid layout."""
        cols = [0.0, 0.33, 0.66, 1.0]
        rows = [0.0, 0.33, 0.66, 1.0]
        cells = [
            [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],  # Top row
            [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2],  # Middle row
            [0, 2, 1, 3], [1, 2, 2, 3], [2, 2, 3, 3]   # Bottom row
        ]

        return {
            "cols": cols,
            "rows": rows,
            "cells": cells[:num_groups],
            "nickname": f"3x3 Grid ({num_groups} groups)"
        }

    def _apply_layout(self, layout_config, description):
        """Apply a layout configuration to the window."""
        try:
            # Extract layout components
            layout_dict = {
                "cols": layout_config.get("cols", [0.0, 1.0]),
                "rows": layout_config.get("rows", [0.0, 1.0]),
                "cells": layout_config.get("cells", [[0, 0, 1, 1]])
            }

            nickname = layout_config.get("nickname", "Unknown Layout")
            self.plugin._debug_print(f"Applying {description}: {nickname}")
            self.plugin._debug_print(f"Layout: {layout_dict}")

            # Apply the layout
            self.window.set_layout(layout_dict)

            # Verify the result
            actual_groups = self.window.num_groups()
            self.plugin._debug_print(f"Layout applied successfully. Actual groups: {actual_groups}")

            return True

        except Exception as e:
            self.plugin._debug_print(f"Failed to apply layout ({description}): {e}")
            return False


class JornAutoPlaceTabsManualCommand(sublime_plugin.WindowCommand):
    """Manual command to place current tab according to rules."""
    
    def run(self):
        view = self.window.active_view()
        if not view:
            return
        
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if plugin:
            plugin._place_tab(view)
            sublime.status_message("Tab placed according to rules")


class JornAutoPlaceTabsPlaceAllCommand(sublime_plugin.WindowCommand):
    """Command to place all tabs according to rules."""
    
    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return
        
        placed_count = 0
        for view in self.window.views():
            if plugin._should_auto_place(view):
                plugin._place_tab(view)
                placed_count += 1
        
        sublime.status_message(f"Placed {placed_count} tabs according to rules")


class JornAutoPlaceTabsToggleCommand(sublime_plugin.WindowCommand):
    """Toggle auto-placement on/off."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        current = plugin.settings.get("auto_place_on_activation", True)
        plugin.settings.set("auto_place_on_activation", not current)
        sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")

        status = "enabled" if not current else "disabled"
        sublime.status_message(f"Auto-placement {status}")


class JornAutoPlaceTabsReloadSettingsCommand(sublime_plugin.WindowCommand):
    """Reload plugin settings and clear project settings cache."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        plugin.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        plugin._clear_settings_cache()  # Clear all cached project settings
        sublime.status_message("AutoPlace settings reloaded")


class JornAutoPlaceTabsShowRulesCommand(sublime_plugin.WindowCommand):
    """Show current placement rules in a new view."""

    def run(self):
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return

        view = self.window.new_file()
        view.set_name("AutoPlace Rules")
        view.set_scratch(True)

        rules_text = self._format_rules(plugin.settings)
        view.run_command("append", {"characters": rules_text})
        view.set_read_only(True)

    def _format_rules(self, settings):
        """Format current rules for display."""
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if not plugin:
            return "Plugin not available"

        # Get effective settings for this window
        effective_settings = plugin._get_effective_settings(self.window)

        lines = ["# Jorn AutoPlace Tabs - Current Rules\n\n"]

        # Check if project-specific settings are active
        project_data = self.window.project_data()
        has_project_settings = (project_data and "settings" in project_data and
                               "jorn_auto_place_tabs" in project_data["settings"])

        if has_project_settings:
            lines.append("## Project-Specific Settings Active\n")
            project_settings = project_data["settings"]["jorn_auto_place_tabs"]
            lines.append(f"Project overrides: {', '.join(project_settings.keys())}\n\n")
        else:
            lines.append("## Using Global Settings Only\n\n")

        # Auto-placement status
        auto_on_activation = effective_settings.get("auto_place_on_activation", True)
        auto_on_load = effective_settings.get("auto_place_on_load", True)
        lines.append(f"Auto-placement on activation: {auto_on_activation}\n")
        lines.append(f"Auto-placement on load: {auto_on_load}\n\n")

        # File type rules
        file_type_rules = effective_settings.get("file_type_rules", {})
        if file_type_rules:
            lines.append("## File Type Rules\n")
            for group, patterns in file_type_rules.items():
                lines.append(f"Group {group}: {', '.join(patterns)}\n")
            lines.append("\n")

        # Directory rules
        dir_rules = effective_settings.get("directory_rules", {})
        if dir_rules:
            lines.append("## Directory Rules\n")
            for group, patterns in dir_rules.items():
                lines.append(f"Group {group}: {', '.join(patterns)}\n")
            lines.append("\n")

        # Custom rules
        custom_rules = effective_settings.get("custom_rules", [])
        if custom_rules:
            lines.append("## Custom Rules\n")
            for rule in sorted(custom_rules, key=lambda r: r.get("priority", 0), reverse=True):
                name = rule.get("name", "Unnamed")
                pattern = rule.get("pattern", "")
                group = rule.get("group", "?")
                priority = rule.get("priority", 0)
                lines.append(f"{name}: {pattern} → Group {group} (Priority: {priority})\n")
            lines.append("\n")

        # Special groups
        project_group = effective_settings.get("project_files_group")
        external_group = effective_settings.get("external_files_group")
        unsaved_group = effective_settings.get("unsaved_files_group")

        lines.append("## Special Groups\n")
        if project_group is not None:
            lines.append(f"Project files: Group {project_group}\n")
        if external_group is not None:
            lines.append(f"External files: Group {external_group}\n")
        if unsaved_group is not None:
            lines.append(f"Unsaved files: Group {unsaved_group}\n")

        # Layout settings
        auto_adjust = effective_settings.get("auto_adjust_layout", False)
        missing_behavior = effective_settings.get("missing_group_behavior", "skip")
        layout_strategy = effective_settings.get("layout_strategy", "auto")
        max_groups = effective_settings.get("max_groups", 4)
        sort_method = effective_settings.get("group_sort_method", "append")

        lines.append("\n## Layout Settings\n")
        lines.append(f"Auto-adjust layout: {auto_adjust}\n")
        lines.append(f"Missing group behavior: {missing_behavior}\n")
        lines.append(f"Layout strategy: {layout_strategy}\n")
        lines.append(f"Maximum groups: {max_groups}\n")
        lines.append(f"Sort method: {sort_method}\n")

        # Available layouts
        layout_configs = effective_settings.get("layout_configs", {})
        named_layouts = effective_settings.get("named_layouts", {})

        if layout_configs:
            lines.append("\n## Configured Layouts\n")
            for group_count, layout in layout_configs.items():
                nickname = layout.get("nickname", f"Layout for {group_count} groups")
                lines.append(f"Groups {group_count}: {nickname}\n")

        if named_layouts:
            lines.append("\n## Named Layouts\n")
            for layout_name, layout in named_layouts.items():
                nickname = layout.get("nickname", layout_name)
                group_count = len(layout.get("cells", []))
                lines.append(f"{layout_name}: {nickname} ({group_count} groups)\n")

        return "".join(lines)


class JornAutoPlaceTabsCreateProjectSettingsCommand(sublime_plugin.WindowCommand):
    """Create project-specific settings template."""

    def run(self):
        project_data = self.window.project_data()
        if not project_data:
            sublime.error_message("No project file is currently open. Please save your project first.")
            return

        # Check if project settings already exist
        if "settings" not in project_data:
            project_data["settings"] = {}

        if "jorn_auto_place_tabs" in project_data["settings"]:
            if not sublime.ok_cancel_dialog(
                "Project-specific AutoPlace settings already exist. Overwrite?",
                "Overwrite"
            ):
                return

        # Create template settings
        template_settings = {
            "auto_place_on_activation": True,
            "file_type_rules": {
                "0": [".py", ".pyw"],
                "1": [".js", ".ts", ".jsx", ".tsx"],
                "2": [".html", ".css", ".scss"],
                "3": [".md", ".txt", ".json"]
            },
            "directory_rules": {
                "0": ["*/src/*", "*/lib/*"],
                "1": ["*/tests/*", "*/test/*"],
                "2": ["*/docs/*"],
                "3": ["*/config/*"]
            },
            "project_files_group": 0,
            "external_files_group": 1,
            "auto_adjust_layout": False,
            "missing_group_behavior": "skip",
            "layout_strategy": "auto",
            "max_groups": 4
        }

        # Add to project data
        project_data["settings"]["jorn_auto_place_tabs"] = template_settings
        self.window.set_project_data(project_data)

        # Clear cache to pick up new settings
        plugin = Jorn_AutoPlaceTabsCommand.instance()
        if plugin:
            plugin._clear_settings_cache(self.window.id())

        sublime.status_message("Project-specific AutoPlace settings created")

    def is_enabled(self):
        return self.window.project_data() is not None
