# Technology Stack - Jorn_AutoPlaceTabs

## Core Technologies
- **Python 3.8+** - Sublime Text 4 plugin development
- **Sublime Text 4 API** - Plugin framework and event system

## Plugin Architecture
- **sublime_plugin.EventListener** - Tab activation and layout events
- **sublime_plugin.WindowCommand** - Manual placement commands
- **sublime_plugin.TextCommand** - Context-specific actions

## Configuration System
- **JSON Settings** - `.sublime-settings` files for user preferences
- **Command Palette** - `.sublime-commands` integration
- **Key Bindings** - `.sublime-keymap` for shortcuts
- **Context Menus** - `.sublime-menu` for tab/main menu integration

## Data Structures
- **defaultdict** - Group and tab state management
- **collections.deque** - Rate limiting and history tracking
- **set/list** - Tab categorization and filtering

## File Organization Pattern
```
Jorn_AutoPlaceTabs/
├── Jorn_AutoPlaceTabs.py          # Main plugin logic
├── Jorn_AutoPlaceTabs.sublime-commands
├── Jorn_AutoPlaceTabs.sublime-settings
├── Jorn_AutoPlaceTabs.sublime-keymap
├── Main.sublime-menu
├── Tab Context.sublime-menu
└── __meta__/                      # Development artifacts
```

## Established Patterns
- **PLUGIN_NAME** constant for consistent naming
- **Instance singleton** pattern for EventListener classes
- **Settings-driven** behavior with runtime configuration
- **Debug logging** with configurable verbosity
- **Rate limiting** to prevent infinite loops
- **Graceful error handling** with user feedback
