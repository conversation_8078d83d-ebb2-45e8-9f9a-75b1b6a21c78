{
	// kan legge inn:
	// - has extension
	// - has filename
	// - etc
	//
	// dette gjør at man kan lage sine egne "sticky" tabs


	// tabs that are excluded will be placed at the bottom of the sort-order
	// (og evt ikke sorteres innad - urørte)
    "exclude_filters": {
    	// last hour
        // "active_within_seconds": 3600,
        // tab has been activated within last hour
        // "tab_active_within_seconds": 3600,
        "tab_active_within_seconds": 3600,

        // been active for a total of at least two minutes (total of time tab has been active)
        // "tab_active_duration_seconds": 115,
    },

    // [Jorn_AutoSortTabs.py]
    // Determines whether tabs should automatically be sorted when a file is loaded or saved.
    // If set to true, tabs will be sorted based on the command defined in "autosort_command".
    "autosort_on_load_save" : true,

    // [Jorn_AutoSortTabs.py]
    // Warning: This can be buggy
    // Determines whether tabs should automatically be sorted when a tab is activated.
    // If set to true, tabs will be sorted based on the command defined in "autosort_command".
    "autosort_on_tab_activated" : true,

    // [Jorn_AutoSortTabs.py]
    // Specifies the command to be used when sorting tabs. This setting is
    // used by all autosorting operations. The command should correspond to
    // one of the available commands defined in the Jorn_SortTabs plugin. If
    // the value of this is empty it will sort based on the previous sorting
    // method.
    //
    // "autosort_command" : "sort_tabs_by_name",
    // "autosort_command" : "sort_tabs_by_file_path",
    // "autosort_command" : "sort_tabs_by_syntax",
    // "autosort_command" : "sort_tabs_by_date",
    // "autosort_command" : "sort_tabs_by_last_activation",
    // "autosort_command" : "",
    "autosort_command" : "sort_tabs_by_last_activation",


    // [Jorn_SortTabs.py]
    // Close empty tabs
    "close_empty_tabs" : true,

    // [Jorn_SortTabs.py]
    // Only sort current group (pane)
    "current_group_only" : true,

    // [Jorn_SortTabs.py]
    // Not yet implemented: Sort all tabs modified today first (before all other tabs)
    "sort_modified_today_first" : false,

}
