"""
Debug test for group index issue.
Run this in Sublime Text console to debug the group creation problem.
"""

import sublime

def test_group_creation():
    """Test group creation with high indices."""
    window = sublime.active_window()
    if not window:
        print("No active window")
        return
    
    print(f"Current groups: {window.num_groups()}")
    
    # Test project settings
    project_data = window.project_data()
    if project_data and "settings" in project_data:
        auto_place_settings = project_data["settings"].get("jorn_auto_place_tabs", {})
        if auto_place_settings:
            print("Project settings found:")
            print(f"  max_groups: {auto_place_settings.get('max_groups', 'not set')}")
            print(f"  auto_adjust_layout: {auto_place_settings.get('auto_adjust_layout', 'not set')}")
            print(f"  file_type_rules: {auto_place_settings.get('file_type_rules', {})}")
        else:
            print("No jorn_auto_place_tabs settings in project")
    else:
        print("No project data found")
    
    # Test layout creation for 8 groups
    print("\nTesting layout creation for 8 groups:")
    
    # 3x3 grid layout for 8 groups
    cols = [0.0, 0.33, 0.66, 1.0]
    rows = [0.0, 0.33, 0.66, 1.0]
    cells = [
        [0, 0, 1, 1], [1, 0, 2, 1], [2, 0, 3, 1],  # Top row
        [0, 1, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2],  # Middle row
        [0, 2, 1, 3], [1, 2, 2, 3]                 # Bottom row (only 2 cells for 8 total)
    ]
    
    test_layout = {"cols": cols, "rows": rows, "cells": cells}
    
    try:
        print(f"Attempting to set layout: {test_layout}")
        window.set_layout(test_layout)
        print(f"Success! New group count: {window.num_groups()}")
        
        # Test if we can access group 7
        try:
            views_in_group_7 = window.views_in_group(7)
            print(f"Group 7 exists and has {len(views_in_group_7)} views")
        except Exception as e:
            print(f"Cannot access group 7: {e}")
            
    except Exception as e:
        print(f"Failed to set layout: {e}")

def test_plugin_instance():
    """Test if plugin instance is available and working."""
    try:
        # Try to import the plugin
        import Jorn_AutoPlaceTabs
        plugin = Jorn_AutoPlaceTabs.Jorn_AutoPlaceTabsCommand.instance()
        
        if plugin:
            print("Plugin instance found")
            window = sublime.active_window()
            if window:
                effective_settings = plugin._get_effective_settings(window)
                print(f"Effective max_groups: {effective_settings.get('max_groups')}")
                print(f"Effective auto_adjust_layout: {effective_settings.get('auto_adjust_layout')}")
        else:
            print("Plugin instance not found")
            
    except Exception as e:
        print(f"Error testing plugin: {e}")

# Run the tests
print("=== Group Creation Debug Test ===")
test_group_creation()
print("\n=== Plugin Instance Test ===")
test_plugin_instance()

print("\n=== Instructions ===")
print("1. Copy this entire script")
print("2. Open Sublime Text console (Ctrl+`)")
print("3. Paste and run the script")
print("4. Check the output for issues")
print("\nIf max_groups is 7 but you want group index 7, change max_groups to 8 in your project settings.")
