{"auto_complete": {"selected_items": []}, "buffers": [{"file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.py", "settings": {"buffer_size": 32776, "encoding": "UTF-8", "line_ending": "Unix"}, "undo_stack": [[15, 1, "revert", null, "AgAAAAAAAAAAAAAAAAAAAAAAAAAMcgAAaW1wb3J0IHN1YmxpbWUKaW1wb3J0IHN1YmxpbWVfcGx1Z2luCmltcG9ydCBvcwppbXBvcnQgdGltZQppbXBvcnQgZm5tYXRjaApmcm9tIGNvbGxlY3Rpb25zIGltcG9ydCBkZWZhdWx0ZGljdCwgZGVxdWUKClBMVUdJTl9OQU1FID0gIkpvcm5fQXV0b1BsYWNlVGFicyIKTUFYX1BMQUNFTUVOVFNfUEVSX1NFQ09ORCA9IDUKCgpjbGFzcyBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kKHN1YmxpbWVfcGx1Z2luLkV2ZW50TGlzdGVuZXIpOgogICAgIiIiCiAgICBNYWluIHBsdWdpbiB0aGF0IGF1dG9tYXRpY2FsbHkgcGxhY2VzIHRhYnMgaW4gYXBwcm9wcmlhdGUgZ3JvdXBzIGJhc2VkIG9uOgogICAgLSBGaWxlIHR5cGUvZXh0ZW5zaW9uIHBhdHRlcm5zCiAgICAtIERpcmVjdG9yeSBwYXR0ZXJucyAgCiAgICAtIFByb2plY3QgbWVtYmVyc2hpcAogICAgLSBDdXN0b20gdXNlci1kZWZpbmVkIHJ1bGVzCiAgICAKICAgIFByZXZlbnRzIGluZmluaXRlIGxvb3BzIHZpYToKICAgIDEpIFJlY3Vyc2lvbiBndWFyZCAoX2lzX3BsYWNpbmcpCiAgICAyKSBGcmVxdWVuY3ktYmFzZWQgbGltaXQgKE1BWF9QTEFDRU1FTlRTX1BFUl9TRUNPTkQpCiAgICAzKSBQbGFjZW1lbnQgaGlzdG9yeSB0cmFja2luZwogICAgIiIiCgogICAgX2luc3RhbmNlID0gTm9uZQoKICAgIGRlZiBfX2luaXRfXyhzZWxmKToKICAgICAgICBzdXBlcigpLl9faW5pdF9fKCkKICAgICAgICBzZWxmLnNldHRpbmdzID0gc3VibGltZS5sb2FkX3NldHRpbmdzKGYie1BMVUdJTl9OQU1FfS5zdWJsaW1lLXNldHRpbmdzIikKICAgICAgICBzZWxmLl9pc19wbGFjaW5nID0gRmFsc2UKICAgICAgICBzZWxmLl9wbGFjZW1lbnRfdGltZXN0YW1wcyA9IGRlcXVlKCkKICAgICAgICBzZWxmLl9sYXN0X3BsYWNlbWVudHMgPSBkZWZhdWx0ZGljdChsYW1iZGE6IGRlZmF1bHRkaWN0KHR1cGxlKSkKICAgICAgICBzZWxmLl9wcm9qZWN0X3NldHRpbmdzX2NhY2hlID0ge30gICMgQ2FjaGUgcHJvamVjdCBzZXR0aW5ncyBieSB3aW5kb3cgSUQKICAgICAgICBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kLl9pbnN0YW5jZSA9IHNlbGYKCiAgICBAY2xhc3NtZXRob2QKICAgIGRlZiBpbnN0YW5jZShjbHMpOgogICAgICAgICIiIlVzZWQgYnkgbWFudWFsIHBsYWNlbWVudCBjb21tYW5kcyB0byByZWZlcmVuY2UgdGhpcyBwbHVnaW4gaW5zdGFuY2UuIiIiCiAgICAgICAgcmV0dXJuIGNscy5faW5zdGFuY2UKCiAgICBkZWYgX2RlYnVnX3ByaW50KHNlbGYsIG1lc3NhZ2UpOgogICAgICAgICIiIlByaW50IGRlYnVnIG1lc3NhZ2Ugb25seSBpZiBkZWJ1ZyBtb2RlIGlzIGVuYWJsZWQuIiIiCiAgICAgICAgaWYgc2VsZi5zZXR0aW5ncy5nZXQoImVuYWJsZV9kZWJ1Z19wcmludHMiLCBGYWxzZSk6CiAgICAgICAgICAgIHByaW50KGYiW3tQTFVHSU5fTkFNRX1dIHttZXNzYWdlfSIpCgogICAgZGVmIF9nZXRfZWZmZWN0aXZlX3NldHRpbmdzKHNlbGYsIHdpbmRvdyk6CiAgICAgICAgIiIiR2V0IGVmZmVjdGl2ZSBzZXR0aW5ncyBjb21iaW5pbmcgZ2xvYmFsIGFuZCBwcm9qZWN0LXNwZWNpZmljIHNldHRpbmdzLiIiIgogICAgICAgIGlmIG5vdCB3aW5kb3c6CiAgICAgICAgICAgIHJldHVybiBzZWxmLnNldHRpbmdzCgogICAgICAgIHdpbmRvd19pZCA9IHdpbmRvdy5pZCgpCgogICAgICAgICMgQ2hlY2sgY2FjaGUgZmlyc3QKICAgICAgICBpZiB3aW5kb3dfaWQgaW4gc2VsZi5fcHJvamVjdF9zZXR0aW5nc19jYWNoZToKICAgICAgICAgICAgcmV0dXJuIHNlbGYuX3Byb2plY3Rfc2V0dGluZ3NfY2FjaGVbd2luZG93X2lkXQoKICAgICAgICAjIFN0YXJ0IHdpdGggZ2xvYmFsIHNldHRpbmdzCiAgICAgICAgZWZmZWN0aXZlX3NldHRpbmdzID0ge30KCiAgICAgICAgIyBDb3B5IGFsbCBnbG9iYWwgc2V0dGluZ3MKICAgICAgICBmb3Iga2V5IGluIFsiYXV0b19wbGFjZV9vbl9hY3RpdmF0aW9uIiwgImF1dG9fcGxhY2Vfb25fbG9hZCIsICJlbmFibGVfZGVidWdfcHJpbnRzIiwKICAgICAgICAgICAgICAgICAgICJncm91cF9zb3J0X21ldGhvZCIsICJmaWxlX3R5cGVfcnVsZXMiLCAiZGlyZWN0b3J5X3J1bGVzIiwgInByb2plY3RfZmlsZXNfZ3JvdXAiLAogICAgICAgICAgICAgICAgICAgImV4dGVybmFsX2ZpbGVzX2dyb3VwIiwgInVuc2F2ZWRfZmlsZXNfZ3JvdXAiLCAiZXhjbHVkZV9wYXR0ZXJucyIsICJjdXN0b21fcnVsZXMiLAogICAgICAgICAgICAgICAgICAgImF1dG9fYWRqdXN0X2xheW91dCIsICJtaXNzaW5nX2dyb3VwX2JlaGF2aW9yIiwgImxheW91dF90eXBlIiwgImxheW91dF9jb25maWdzIl06CiAgICAgICAgICAgIGVmZmVjdGl2ZV9zZXR0aW5nc1trZXldID0gc2VsZi5zZXR0aW5ncy5nZXQoa2V5KQoKICAgICAgICAjIEdldCBwcm9qZWN0LXNwZWNpZmljIHNldHRpbmdzCiAgICAgICAgcHJvamVjdF9kYXRhID0gd2luZG93LnByb2plY3RfZGF0YSgpCiAgICAgICAgaWYgcHJvamVjdF9kYXRhIGFuZCAic2V0dGluZ3MiIGluIHByb2plY3RfZGF0YToKICAgICAgICAgICAgcHJvamVjdF9zZXR0aW5ncyA9IHByb2plY3RfZGF0YVsic2V0dGluZ3MiXS5nZXQoImpvcm5fYXV0b19wbGFjZV90YWJzIiwge30pCiAgICAgICAgICAgIGlmIHByb2plY3Rfc2V0dGluZ3M6CiAgICAgICAgICAgICAgICBzZWxmLl9kZWJ1Z19wcmludChmIkZvdW5kIHByb2plY3Qtc3BlY2lmaWMgc2V0dGluZ3M6IHtsaXN0KHByb2plY3Rfc2V0dGluZ3Mua2V5cygpKX0iKQogICAgICAgICAgICAgICAgIyBPdmVycmlkZSBnbG9iYWwgc2V0dGluZ3Mgd2l0aCBwcm9qZWN0LXNwZWNpZmljIG9uZXMKICAgICAgICAgICAgICAgIGVmZmVjdGl2ZV9zZXR0aW5ncy51cGRhdGUocHJvamVjdF9zZXR0aW5ncykKCiAgICAgICAgIyBDYWNoZSB0aGUgcmVzdWx0CiAgICAgICAgc2VsZi5fcHJvamVjdF9zZXR0aW5nc19jYWNoZVt3aW5kb3dfaWRdID0gZWZmZWN0aXZlX3NldHRpbmdzCgogICAgICAgIHJldHVybiBlZmZlY3RpdmVfc2V0dGluZ3MKCiAgICBkZWYgX2NsZWFyX3NldHRpbmdzX2NhY2hlKHNlbGYsIHdpbmRvd19pZD1Ob25lKToKICAgICAgICAiIiJDbGVhciBzZXR0aW5ncyBjYWNoZSBmb3IgYSBzcGVjaWZpYyB3aW5kb3cgb3IgYWxsIHdpbmRvd3MuIiIiCiAgICAgICAgaWYgd2luZG93X2lkOgogICAgICAgICAgICBzZWxmLl9wcm9qZWN0X3NldHRpbmdzX2NhY2hlLnBvcCh3aW5kb3dfaWQsIE5vbmUpCiAgICAgICAgZWxzZToKICAgICAgICAgICAgc2VsZi5fcHJvamVjdF9zZXR0aW5nc19jYWNoZS5jbGVhcigpCgogICAgZGVmIF9nZXRfc2V0dGluZyhzZWxmLCBrZXksIGRlZmF1bHQ9Tm9uZSwgd2luZG93PU5vbmUpOgogICAgICAgICIiIkdldCBhIHNldHRpbmcgdmFsdWUgZnJvbSBlZmZlY3RpdmUgc2V0dGluZ3MgKGdsb2JhbCArIHByb2plY3Qtc3BlY2lmaWMpLiIiIgogICAgICAgIGlmIHdpbmRvdzoKICAgICAgICAgICAgZWZmZWN0aXZlX3NldHRpbmdzID0gc2VsZi5fZ2V0X2VmZmVjdGl2ZV9zZXR0aW5ncyh3aW5kb3cpCiAgICAgICAgICAgIHJldHVybiBlZmZlY3RpdmVfc2V0dGluZ3MuZ2V0KGtleSwgZGVmYXVsdCkKICAgICAgICBlbHNlOgogICAgICAgICAgICByZXR1cm4gc2VsZi5zZXR0aW5ncy5nZXQoa2V5LCBkZWZhdWx0KQoKICAgIGRlZiBfY2hlY2tfcGxhY2VtZW50X2ZyZXF1ZW5jeShzZWxmKToKICAgICAgICAiIiJSYXRlIGxpbWl0aW5nIHRvIHByZXZlbnQgZXhjZXNzaXZlIHBsYWNlbWVudHMuIiIiCiAgICAgICAgbm93ID0gdGltZS50aW1lKCkKICAgICAgICBzZWxmLl9wbGFjZW1lbnRfdGltZXN0YW1wcy5hcHBlbmQobm93KQogICAgICAgIAogICAgICAgICMgUmVtb3ZlIHRpbWVzdGFtcHMgb2xkZXIgdGhhbiAxIHNlY29uZAogICAgICAgIHdoaWxlIChzZWxmLl9wbGFjZW1lbnRfdGltZXN0YW1wcyBhbmQgCiAgICAgICAgICAgICAgIG5vdyAtIHNlbGYuX3BsYWNlbWVudF90aW1lc3RhbXBzWzBdID4gMS4wKToKICAgICAgICAgICAgc2VsZi5fcGxhY2VtZW50X3RpbWVzdGFtcHMucG9wbGVmdCgpCiAgICAgICAgCiAgICAgICAgcmV0dXJuIGxlbihzZWxmLl9wbGFjZW1lbnRfdGltZXN0YW1wcykgPD0gTUFYX1BMQUNFTUVOVFNfUEVSX1NFQ09ORAoKICAgIGRlZiBvbl9hY3RpdmF0ZWRfYXN5bmMoc2VsZiwgdmlldyk6CiAgICAgICAgIiIiSGFuZGxlIHRhYiBhY3RpdmF0aW9uIGZvciBhdXRvLXBsYWNlbWVudC4iIiIKICAgICAgICBpZiBub3QgdmlldyBvciBub3Qgdmlldy53aW5kb3coKSBvciBzZWxmLl9pc19wbGFjaW5nOgogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgd2luZG93ID0gdmlldy53aW5kb3coKQogICAgICAgIGlmIG5vdCBzZWxmLl9nZXRfc2V0dGluZygiYXV0b19wbGFjZV9vbl9hY3RpdmF0aW9uIiwgVHJ1ZSwgd2luZG93KToKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIGlmIG5vdCBzZWxmLl9zaG91bGRfYXV0b19wbGFjZSh2aWV3KToKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIGlmIG5vdCBzZWxmLl9jaGVja19wbGFjZW1lbnRfZnJlcXVlbmN5KCk6CiAgICAgICAgICAgIHNlbGYuX2RlYnVnX3ByaW50KCJSYXRlIGxpbWl0IGV4Y2VlZGVkLCBza2lwcGluZyBwbGFjZW1lbnQiKQogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgc2VsZi5fcGxhY2VfdGFiKHZpZXcpCgogICAgZGVmIG9uX2xvYWRfYXN5bmMoc2VsZiwgdmlldyk6CiAgICAgICAgIiIiSGFuZGxlIGZpbGUgbG9hZCBmb3IgYXV0by1wbGFjZW1lbnQuIiIiCiAgICAgICAgaWYgbm90IHZpZXcgb3Igbm90IHZpZXcud2luZG93KCkgb3Igc2VsZi5faXNfcGxhY2luZzoKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIHdpbmRvdyA9IHZpZXcud2luZG93KCkKICAgICAgICBpZiBub3Qgc2VsZi5fZ2V0X3NldHRpbmcoImF1dG9fcGxhY2Vfb25fbG9hZCIsIFRydWUsIHdpbmRvdyk6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICBpZiBub3Qgc2VsZi5fc2hvdWxkX2F1dG9fcGxhY2Uodmlldyk6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICBpZiBub3Qgc2VsZi5fY2hlY2tfcGxhY2VtZW50X2ZyZXF1ZW5jeSgpOgogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgIyBTbWFsbCBkZWxheSB0byBlbnN1cmUgZmlsZSBpcyBmdWxseSBsb2FkZWQKICAgICAgICBzdWJsaW1lLnNldF90aW1lb3V0X2FzeW5jKGxhbWJkYTogc2VsZi5fcGxhY2VfdGFiKHZpZXcpLCAxMDApCgogICAgZGVmIG9uX3dpbmRvd19jb21tYW5kKHNlbGYsIHdpbmRvdywgY29tbWFuZF9uYW1lLCBhcmdzKToKICAgICAgICAiIiJIYW5kbGUgd2luZG93IGNvbW1hbmRzIHRoYXQgbWlnaHQgY2hhbmdlIHByb2plY3Qgc2V0dGluZ3MuIiIiCiAgICAgICAgaWYgY29tbWFuZF9uYW1lIGluIFsib3Blbl9wcm9qZWN0IiwgImNsb3NlX3Byb2plY3QiLCAic3dpdGNoX3Byb2plY3QiXToKICAgICAgICAgICAgIyBDbGVhciBzZXR0aW5ncyBjYWNoZSB3aGVuIHByb2plY3QgY2hhbmdlcwogICAgICAgICAgICBzZWxmLl9jbGVhcl9zZXR0aW5nc19jYWNoZSh3aW5kb3cuaWQoKSkKCiAgICBkZWYgb25fcG9zdF93aW5kb3dfY29tbWFuZChzZWxmLCB3aW5kb3csIGNvbW1hbmRfbmFtZSwgYXJncyk6CiAgICAgICAgIiIiSGFuZGxlIHBvc3Qtd2luZG93IGNvbW1hbmRzIHRoYXQgbWlnaHQgY2hhbmdlIHByb2plY3Qgc2V0dGluZ3MuIiIiCiAgICAgICAgaWYgY29tbWFuZF9uYW1lIGluIFsib3Blbl9wcm9qZWN0IiwgImNsb3NlX3Byb2plY3QiLCAic3dpdGNoX3Byb2plY3QiXToKICAgICAgICAgICAgIyBDbGVhciBzZXR0aW5ncyBjYWNoZSB3aGVuIHByb2plY3QgY2hhbmdlcwogICAgICAgICAgICBzZWxmLl9jbGVhcl9zZXR0aW5nc19jYWNoZSh3aW5kb3cuaWQoKSkKCiAgICBkZWYgX3Nob3VsZF9hdXRvX3BsYWNlKHNlbGYsIHZpZXcpOgogICAgICAgICIiIkRldGVybWluZSBpZiBhIHZpZXcgc2hvdWxkIGJlIGF1dG8tcGxhY2VkLiIiIgogICAgICAgIGlmIG5vdCB2aWV3IG9yIG5vdCB2aWV3LndpbmRvdygpOgogICAgICAgICAgICByZXR1cm4gRmFsc2UKCiAgICAgICAgIyBTa2lwIGlmIGFscmVhZHkgaW4gY29ycmVjdCBncm91cAogICAgICAgIHRhcmdldF9ncm91cCA9IHNlbGYuX2RldGVybWluZV90YXJnZXRfZ3JvdXAodmlldykKICAgICAgICBpZiB0YXJnZXRfZ3JvdXAgaXMgTm9uZToKICAgICAgICAgICAgcmV0dXJuIEZhbHNlCgogICAgICAgIGN1cnJlbnRfZ3JvdXAsIF8gPSB2aWV3LndpbmRvdygpLmdldF92aWV3X2luZGV4KHZpZXcpCiAgICAgICAgcmV0dXJuIGN1cnJlbnRfZ3JvdXAgIT0gdGFyZ2V0X2dyb3VwCgogICAgZGVmIF9kZXRlcm1pbmVfdGFyZ2V0X2dyb3VwKHNlbGYsIHZpZXcpOgogICAgICAgICIiIkRldGVybWluZSB0aGUgdGFyZ2V0IGdyb3VwIGZvciBhIHZpZXcgYmFzZWQgb24gcGxhY2VtZW50IHJ1bGVzLiIiIgogICAgICAgIHdpbmRvdyA9IHZpZXcud2luZG93KCkKICAgICAgICBpZiBub3Qgd2luZG93OgogICAgICAgICAgICByZXR1cm4gTm9uZQoKICAgICAgICBmaWxlX3BhdGggPSB2aWV3LmZpbGVfbmFtZSgpCiAgICAgICAgaWYgbm90IGZpbGVfcGF0aDoKICAgICAgICAgICAgcmV0dXJuIHNlbGYuX2dldF9ncm91cF9mb3JfdW5zYXZlZCh2aWV3KQoKICAgICAgICAjIENoZWNrIGlmIGZpbGUgc2hvdWxkIGJlIGV4Y2x1ZGVkCiAgICAgICAgaWYgc2VsZi5fc2hvdWxkX2V4Y2x1ZGVfZmlsZShmaWxlX3BhdGgsIHdpbmRvdyk6CiAgICAgICAgICAgIHJldHVybiBOb25lCgogICAgICAgICMgQ2hlY2sgY3VzdG9tIHJ1bGVzIGZpcnN0IChoaWdoZXN0IHByaW9yaXR5KQogICAgICAgIHRhcmdldF9ncm91cCA9IHNlbGYuX2NoZWNrX2N1c3RvbV9ydWxlcyhmaWxlX3BhdGgsIHdpbmRvdykKICAgICAgICBpZiB0YXJnZXRfZ3JvdXAgaXMgbm90IE5vbmU6CiAgICAgICAgICAgIHJldHVybiB0YXJnZXRfZ3JvdXAKCiAgICAgICAgIyBDaGVjayBmaWxlIHR5cGUgcnVsZXMKICAgICAgICB0YXJnZXRfZ3JvdXAgPSBzZWxmLl9jaGVja19maWxlX3R5cGVfcnVsZXMoZmlsZV9wYXRoLCB3aW5kb3cpCiAgICAgICAgaWYgdGFyZ2V0X2dyb3VwIGlzIG5vdCBOb25lOgogICAgICAgICAgICByZXR1cm4gdGFyZ2V0X2dyb3VwCgogICAgICAgICMgQ2hlY2sgZGlyZWN0b3J5IHJ1bGVzCiAgICAgICAgdGFyZ2V0X2dyb3VwID0gc2VsZi5fY2hlY2tfZGlyZWN0b3J5X3J1bGVzKGZpbGVfcGF0aCwgd2luZG93KQogICAgICAgIGlmIHRhcmdldF9ncm91cCBpcyBub3QgTm9uZToKICAgICAgICAgICAgcmV0dXJuIHRhcmdldF9ncm91cAoKICAgICAgICAjIENoZWNrIHByb2plY3QgbWVtYmVyc2hpcAogICAgICAgIHRhcmdldF9ncm91cCA9IHNlbGYuX2NoZWNrX3Byb2plY3RfcnVsZXModmlldywgZmlsZV9wYXRoLCB3aW5kb3cpCiAgICAgICAgaWYgdGFyZ2V0X2dyb3VwIGlzIG5vdCBOb25lOgogICAgICAgICAgICByZXR1cm4gdGFyZ2V0X2dyb3VwCgogICAgICAgIHJldHVybiBOb25lCgogICAgZGVmIF9jaGVja19maWxlX3R5cGVfcnVsZXMoc2VsZiwgZmlsZV9wYXRoLCB3aW5kb3c9Tm9uZSk6CiAgICAgICAgIiIiQ2hlY2sgZmlsZSB0eXBlL2V4dGVuc2lvbiBydWxlcy4iIiIKICAgICAgICBmaWxlX2V4dCA9IG9zLnBhdGguc3BsaXRleHQoZmlsZV9wYXRoKVsxXS5sb3dlcigpCiAgICAgICAgZmlsZV9uYW1lID0gb3MucGF0aC5iYXNlbmFtZShmaWxlX3BhdGgpCgogICAgICAgIHR5cGVfcnVsZXMgPSBzZWxmLl9nZXRfc2V0dGluZygiZmlsZV90eXBlX3J1bGVzIiwge30sIHdpbmRvdykKCiAgICAgICAgZm9yIGdyb3VwX2luZGV4LCBwYXR0ZXJucyBpbiB0eXBlX3J1bGVzLml0ZW1zKCk6CiAgICAgICAgICAgIHRyeToKICAgICAgICAgICAgICAgIGdyb3VwX251bSA9IGludChncm91cF9pbmRleCkKICAgICAgICAgICAgICAgIGZvciBwYXR0ZXJuIGluIHBhdHRlcm5zOgogICAgICAgICAgICAgICAgICAgIGlmIChwYXR0ZXJuLnN0YXJ0c3dpdGgoJy4nKSBhbmQgZmlsZV9leHQgPT0gcGF0dGVybi5sb3dlcigpKSBvciBcCiAgICAgICAgICAgICAgICAgICAgICAgZm5tYXRjaC5mbm1hdGNoKGZpbGVfbmFtZS5sb3dlcigpLCBwYXR0ZXJuLmxvd2VyKCkpOgogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZ3JvdXBfbnVtCiAgICAgICAgICAgIGV4Y2VwdCAoVmFsdWVFcnJvciwgVHlwZUVycm9yKToKICAgICAgICAgICAgICAgIGNvbnRpbnVlCgogICAgICAgIHJldHVybiBOb25lCgogICAgZGVmIF9jaGVja19kaXJlY3RvcnlfcnVsZXMoc2VsZiwgZmlsZV9wYXRoLCB3aW5kb3c9Tm9uZSk6CiAgICAgICAgIiIiQ2hlY2sgZGlyZWN0b3J5LWJhc2VkIHJ1bGVzLiIiIgogICAgICAgIGRpcl9wYXRoID0gb3MucGF0aC5kaXJuYW1lKGZpbGVfcGF0aCkKICAgICAgICBkaXJfcnVsZXMgPSBzZWxmLl9nZXRfc2V0dGluZygiZGlyZWN0b3J5X3J1bGVzIiwge30sIHdpbmRvdykKCiAgICAgICAgZm9yIGdyb3VwX2luZGV4LCBwYXR0ZXJucyBpbiBkaXJfcnVsZXMuaXRlbXMoKToKICAgICAgICAgICAgdHJ5OgogICAgICAgICAgICAgICAgZ3JvdXBfbnVtID0gaW50KGdyb3VwX2luZGV4KQogICAgICAgICAgICAgICAgZm9yIHBhdHRlcm4gaW4gcGF0dGVybnM6CiAgICAgICAgICAgICAgICAgICAgaWYgZm5tYXRjaC5mbm1hdGNoKGRpcl9wYXRoLCBwYXR0ZXJuKToKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGdyb3VwX251bQogICAgICAgICAgICBleGNlcHQgKFZhbHVlRXJyb3IsIFR5cGVFcnJvcik6CiAgICAgICAgICAgICAgICBjb250aW51ZQoKICAgICAgICByZXR1cm4gTm9uZQoKICAgIGRlZiBfY2hlY2tfcHJvamVjdF9ydWxlcyhzZWxmLCB2aWV3LCBmaWxlX3BhdGgsIHdpbmRvdz1Ob25lKToKICAgICAgICAiIiJDaGVjayBwcm9qZWN0IG1lbWJlcnNoaXAgcnVsZXMuIiIiCiAgICAgICAgaWYgbm90IHdpbmRvdzoKICAgICAgICAgICAgd2luZG93ID0gdmlldy53aW5kb3coKQogICAgICAgIHByb2plY3RfZm9sZGVycyA9IHdpbmRvdy5mb2xkZXJzKCkgaWYgd2luZG93IGVsc2UgW10KCiAgICAgICAgaXNfaW5fcHJvamVjdCA9IGFueShmaWxlX3BhdGguc3RhcnRzd2l0aChmb2xkZXIpIGZvciBmb2xkZXIgaW4gcHJvamVjdF9mb2xkZXJzKQoKICAgICAgICBpZiBpc19pbl9wcm9qZWN0OgogICAgICAgICAgICByZXR1cm4gc2VsZi5fZ2V0X3NldHRpbmcoInByb2plY3RfZmlsZXNfZ3JvdXAiLCBOb25lLCB3aW5kb3cpCiAgICAgICAgZWxzZToKICAgICAgICAgICAgcmV0dXJuIHNlbGYuX2dldF9zZXR0aW5nKCJleHRlcm5hbF9maWxlc19ncm91cCIsIE5vbmUsIHdpbmRvdykKCiAgICBkZWYgX2dldF9ncm91cF9mb3JfdW5zYXZlZChzZWxmLCB2aWV3KToKICAgICAgICAiIiJEZXRlcm1pbmUgZ3JvdXAgZm9yIHVuc2F2ZWQgZmlsZXMuIiIiCiAgICAgICAgd2luZG93ID0gdmlldy53aW5kb3coKQogICAgICAgIHJldHVybiBzZWxmLl9nZXRfc2V0dGluZygidW5zYXZlZF9maWxlc19ncm91cCIsIE5vbmUsIHdpbmRvdykKCiAgICBkZWYgX3Nob3VsZF9leGNsdWRlX2ZpbGUoc2VsZiwgZmlsZV9wYXRoLCB3aW5kb3c9Tm9uZSk6CiAgICAgICAgIiIiQ2hlY2sgaWYgZmlsZSBzaG91bGQgYmUgZXhjbHVkZWQgZnJvbSBhdXRvLXBsYWNlbWVudC4iIiIKICAgICAgICBleGNsdWRlX3BhdHRlcm5zID0gc2VsZi5fZ2V0X3NldHRpbmcoImV4Y2x1ZGVfcGF0dGVybnMiLCBbXSwgd2luZG93KQogICAgICAgIGZpbGVfbmFtZSA9IG9zLnBhdGguYmFzZW5hbWUoZmlsZV9wYXRoKQoKICAgICAgICBmb3IgcGF0dGVybiBpbiBleGNsdWRlX3BhdHRlcm5zOgogICAgICAgICAgICBpZiBmbm1hdGNoLmZubWF0Y2goZmlsZV9wYXRoLCBwYXR0ZXJuKSBvciBmbm1hdGNoLmZubWF0Y2goZmlsZV9uYW1lLCBwYXR0ZXJuKToKICAgICAgICAgICAgICAgIHJldHVybiBUcnVlCiAgICAgICAgcmV0dXJuIEZhbHNlCgogICAgZGVmIF9jaGVja19jdXN0b21fcnVsZXMoc2VsZiwgZmlsZV9wYXRoLCB3aW5kb3c9Tm9uZSk6CiAgICAgICAgIiIiQ2hlY2sgY3VzdG9tIHJ1bGVzIHdpdGggcHJpb3JpdHkgb3JkZXJpbmcuIiIiCiAgICAgICAgY3VzdG9tX3J1bGVzID0gc2VsZi5fZ2V0X3NldHRpbmcoImN1c3RvbV9ydWxlcyIsIFtdLCB3aW5kb3cpCiAgICAgICAgaWYgbm90IGN1c3RvbV9ydWxlczoKICAgICAgICAgICAgcmV0dXJuIE5vbmUKCiAgICAgICAgIyBTb3J0IGJ5IHByaW9yaXR5IChoaWdoZXIgZmlyc3QpCiAgICAgICAgc29ydGVkX3J1bGVzID0gc29ydGVkKGN1c3RvbV9ydWxlcywga2V5PWxhbWJkYSByOiByLmdldCgicHJpb3JpdHkiLCAwKSwgcmV2ZXJzZT1UcnVlKQoKICAgICAgICBmaWxlX25hbWUgPSBvcy5wYXRoLmJhc2VuYW1lKGZpbGVfcGF0aCkKICAgICAgICBmb3IgcnVsZSBpbiBzb3J0ZWRfcnVsZXM6CiAgICAgICAgICAgIHBhdHRlcm4gPSBydWxlLmdldCgicGF0dGVybiIsICIiKQogICAgICAgICAgICBpZiBwYXR0ZXJuIGFuZCAoZm5tYXRjaC5mbm1hdGNoKGZpbGVfbmFtZSwgcGF0dGVybikgb3IgZm5tYXRjaC5mbm1hdGNoKGZpbGVfcGF0aCwgcGF0dGVybikpOgogICAgICAgICAgICAgICAgcmV0dXJuIHJ1bGUuZ2V0KCJncm91cCIpCgogICAgICAgIHJldHVybiBOb25lCgogICAgZGVmIF9wbGFjZV90YWIoc2VsZiwgdmlldyk6CiAgICAgICAgIiIiUGxhY2UgYSB0YWIgaW4gaXRzIHRhcmdldCBncm91cC4iIiIKICAgICAgICBpZiBzZWxmLl9pc19wbGFjaW5nOgogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgd2luZG93ID0gdmlldy53aW5kb3coKQogICAgICAgIGlmIG5vdCB3aW5kb3c6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICB0YXJnZXRfZ3JvdXAgPSBzZWxmLl9kZXRlcm1pbmVfdGFyZ2V0X2dyb3VwKHZpZXcpCiAgICAgICAgaWYgdGFyZ2V0X2dyb3VwIGlzIE5vbmU6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICBjdXJyZW50X2dyb3VwLCBjdXJyZW50X2luZGV4ID0gd2luZG93LmdldF92aWV3X2luZGV4KHZpZXcpCiAgICAgICAgCiAgICAgICAgIyBFbnN1cmUgdGFyZ2V0IGdyb3VwIGV4aXN0cywgY3JlYXRlIGlmIG5lZWRlZAogICAgICAgIGlmIHRhcmdldF9ncm91cCA+PSB3aW5kb3cubnVtX2dyb3VwcygpOgogICAgICAgICAgICBpZiBzZWxmLl9nZXRfc2V0dGluZygiYXV0b19hZGp1c3RfbGF5b3V0IiwgRmFsc2UsIHdpbmRvdyk6CiAgICAgICAgICAgICAgICBzZWxmLl9jcmVhdGVfbGF5b3V0X2Zvcl9ncm91cHMod2luZG93LCB0YXJnZXRfZ3JvdXAgKyAxKQogICAgICAgICAgICBlbHNlOgogICAgICAgICAgICAgICAgIyBUYXJnZXQgZ3JvdXAgZG9lc24ndCBleGlzdCBhbmQgYXV0by1sYXlvdXQgaXMgZGlzYWJsZWQKICAgICAgICAgICAgICAgIGZhbGxiYWNrX2JlaGF2aW9yID0gc2VsZi5fZ2V0X3NldHRpbmcoIm1pc3NpbmdfZ3JvdXBfYmVoYXZpb3IiLCAic2tpcCIsIHdpbmRvdykKCiAgICAgICAgICAgICAgICBpZiBmYWxsYmFja19iZWhhdmlvciA9PSAic2tpcCI6CiAgICAgICAgICAgICAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJUYXJnZXQgZ3JvdXAge3RhcmdldF9ncm91cH0gZG9lc24ndCBleGlzdCAob25seSB7d2luZG93Lm51bV9ncm91cHMoKX0gZ3JvdXBzKSwgc2tpcHBpbmciKQogICAgICAgICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICAgICAgZWxpZiBmYWxsYmFja19iZWhhdmlvciA9PSAibGFzdF9ncm91cCI6CiAgICAgICAgICAgICAgICAgICAgdGFyZ2V0X2dyb3VwID0gd2luZG93Lm51bV9ncm91cHMoKSAtIDEKICAgICAgICAgICAgICAgICAgICBzZWxmLl9kZWJ1Z19wcmludChmIlRhcmdldCBncm91cCBkb2Vzbid0IGV4aXN0LCB1c2luZyBsYXN0IGdyb3VwICh7dGFyZ2V0X2dyb3VwfSkiKQogICAgICAgICAgICAgICAgZWxpZiBmYWxsYmFja19iZWhhdmlvciA9PSAiZmlyc3RfZ3JvdXAiOgogICAgICAgICAgICAgICAgICAgIHRhcmdldF9ncm91cCA9IDAKICAgICAgICAgICAgICAgICAgICBzZWxmLl9kZWJ1Z19wcmludChmIlRhcmdldCBncm91cCBkb2Vzbid0IGV4aXN0LCB1c2luZyBmaXJzdCBncm91cCAoe3RhcmdldF9ncm91cH0pIikKICAgICAgICAgICAgICAgIGVsc2U6CiAgICAgICAgICAgICAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJUYXJnZXQgZ3JvdXAge3RhcmdldF9ncm91cH0gZG9lc24ndCBleGlzdCwgc2tpcHBpbmcgKHVua25vd24gZmFsbGJhY2sgYmVoYXZpb3IpIikKICAgICAgICAgICAgICAgICAgICByZXR1cm4KCiAgICAgICAgaWYgY3VycmVudF9ncm91cCA9PSB0YXJnZXRfZ3JvdXA6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICBzZWxmLl9pc19wbGFjaW5nID0gVHJ1ZQogICAgICAgIHRyeToKICAgICAgICAgICAgIyBEZXRlcm1pbmUgdGFyZ2V0IGluZGV4IHdpdGhpbiBncm91cAogICAgICAgICAgICB0YXJnZXRfaW5kZXggPSBzZWxmLl9nZXRfdGFyZ2V0X2luZGV4KHZpZXcsIHRhcmdldF9ncm91cCwgd2luZG93KQogICAgICAgICAgICAKICAgICAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJNb3ZpbmcgdGFiIGZyb20gZ3JvdXAge2N1cnJlbnRfZ3JvdXB9IHRvIGdyb3VwIHt0YXJnZXRfZ3JvdXB9LCBpbmRleCB7dGFyZ2V0X2luZGV4fSIpCiAgICAgICAgICAgIHdpbmRvdy5zZXRfdmlld19pbmRleCh2aWV3LCB0YXJnZXRfZ3JvdXAsIHRhcmdldF9pbmRleCkKICAgICAgICAgICAgCiAgICAgICAgICAgICMgVHJhY2sgdGhpcyBwbGFjZW1lbnQKICAgICAgICAgICAgc2VsZi5fbGFzdF9wbGFjZW1lbnRzW3dpbmRvdy5pZCgpXVt2aWV3LmlkKCldID0gKHRhcmdldF9ncm91cCwgdGFyZ2V0X2luZGV4LCB0aW1lLnRpbWUoKSkKICAgICAgICAgICAgCiAgICAgICAgZmluYWxseToKICAgICAgICAgICAgc2VsZi5faXNfcGxhY2luZyA9IEZhbHNlCgogICAgZGVmIF9nZXRfdGFyZ2V0X2luZGV4KHNlbGYsIHZpZXcsIHRhcmdldF9ncm91cCwgd2luZG93PU5vbmUpOgogICAgICAgICIiIkRldGVybWluZSB0aGUgdGFyZ2V0IGluZGV4IHdpdGhpbiBhIGdyb3VwLiIiIgogICAgICAgIGlmIG5vdCB3aW5kb3c6CiAgICAgICAgICAgIHdpbmRvdyA9IHZpZXcud2luZG93KCkKICAgICAgICB2aWV3c19pbl9ncm91cCA9IHdpbmRvdy52aWV3c19pbl9ncm91cCh0YXJnZXRfZ3JvdXApCgogICAgICAgIHNvcnRfbWV0aG9kID0gc2VsZi5fZ2V0X3NldHRpbmcoImdyb3VwX3NvcnRfbWV0aG9kIiwgImFwcGVuZCIsIHdpbmRvdykKCiAgICAgICAgaWYgc29ydF9tZXRob2QgPT0gInByZXBlbmQiOgogICAgICAgICAgICByZXR1cm4gMAogICAgICAgIGVsaWYgc29ydF9tZXRob2QgPT0gImFwcGVuZCI6CiAgICAgICAgICAgIHJldHVybiBsZW4odmlld3NfaW5fZ3JvdXApCiAgICAgICAgZWxpZiBzb3J0X21ldGhvZCA9PSAiYWxwaGFiZXRpY2FsIjoKICAgICAgICAgICAgcmV0dXJuIHNlbGYuX2dldF9hbHBoYWJldGljYWxfaW5kZXgodmlldywgdmlld3NfaW5fZ3JvdXApCiAgICAgICAgZWxzZToKICAgICAgICAgICAgcmV0dXJuIGxlbih2aWV3c19pbl9ncm91cCkKCiAgICBkZWYgX2dldF9hbHBoYWJldGljYWxfaW5kZXgoc2VsZiwgdmlldywgdmlld3NfaW5fZ3JvdXApOgogICAgICAgICIiIkdldCBpbmRleCBmb3IgYWxwaGFiZXRpY2FsIGluc2VydGlvbi4iIiIKICAgICAgICB2aWV3X25hbWUgPSBvcy5wYXRoLmJhc2VuYW1lKHZpZXcuZmlsZV9uYW1lKCkgb3Igdmlldy5uYW1lKCkgb3IgIiIpCiAgICAgICAgCiAgICAgICAgZm9yIGksIGV4aXN0aW5nX3ZpZXcgaW4gZW51bWVyYXRlKHZpZXdzX2luX2dyb3VwKToKICAgICAgICAgICAgZXhpc3RpbmdfbmFtZSA9IG9zLnBhdGguYmFzZW5hbWUoZXhpc3Rpbmdfdmlldy5maWxlX25hbWUoKSBvciBleGlzdGluZ192aWV3Lm5hbWUoKSBvciAiIikKICAgICAgICAgICAgaWYgdmlld19uYW1lLmxvd2VyKCkgPCBleGlzdGluZ19uYW1lLmxvd2VyKCk6CiAgICAgICAgICAgICAgICByZXR1cm4gaQogICAgICAgIAogICAgICAgIHJldHVybiBsZW4odmlld3NfaW5fZ3JvdXApCgogICAgZGVmIF9jcmVhdGVfbGF5b3V0X2Zvcl9ncm91cHMoc2VsZiwgd2luZG93LCBudW1fZ3JvdXBzKToKICAgICAgICAiIiJDcmVhdGUgYSBsYXlvdXQgd2l0aCB0aGUgc3BlY2lmaWVkIG51bWJlciBvZiBncm91cHMuIiIiCiAgICAgICAgY3VycmVudF9ncm91cHMgPSB3aW5kb3cubnVtX2dyb3VwcygpCiAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJOZWVkIHtudW1fZ3JvdXBzfSBncm91cHMsIGN1cnJlbnQ6IHtjdXJyZW50X2dyb3Vwc30iKQoKICAgICAgICBpZiBudW1fZ3JvdXBzIDw9IGN1cnJlbnRfZ3JvdXBzOgogICAgICAgICAgICBzZWxmLl9kZWJ1Z19wcmludChmIkFscmVhZHkgaGF2ZSBlbm91Z2ggZ3JvdXBzIikKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgICMgQ2hlY2sgaWYgd2UgaGF2ZSBhIGN1c3RvbSBsYXlvdXQgZm9yIHRoaXMgZ3JvdXAgY291bnQKICAgICAgICBsYXlvdXRfY29uZmlncyA9IHNlbGYuX2dldF9zZXR0aW5nKCJsYXlvdXRfY29uZmlncyIsIHt9LCB3aW5kb3cpCiAgICAgICAgY3VzdG9tX2xheW91dCA9IGxheW91dF9jb25maWdzLmdldChzdHIobnVtX2dyb3VwcykpCgogICAgICAgIGlmIGN1c3RvbV9sYXlvdXQ6CiAgICAgICAgICAgIHNlbGYuX2RlYnVnX3ByaW50KGYiVXNpbmcgY3VzdG9tIGxheW91dCBmb3Ige251bV9ncm91cHN9IGdyb3VwcyIpCiAgICAgICAgICAgIHNlbGYuX2FwcGx5X2xheW91dCh3aW5kb3csIGN1c3RvbV9sYXlvdXQpCiAgICAgICAgICAgIHJldHVybgoKICAgICAgICAjIEdlbmVyYXRlIGxheW91dCBiYXNlZCBvbiBsYXlvdXQgdHlwZQogICAgICAgIGxheW91dF90eXBlID0gc2VsZi5fZ2V0X3NldHRpbmcoImxheW91dF90eXBlIiwgImNvbHVtbnMiLCB3aW5kb3cpCgogICAgICAgIGlmIGxheW91dF90eXBlID09ICJjb2x1bW5zIjoKICAgICAgICAgICAgbGF5b3V0ID0gc2VsZi5fY3JlYXRlX2NvbHVtbnNfbGF5b3V0KG51bV9ncm91cHMpCiAgICAgICAgZWxpZiBsYXlvdXRfdHlwZSA9PSAicm93cyI6CiAgICAgICAgICAgIGxheW91dCA9IHNlbGYuX2NyZWF0ZV9yb3dzX2xheW91dChudW1fZ3JvdXBzKQogICAgICAgIGVsaWYgbGF5b3V0X3R5cGUgPT0gImdyaWQiOgogICAgICAgICAgICBsYXlvdXQgPSBzZWxmLl9jcmVhdGVfZ3JpZF9sYXlvdXQobnVtX2dyb3VwcykKICAgICAgICBlbHNlOgogICAgICAgICAgICAjIERlZmF1bHQgdG8gY29sdW1ucwogICAgICAgICAgICBsYXlvdXQgPSBzZWxmLl9jcmVhdGVfY29sdW1uc19sYXlvdXQobnVtX2dyb3VwcykKCiAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJDcmVhdGluZyB7bGF5b3V0X3R5cGV9IGxheW91dCBmb3Ige251bV9ncm91cHN9IGdyb3VwcyIpCiAgICAgICAgc2VsZi5fYXBwbHlfbGF5b3V0KHdpbmRvdywgbGF5b3V0KQoKICAgIGRlZiBfY3JlYXRlX2NvbHVtbnNfbGF5b3V0KHNlbGYsIG51bV9ncm91cHMpOgogICAgICAgICIiIkNyZWF0ZSBhIHNpbXBsZSBjb2x1bW5zIGxheW91dC4iIiIKICAgICAgICBjb2xzID0gW2kgLyBudW1fZ3JvdXBzIGZvciBpIGluIHJhbmdlKG51bV9ncm91cHMgKyAxKV0KICAgICAgICByb3dzID0gWzAuMCwgMS4wXQogICAgICAgIGNlbGxzID0gW1tpLCAwLCBpICsgMSwgMV0gZm9yIGkgaW4gcmFuZ2UobnVtX2dyb3VwcyldCiAgICAgICAgcmV0dXJuIHsiY29scyI6IGNvbHMsICJyb3dzIjogcm93cywgImNlbGxzIjogY2VsbHN9CgogICAgZGVmIF9jcmVhdGVfcm93c19sYXlvdXQoc2VsZiwgbnVtX2dyb3Vwcyk6CiAgICAgICAgIiIiQ3JlYXRlIGEgc2ltcGxlIHJvd3MgbGF5b3V0LiIiIgogICAgICAgIGNvbHMgPSBbMC4wLCAxLjBdCiAgICAgICAgcm93cyA9IFtpIC8gbnVtX2dyb3VwcyBmb3IgaSBpbiByYW5nZShudW1fZ3JvdXBzICsgMSldCiAgICAgICAgY2VsbHMgPSBbWzAsIGksIDEsIGkgKyAxXSBmb3IgaSBpbiByYW5nZShudW1fZ3JvdXBzKV0KICAgICAgICByZXR1cm4geyJjb2xzIjogY29scywgInJvd3MiOiByb3dzLCAiY2VsbHMiOiBjZWxsc30KCiAgICBkZWYgX2NyZWF0ZV9ncmlkX2xheW91dChzZWxmLCBudW1fZ3JvdXBzKToKICAgICAgICAiIiJDcmVhdGUgYSBncmlkIGxheW91dC4iIiIKICAgICAgICBpbXBvcnQgbWF0aAogICAgICAgIG51bV9jb2x1bW5zID0gaW50KG1hdGguc3FydChudW1fZ3JvdXBzKSkKICAgICAgICBudW1fcm93cyA9IChudW1fZ3JvdXBzICsgbnVtX2NvbHVtbnMgLSAxKSAvLyBudW1fY29sdW1ucwoKICAgICAgICBpZiBudW1fY29sdW1ucyA9PSAwOgogICAgICAgICAgICBudW1fY29sdW1ucyA9IDEKCiAgICAgICAgY29scyA9IFtpIC8gbnVtX2NvbHVtbnMgZm9yIGkgaW4gcmFuZ2UobnVtX2NvbHVtbnMgKyAxKV0KICAgICAgICByb3dzID0gW2kgLyBudW1fcm93cyBmb3IgaSBpbiByYW5nZShudW1fcm93cyArIDEpXQoKICAgICAgICBjZWxscyA9IFtdCiAgICAgICAgZ3JvdXAgPSAwCiAgICAgICAgZm9yIHJvdyBpbiByYW5nZShudW1fcm93cyk6CiAgICAgICAgICAgIGZvciBjb2wgaW4gcmFuZ2UobnVtX2NvbHVtbnMpOgogICAgICAgICAgICAgICAgaWYgZ3JvdXAgPCBudW1fZ3JvdXBzOgogICAgICAgICAgICAgICAgICAgIGNlbGxzLmFwcGVuZChbY29sLCByb3csIGNvbCArIDEsIHJvdyArIDFdKQogICAgICAgICAgICAgICAgICAgIGdyb3VwICs9IDEKCiAgICAgICAgcmV0dXJuIHsiY29scyI6IGNvbHMsICJyb3dzIjogcm93cywgImNlbGxzIjogY2VsbHN9CgogICAgZGVmIF9hcHBseV9sYXlvdXQoc2VsZiwgd2luZG93LCBsYXlvdXQpOgogICAgICAgICIiIkFwcGx5IGEgbGF5b3V0IHRvIHRoZSB3aW5kb3cuIiIiCiAgICAgICAgdHJ5OgogICAgICAgICAgICB3aW5kb3cuc2V0X2xheW91dChsYXlvdXQpCiAgICAgICAgICAgIHNlbGYuX2RlYnVnX3ByaW50KGYiQXBwbGllZCBsYXlvdXQ6IHtsYXlvdXR9IikKICAgICAgICBleGNlcHQgRXhjZXB0aW9uIGFzIGU6CiAgICAgICAgICAgIHNlbGYuX2RlYnVnX3ByaW50KGYiRmFpbGVkIHRvIGFwcGx5IGxheW91dDoge2V9IikKCgoKCgpjbGFzcyBKb3JuQXV0b1BsYWNlVGFic01hbnVhbENvbW1hbmQoc3VibGltZV9wbHVnaW4uV2luZG93Q29tbWFuZCk6CiAgICAiIiJNYW51YWwgY29tbWFuZCB0byBwbGFjZSBjdXJyZW50IHRhYiBhY2NvcmRpbmcgdG8gcnVsZXMuIiIiCiAgICAKICAgIGRlZiBydW4oc2VsZik6CiAgICAgICAgdmlldyA9IHNlbGYud2luZG93LmFjdGl2ZV92aWV3KCkKICAgICAgICBpZiBub3QgdmlldzoKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgCiAgICAgICAgcGx1Z2luID0gSm9ybl9BdXRvUGxhY2VUYWJzQ29tbWFuZC5pbnN0YW5jZSgpCiAgICAgICAgaWYgcGx1Z2luOgogICAgICAgICAgICBwbHVnaW4uX3BsYWNlX3RhYih2aWV3KQogICAgICAgICAgICBzdWJsaW1lLnN0YXR1c19tZXNzYWdlKCJUYWIgcGxhY2VkIGFjY29yZGluZyB0byBydWxlcyIpCgoKY2xhc3MgSm9ybkF1dG9QbGFjZVRhYnNQbGFjZUFsbENvbW1hbmQoc3VibGltZV9wbHVnaW4uV2luZG93Q29tbWFuZCk6CiAgICAiIiJDb21tYW5kIHRvIHBsYWNlIGFsbCB0YWJzIGFjY29yZGluZyB0byBydWxlcy4iIiIKICAgIAogICAgZGVmIHJ1bihzZWxmKToKICAgICAgICBwbHVnaW4gPSBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kLmluc3RhbmNlKCkKICAgICAgICBpZiBub3QgcGx1Z2luOgogICAgICAgICAgICByZXR1cm4KICAgICAgICAKICAgICAgICBwbGFjZWRfY291bnQgPSAwCiAgICAgICAgZm9yIHZpZXcgaW4gc2VsZi53aW5kb3cudmlld3MoKToKICAgICAgICAgICAgaWYgcGx1Z2luLl9zaG91bGRfYXV0b19wbGFjZSh2aWV3KToKICAgICAgICAgICAgICAgIHBsdWdpbi5fcGxhY2VfdGFiKHZpZXcpCiAgICAgICAgICAgICAgICBwbGFjZWRfY291bnQgKz0gMQogICAgICAgIAogICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoZiJQbGFjZWQge3BsYWNlZF9jb3VudH0gdGFicyBhY2NvcmRpbmcgdG8gcnVsZXMiKQoKCmNsYXNzIEpvcm5BdXRvUGxhY2VUYWJzVG9nZ2xlQ29tbWFuZChzdWJsaW1lX3BsdWdpbi5XaW5kb3dDb21tYW5kKToKICAgICIiIlRvZ2dsZSBhdXRvLXBsYWNlbWVudCBvbi9vZmYuIiIiCgogICAgZGVmIHJ1bihzZWxmKToKICAgICAgICBwbHVnaW4gPSBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kLmluc3RhbmNlKCkKICAgICAgICBpZiBub3QgcGx1Z2luOgogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgY3VycmVudCA9IHBsdWdpbi5zZXR0aW5ncy5nZXQoImF1dG9fcGxhY2Vfb25fYWN0aXZhdGlvbiIsIFRydWUpCiAgICAgICAgcGx1Z2luLnNldHRpbmdzLnNldCgiYXV0b19wbGFjZV9vbl9hY3RpdmF0aW9uIiwgbm90IGN1cnJlbnQpCiAgICAgICAgc3VibGltZS5zYXZlX3NldHRpbmdzKGYie1BMVUdJTl9OQU1FfS5zdWJsaW1lLXNldHRpbmdzIikKCiAgICAgICAgc3RhdHVzID0gImVuYWJsZWQiIGlmIG5vdCBjdXJyZW50IGVsc2UgImRpc2FibGVkIgogICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoZiJBdXRvLXBsYWNlbWVudCB7c3RhdHVzfSIpCgoKY2xhc3MgSm9ybkF1dG9QbGFjZVRhYnNSZWxvYWRTZXR0aW5nc0NvbW1hbmQoc3VibGltZV9wbHVnaW4uV2luZG93Q29tbWFuZCk6CiAgICAiIiJSZWxvYWQgcGx1Z2luIHNldHRpbmdzIGFuZCBjbGVhciBwcm9qZWN0IHNldHRpbmdzIGNhY2hlLiIiIgoKICAgIGRlZiBydW4oc2VsZik6CiAgICAgICAgcGx1Z2luID0gSm9ybl9BdXRvUGxhY2VUYWJzQ29tbWFuZC5pbnN0YW5jZSgpCiAgICAgICAgaWYgbm90IHBsdWdpbjoKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIHBsdWdpbi5zZXR0aW5ncyA9IHN1YmxpbWUubG9hZF9zZXR0aW5ncyhmIntQTFVHSU5fTkFNRX0uc3VibGltZS1zZXR0aW5ncyIpCiAgICAgICAgcGx1Z2luLl9jbGVhcl9zZXR0aW5nc19jYWNoZSgpICAjIENsZWFyIGFsbCBjYWNoZWQgcHJvamVjdCBzZXR0aW5ncwogICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoIkF1dG9QbGFjZSBzZXR0aW5ncyByZWxvYWRlZCIpCgoKY2xhc3MgSm9ybkF1dG9QbGFjZVRhYnNTaG93UnVsZXNDb21tYW5kKHN1YmxpbWVfcGx1Z2luLldpbmRvd0NvbW1hbmQpOgogICAgIiIiU2hvdyBjdXJyZW50IHBsYWNlbWVudCBydWxlcyBpbiBhIG5ldyB2aWV3LiIiIgoKICAgIGRlZiBydW4oc2VsZik6CiAgICAgICAgcGx1Z2luID0gSm9ybl9BdXRvUGxhY2VUYWJzQ29tbWFuZC5pbnN0YW5jZSgpCiAgICAgICAgaWYgbm90IHBsdWdpbjoKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIHZpZXcgPSBzZWxmLndpbmRvdy5uZXdfZmlsZSgpCiAgICAgICAgdmlldy5zZXRfbmFtZSgiQXV0b1BsYWNlIFJ1bGVzIikKICAgICAgICB2aWV3LnNldF9zY3JhdGNoKFRydWUpCgogICAgICAgIHJ1bGVzX3RleHQgPSBzZWxmLl9mb3JtYXRfcnVsZXMocGx1Z2luLnNldHRpbmdzKQogICAgICAgIHZpZXcucnVuX2NvbW1hbmQoImFwcGVuZCIsIHsiY2hhcmFjdGVycyI6IHJ1bGVzX3RleHR9KQogICAgICAgIHZpZXcuc2V0X3JlYWRfb25seShUcnVlKQoKICAgIGRlZiBfZm9ybWF0X3J1bGVzKHNlbGYsIHNldHRpbmdzKToKICAgICAgICAiIiJGb3JtYXQgY3VycmVudCBydWxlcyBmb3IgZGlzcGxheS4iIiIKICAgICAgICBwbHVnaW4gPSBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kLmluc3RhbmNlKCkKICAgICAgICBpZiBub3QgcGx1Z2luOgogICAgICAgICAgICByZXR1cm4gIlBsdWdpbiBub3QgYXZhaWxhYmxlIgoKICAgICAgICAjIEdldCBlZmZlY3RpdmUgc2V0dGluZ3MgZm9yIHRoaXMgd2luZG93CiAgICAgICAgZWZmZWN0aXZlX3NldHRpbmdzID0gcGx1Z2luLl9nZXRfZWZmZWN0aXZlX3NldHRpbmdzKHNlbGYud2luZG93KQoKICAgICAgICBsaW5lcyA9IFsiIyBKb3JuIEF1dG9QbGFjZSBUYWJzIC0gQ3VycmVudCBSdWxlc1xuXG4iXQoKICAgICAgICAjIENoZWNrIGlmIHByb2plY3Qtc3BlY2lmaWMgc2V0dGluZ3MgYXJlIGFjdGl2ZQogICAgICAgIHByb2plY3RfZGF0YSA9IHNlbGYud2luZG93LnByb2plY3RfZGF0YSgpCiAgICAgICAgaGFzX3Byb2plY3Rfc2V0dGluZ3MgPSAocHJvamVjdF9kYXRhIGFuZCAic2V0dGluZ3MiIGluIHByb2plY3RfZGF0YSBhbmQKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJqb3JuX2F1dG9fcGxhY2VfdGFicyIgaW4gcHJvamVjdF9kYXRhWyJzZXR0aW5ncyJdKQoKICAgICAgICBpZiBoYXNfcHJvamVjdF9zZXR0aW5nczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBQcm9qZWN0LVNwZWNpZmljIFNldHRpbmdzIEFjdGl2ZVxuIikKICAgICAgICAgICAgcHJvamVjdF9zZXR0aW5ncyA9IHByb2plY3RfZGF0YVsic2V0dGluZ3MiXVsiam9ybl9hdXRvX3BsYWNlX3RhYnMiXQogICAgICAgICAgICBsaW5lcy5hcHBlbmQoZiJQcm9qZWN0IG92ZXJyaWRlczogeycsICcuam9pbihwcm9qZWN0X3NldHRpbmdzLmtleXMoKSl9XG5cbiIpCiAgICAgICAgZWxzZToKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBVc2luZyBHbG9iYWwgU2V0dGluZ3MgT25seVxuXG4iKQoKICAgICAgICAjIEF1dG8tcGxhY2VtZW50IHN0YXR1cwogICAgICAgIGF1dG9fb25fYWN0aXZhdGlvbiA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImF1dG9fcGxhY2Vfb25fYWN0aXZhdGlvbiIsIFRydWUpCiAgICAgICAgYXV0b19vbl9sb2FkID0gZWZmZWN0aXZlX3NldHRpbmdzLmdldCgiYXV0b19wbGFjZV9vbl9sb2FkIiwgVHJ1ZSkKICAgICAgICBsaW5lcy5hcHBlbmQoZiJBdXRvLXBsYWNlbWVudCBvbiBhY3RpdmF0aW9uOiB7YXV0b19vbl9hY3RpdmF0aW9ufVxuIikKICAgICAgICBsaW5lcy5hcHBlbmQoZiJBdXRvLXBsYWNlbWVudCBvbiBsb2FkOiB7YXV0b19vbl9sb2FkfVxuXG4iKQoKICAgICAgICAjIEZpbGUgdHlwZSBydWxlcwogICAgICAgIGZpbGVfdHlwZV9ydWxlcyA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImZpbGVfdHlwZV9ydWxlcyIsIHt9KQogICAgICAgIGlmIGZpbGVfdHlwZV9ydWxlczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBGaWxlIFR5cGUgUnVsZXNcbiIpCiAgICAgICAgICAgIGZvciBncm91cCwgcGF0dGVybnMgaW4gZmlsZV90eXBlX3J1bGVzLml0ZW1zKCk6CiAgICAgICAgICAgICAgICBsaW5lcy5hcHBlbmQoZiJHcm91cCB7Z3JvdXB9OiB7JywgJy5qb2luKHBhdHRlcm5zKX1cbiIpCiAgICAgICAgICAgIGxpbmVzLmFwcGVuZCgiXG4iKQoKICAgICAgICAjIERpcmVjdG9yeSBydWxlcwogICAgICAgIGRpcl9ydWxlcyA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImRpcmVjdG9yeV9ydWxlcyIsIHt9KQogICAgICAgIGlmIGRpcl9ydWxlczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBEaXJlY3RvcnkgUnVsZXNcbiIpCiAgICAgICAgICAgIGZvciBncm91cCwgcGF0dGVybnMgaW4gZGlyX3J1bGVzLml0ZW1zKCk6CiAgICAgICAgICAgICAgICBsaW5lcy5hcHBlbmQoZiJHcm91cCB7Z3JvdXB9OiB7JywgJy5qb2luKHBhdHRlcm5zKX1cbiIpCiAgICAgICAgICAgIGxpbmVzLmFwcGVuZCgiXG4iKQoKICAgICAgICAjIEN1c3RvbSBydWxlcwogICAgICAgIGN1c3RvbV9ydWxlcyA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImN1c3RvbV9ydWxlcyIsIFtdKQogICAgICAgIGlmIGN1c3RvbV9ydWxlczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBDdXN0b20gUnVsZXNcbiIpCiAgICAgICAgICAgIGZvciBydWxlIGluIHNvcnRlZChjdXN0b21fcnVsZXMsIGtleT1sYW1iZGEgcjogci5nZXQoInByaW9yaXR5IiwgMCksIHJldmVyc2U9VHJ1ZSk6CiAgICAgICAgICAgICAgICBuYW1lID0gcnVsZS5nZXQoIm5hbWUiLCAiVW5uYW1lZCIpCiAgICAgICAgICAgICAgICBwYXR0ZXJuID0gcnVsZS5nZXQoInBhdHRlcm4iLCAiIikKICAgICAgICAgICAgICAgIGdyb3VwID0gcnVsZS5nZXQoImdyb3VwIiwgIj8iKQogICAgICAgICAgICAgICAgcHJpb3JpdHkgPSBydWxlLmdldCgicHJpb3JpdHkiLCAwKQogICAgICAgICAgICAgICAgbGluZXMuYXBwZW5kKGYie25hbWV9OiB7cGF0dGVybn0g4oaSIEdyb3VwIHtncm91cH0gKFByaW9yaXR5OiB7cHJpb3JpdHl9KVxuIikKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCJcbiIpCgogICAgICAgICMgU3BlY2lhbCBncm91cHMKICAgICAgICBwcm9qZWN0X2dyb3VwID0gZWZmZWN0aXZlX3NldHRpbmdzLmdldCgicHJvamVjdF9maWxlc19ncm91cCIpCiAgICAgICAgZXh0ZXJuYWxfZ3JvdXAgPSBlZmZlY3RpdmVfc2V0dGluZ3MuZ2V0KCJleHRlcm5hbF9maWxlc19ncm91cCIpCiAgICAgICAgdW5zYXZlZF9ncm91cCA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoInVuc2F2ZWRfZmlsZXNfZ3JvdXAiKQoKICAgICAgICBsaW5lcy5hcHBlbmQoIiMjIFNwZWNpYWwgR3JvdXBzXG4iKQogICAgICAgIGlmIHByb2plY3RfZ3JvdXAgaXMgbm90IE5vbmU6CiAgICAgICAgICAgIGxpbmVzLmFwcGVuZChmIlByb2plY3QgZmlsZXM6IEdyb3VwIHtwcm9qZWN0X2dyb3VwfVxuIikKICAgICAgICBpZiBleHRlcm5hbF9ncm91cCBpcyBub3QgTm9uZToKICAgICAgICAgICAgbGluZXMuYXBwZW5kKGYiRXh0ZXJuYWwgZmlsZXM6IEdyb3VwIHtleHRlcm5hbF9ncm91cH1cbiIpCiAgICAgICAgaWYgdW5zYXZlZF9ncm91cCBpcyBub3QgTm9uZToKICAgICAgICAgICAgbGluZXMuYXBwZW5kKGYiVW5zYXZlZCBmaWxlczogR3JvdXAge3Vuc2F2ZWRfZ3JvdXB9XG4iKQoKICAgICAgICAjIExheW91dCBzZXR0aW5ncwogICAgICAgIGF1dG9fYWRqdXN0ID0gZWZmZWN0aXZlX3NldHRpbmdzLmdldCgiYXV0b19hZGp1c3RfbGF5b3V0IiwgRmFsc2UpCiAgICAgICAgbWlzc2luZ19iZWhhdmlvciA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoIm1pc3NpbmdfZ3JvdXBfYmVoYXZpb3IiLCAic2tpcCIpCiAgICAgICAgbGF5b3V0X3R5cGUgPSBlZmZlY3RpdmVfc2V0dGluZ3MuZ2V0KCJsYXlvdXRfdHlwZSIsICJjb2x1bW5zIikKICAgICAgICBzb3J0X21ldGhvZCA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImdyb3VwX3NvcnRfbWV0aG9kIiwgImFwcGVuZCIpCgogICAgICAgIGxpbmVzLmFwcGVuZCgiXG4jIyBMYXlvdXQgU2V0dGluZ3NcbiIpCiAgICAgICAgbGluZXMuYXBwZW5kKGYiQXV0by1hZGp1c3QgbGF5b3V0OiB7YXV0b19hZGp1c3R9XG4iKQogICAgICAgIGxpbmVzLmFwcGVuZChmIk1pc3NpbmcgZ3JvdXAgYmVoYXZpb3I6IHttaXNzaW5nX2JlaGF2aW9yfVxuIikKICAgICAgICBsaW5lcy5hcHBlbmQoZiJMYXlvdXQgdHlwZToge2xheW91dF90eXBlfVxuIikKICAgICAgICBsaW5lcy5hcHBlbmQoZiJTb3J0IG1ldGhvZDoge3NvcnRfbWV0aG9kfVxuIikKCiAgICAgICAgIyBDdXN0b20gbGF5b3V0cwogICAgICAgIGxheW91dF9jb25maWdzID0gZWZmZWN0aXZlX3NldHRpbmdzLmdldCgibGF5b3V0X2NvbmZpZ3MiLCB7fSkKICAgICAgICBpZiBsYXlvdXRfY29uZmlnczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCJcbiMjIEN1c3RvbSBMYXlvdXRzXG4iKQogICAgICAgICAgICBmb3IgZ3JvdXBfY291bnQsIGxheW91dCBpbiBsYXlvdXRfY29uZmlncy5pdGVtcygpOgogICAgICAgICAgICAgICAgZ3JvdXBfY291bnRfYWN0dWFsID0gbGVuKGxheW91dC5nZXQoImNlbGxzIiwgW10pKQogICAgICAgICAgICAgICAgbGluZXMuYXBwZW5kKGYiR3JvdXBzIHtncm91cF9jb3VudH06IEN1c3RvbSBsYXlvdXQgd2l0aCB7Z3JvdXBfY291bnRfYWN0dWFsfSBncm91cHNcbiIpCgogICAgICAgIHJldHVybiAiIi5qb2luKGxpbmVzKQoKCmNsYXNzIEpvcm5BdXRvUGxhY2VUYWJzQ3JlYXRlUHJvamVjdFNldHRpbmdzQ29tbWFuZChzdWJsaW1lX3BsdWdpbi5XaW5kb3dDb21tYW5kKToKICAgICIiIkNyZWF0ZSBwcm9qZWN0LXNwZWNpZmljIHNldHRpbmdzIHRlbXBsYXRlLiIiIgoKICAgIGRlZiBydW4oc2VsZik6CiAgICAgICAgcHJvamVjdF9kYXRhID0gc2VsZi53aW5kb3cucHJvamVjdF9kYXRhKCkKICAgICAgICBpZiBub3QgcHJvamVjdF9kYXRhOgogICAgICAgICAgICBzdWJsaW1lLmVycm9yX21lc3NhZ2UoIk5vIHByb2plY3QgZmlsZSBpcyBjdXJyZW50bHkgb3Blbi4gUGxlYXNlIHNhdmUgeW91ciBwcm9qZWN0IGZpcnN0LiIpCiAgICAgICAgICAgIHJldHVybgoKICAgICAgICAjIENoZWNrIGlmIHByb2plY3Qgc2V0dGluZ3MgYWxyZWFkeSBleGlzdAogICAgICAgIGlmICJzZXR0aW5ncyIgbm90IGluIHByb2plY3RfZGF0YToKICAgICAgICAgICAgcHJvamVjdF9kYXRhWyJzZXR0aW5ncyJdID0ge30KCiAgICAgICAgaWYgImpvcm5fYXV0b19wbGFjZV90YWJzIiBpbiBwcm9qZWN0X2RhdGFbInNldHRpbmdzIl06CiAgICAgICAgICAgIGlmIG5vdCBzdWJsaW1lLm9rX2NhbmNlbF9kaWFsb2coCiAgICAgICAgICAgICAgICAiUHJvamVjdC1zcGVjaWZpYyBBdXRvUGxhY2Ugc2V0dGluZ3MgYWxyZWFkeSBleGlzdC4gT3ZlcndyaXRlPyIsCiAgICAgICAgICAgICAgICAiT3ZlcndyaXRlIgogICAgICAgICAgICApOgogICAgICAgICAgICAgICAgcmV0dXJuCgogICAgICAgICMgQ3JlYXRlIHRlbXBsYXRlIHNldHRpbmdzCiAgICAgICAgdGVtcGxhdGVfc2V0dGluZ3MgPSB7CiAgICAgICAgICAgICJhdXRvX3BsYWNlX29uX2FjdGl2YXRpb24iOiBUcnVlLAogICAgICAgICAgICAiZmlsZV90eXBlX3J1bGVzIjogewogICAgICAgICAgICAgICAgIjAiOiBbIi5weSIsICIucHl3Il0sCiAgICAgICAgICAgICAgICAiMSI6IFsiLmpzIiwgIi50cyIsICIuanN4IiwgIi50c3giXSwKICAgICAgICAgICAgICAgICIyIjogWyIuaHRtbCIsICIuY3NzIiwgIi5zY3NzIl0sCiAgICAgICAgICAgICAgICAiMyI6IFsiLm1kIiwgIi50eHQiLCAiLmpzb24iXQogICAgICAgICAgICB9LAogICAgICAgICAgICAiZGlyZWN0b3J5X3J1bGVzIjogewogICAgICAgICAgICAgICAgIjAiOiBbIiovc3JjLyoiLCAiKi9saWIvKiJdLAogICAgICAgICAgICAgICAgIjEiOiBbIiovdGVzdHMvKiIsICIqL3Rlc3QvKiJdLAogICAgICAgICAgICAgICAgIjIiOiBbIiovZG9jcy8qIl0sCiAgICAgICAgICAgICAgICAiMyI6IFsiKi9jb25maWcvKiJdCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgICJwcm9qZWN0X2ZpbGVzX2dyb3VwIjogMCwKICAgICAgICAgICAgImV4dGVybmFsX2ZpbGVzX2dyb3VwIjogMSwKICAgICAgICAgICAgImF1dG9fYWRqdXN0X2xheW91dCI6IEZhbHNlLAogICAgICAgICAgICAibWlzc2luZ19ncm91cF9iZWhhdmlvciI6ICJza2lwIiwKICAgICAgICAgICAgImxheW91dF90eXBlIjogImNvbHVtbnMiCiAgICAgICAgfQoKICAgICAgICAjIEFkZCB0byBwcm9qZWN0IGRhdGEKICAgICAgICBwcm9qZWN0X2RhdGFbInNldHRpbmdzIl1bImpvcm5fYXV0b19wbGFjZV90YWJzIl0gPSB0ZW1wbGF0ZV9zZXR0aW5ncwogICAgICAgIHNlbGYud2luZG93LnNldF9wcm9qZWN0X2RhdGEocHJvamVjdF9kYXRhKQoKICAgICAgICAjIENsZWFyIGNhY2hlIHRvIHBpY2sgdXAgbmV3IHNldHRpbmdzCiAgICAgICAgcGx1Z2luID0gSm9ybl9BdXRvUGxhY2VUYWJzQ29tbWFuZC5pbnN0YW5jZSgpCiAgICAgICAgaWYgcGx1Z2luOgogICAgICAgICAgICBwbHVnaW4uX2NsZWFyX3NldHRpbmdzX2NhY2hlKHNlbGYud2luZG93LmlkKCkpCgogICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoIlByb2plY3Qtc3BlY2lmaWMgQXV0b1BsYWNlIHNldHRpbmdzIGNyZWF0ZWQiKQoKICAgIGRlZiBpc19lbmFibGVkKHNlbGYpOgogICAgICAgIHJldHVybiBzZWxmLndpbmRvdy5wcm9qZWN0X2RhdGEoKSBpcyBub3QgTm9uZQoKCmNsYXNzIEpvcm5BdXRvUGxhY2VUYWJzVGVzdExheW91dENvbW1hbmQoc3VibGltZV9wbHVnaW4uV2luZG93Q29tbWFuZCk6CiAgICAiIiJUZXN0IGEgc3BlY2lmaWMgbGF5b3V0IGJ5IGFwcGx5aW5nIGl0IHRvIHRoZSBjdXJyZW50IHdpbmRvdy4iIiIKCiAgICBkZWYgcnVuKHNlbGYpOgogICAgICAgIHBsdWdpbiA9IEpvcm5fQXV0b1BsYWNlVGFic0NvbW1hbmQuaW5zdGFuY2UoKQogICAgICAgIGlmIG5vdCBwbHVnaW46CiAgICAgICAgICAgIHN1YmxpbWUuZXJyb3JfbWVzc2FnZSgiQXV0b1BsYWNlIHBsdWdpbiBub3QgYXZhaWxhYmxlIikKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgICMgR2V0IGF2YWlsYWJsZSBjdXN0b20gbGF5b3V0cwogICAgICAgIGxheW91dF9jb25maWdzID0gcGx1Z2luLl9nZXRfc2V0dGluZygibGF5b3V0X2NvbmZpZ3MiLCB7fSwgc2VsZi53aW5kb3cpCgogICAgICAgICMgQ3JlYXRlIGxpc3Qgb2YgbGF5b3V0IG9wdGlvbnMKICAgICAgICBsYXlvdXRfb3B0aW9ucyA9IFtdCgogICAgICAgICMgQWRkIGN1c3RvbSBsYXlvdXRzCiAgICAgICAgZm9yIGdyb3VwX2NvdW50LCBsYXlvdXQgaW4gbGF5b3V0X2NvbmZpZ3MuaXRlbXMoKToKICAgICAgICAgICAgYWN0dWFsX2dyb3VwcyA9IGxlbihsYXlvdXQuZ2V0KCJjZWxscyIsIFtdKSkKICAgICAgICAgICAgbGF5b3V0X29wdGlvbnMuYXBwZW5kKFtmIkN1c3RvbToge2dyb3VwX2NvdW50fSBncm91cHMiLCBmIih7YWN0dWFsX2dyb3Vwc30gZ3JvdXBzKSJdKQoKICAgICAgICAjIEFkZCBnZW5lcmF0ZWQgbGF5b3V0IG9wdGlvbnMKICAgICAgICBsYXlvdXRfb3B0aW9ucy5leHRlbmQoWwogICAgICAgICAgICBbIkdlbmVyYXRlZDogMiBDb2x1bW5zIiwgImNvbHVtbnMiXSwKICAgICAgICAgICAgWyJHZW5lcmF0ZWQ6IDMgQ29sdW1ucyIsICJjb2x1bW5zIl0sCiAgICAgICAgICAgIFsiR2VuZXJhdGVkOiA0IENvbHVtbnMiLCAiY29sdW1ucyJdLAogICAgICAgICAgICBbIkdlbmVyYXRlZDogOCBDb2x1bW5zIiwgImNvbHVtbnMiXSwKICAgICAgICAgICAgWyJHZW5lcmF0ZWQ6IDIgUm93cyIsICJyb3dzIl0sCiAgICAgICAgICAgIFsiR2VuZXJhdGVkOiAzIFJvd3MiLCAicm93cyJdLAogICAgICAgICAgICBbIkdlbmVyYXRlZDogNCBSb3dzIiwgInJvd3MiXSwKICAgICAgICAgICAgWyJHZW5lcmF0ZWQ6IDJ4MiBHcmlkIiwgImdyaWQiXSwKICAgICAgICAgICAgWyJHZW5lcmF0ZWQ6IDN4MyBHcmlkIiwgImdyaWQiXQogICAgICAgIF0pCgogICAgICAgIGRlZiBvbl9zZWxlY3QoaW5kZXgpOgogICAgICAgICAgICBpZiBpbmRleCA9PSAtMToKICAgICAgICAgICAgICAgIHJldHVybgoKICAgICAgICAgICAgc2VsZWN0ZWQgPSBsYXlvdXRfb3B0aW9uc1tpbmRleF0KICAgICAgICAgICAgbGF5b3V0X25hbWUgPSBzZWxlY3RlZFswXQoKICAgICAgICAgICAgaWYgbGF5b3V0X25hbWUuc3RhcnRzd2l0aCgiQ3VzdG9tOiIpOgogICAgICAgICAgICAgICAgIyBIYW5kbGUgY3VzdG9tIGxheW91dHMKICAgICAgICAgICAgICAgIGdyb3VwX2NvdW50ID0gbGF5b3V0X25hbWUuc3BsaXQoIjoiKVsxXS5zdHJpcCgpLnNwbGl0KClbMF0KICAgICAgICAgICAgICAgIGxheW91dF9jb25maWcgPSBsYXlvdXRfY29uZmlncy5nZXQoZ3JvdXBfY291bnQpCiAgICAgICAgICAgICAgICBpZiBsYXlvdXRfY29uZmlnOgogICAgICAgICAgICAgICAgICAgIHBsdWdpbi5fYXBwbHlfbGF5b3V0KHNlbGYud2luZG93LCBsYXlvdXRfY29uZmlnKQogICAgICAgICAgICAgICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoZiJBcHBsaWVkIGN1c3RvbSBsYXlvdXQgZm9yIHtncm91cF9jb3VudH0gZ3JvdXBzIikKICAgICAgICAgICAgICAgIGVsc2U6CiAgICAgICAgICAgICAgICAgICAgc3VibGltZS5lcnJvcl9tZXNzYWdlKGYiQ3VzdG9tIGxheW91dCBmb3Ige2dyb3VwX2NvdW50fSBncm91cHMgbm90IGZvdW5kIikKCiAgICAgICAgICAgIGVsaWYgbGF5b3V0X25hbWUuc3RhcnRzd2l0aCgiR2VuZXJhdGVkOiIpOgogICAgICAgICAgICAgICAgIyBIYW5kbGUgZ2VuZXJhdGVkIGxheW91dHMKICAgICAgICAgICAgICAgIHBhcnRzID0gbGF5b3V0X25hbWUuc3BsaXQoKQogICAgICAgICAgICAgICAgaWYgIkNvbHVtbnMiIGluIGxheW91dF9uYW1lOgogICAgICAgICAgICAgICAgICAgIG51bV9ncm91cHMgPSBpbnQocGFydHNbMV0pCiAgICAgICAgICAgICAgICAgICAgbGF5b3V0ID0gcGx1Z2luLl9jcmVhdGVfY29sdW1uc19sYXlvdXQobnVtX2dyb3VwcykKICAgICAgICAgICAgICAgICAgICBwbHVnaW4uX2FwcGx5X2xheW91dChzZWxmLndpbmRvdywgbGF5b3V0KQogICAgICAgICAgICAgICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoZiJBcHBsaWVkIHtudW1fZ3JvdXBzfSBjb2x1bW5zIGxheW91dCIpCiAgICAgICAgICAgICAgICBlbGlmICJSb3dzIiBpbiBsYXlvdXRfbmFtZToKICAgICAgICAgICAgICAgICAgICBudW1fZ3JvdXBzID0gaW50KHBhcnRzWzFdKQogICAgICAgICAgICAgICAgICAgIGxheW91dCA9IHBsdWdpbi5fY3JlYXRlX3Jvd3NfbGF5b3V0KG51bV9ncm91cHMpCiAgICAgICAgICAgICAgICAgICAgcGx1Z2luLl9hcHBseV9sYXlvdXQoc2VsZi53aW5kb3csIGxheW91dCkKICAgICAgICAgICAgICAgICAgICBzdWJsaW1lLnN0YXR1c19tZXNzYWdlKGYiQXBwbGllZCB7bnVtX2dyb3Vwc30gcm93cyBsYXlvdXQiKQogICAgICAgICAgICAgICAgZWxpZiAiR3JpZCIgaW4gbGF5b3V0X25hbWU6CiAgICAgICAgICAgICAgICAgICAgaWYgIjJ4MiIgaW4gbGF5b3V0X25hbWU6CiAgICAgICAgICAgICAgICAgICAgICAgIGxheW91dCA9IHBsdWdpbi5fY3JlYXRlX2dyaWRfbGF5b3V0KDQpCiAgICAgICAgICAgICAgICAgICAgICAgIHBsdWdpbi5fYXBwbHlfbGF5b3V0KHNlbGYud2luZG93LCBsYXlvdXQpCiAgICAgICAgICAgICAgICAgICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoIkFwcGxpZWQgMngyIGdyaWQgbGF5b3V0IikKICAgICAgICAgICAgICAgICAgICBlbGlmICIzeDMiIGluIGxheW91dF9uYW1lOgogICAgICAgICAgICAgICAgICAgICAgICBsYXlvdXQgPSBwbHVnaW4uX2NyZWF0ZV9ncmlkX2xheW91dCg5KQogICAgICAgICAgICAgICAgICAgICAgICBwbHVnaW4uX2FwcGx5X2xheW91dChzZWxmLndpbmRvdywgbGF5b3V0KQogICAgICAgICAgICAgICAgICAgICAgICBzdWJsaW1lLnN0YXR1c19tZXNzYWdlKCJBcHBsaWVkIDN4MyBncmlkIGxheW91dCIpCgogICAgICAgIGlmIG5vdCBsYXlvdXRfb3B0aW9uczoKICAgICAgICAgICAgc3VibGltZS5lcnJvcl9tZXNzYWdlKCJObyBsYXlvdXRzIGF2YWlsYWJsZSB0byB0ZXN0IikKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIHNlbGYud2luZG93LnNob3dfcXVpY2tfcGFuZWwobGF5b3V0X29wdGlvbnMsIG9uX3NlbGVjdCkKAAAAAAAAAAAIgAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAC7AQAAAAAAALsBAAAAAAAAAAAAAAAA8L8"], [16, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAC7AQAAAAAAALsBAAAAAAAAAAAAAAAA8L8"]]}, {"file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-keymap", "settings": {"buffer_size": 576, "line_ending": "Unix"}}, {"file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-commands", "settings": {"buffer_size": 867, "line_ending": "Unix"}, "undo_stack": [[1, 1, "revert", null, "AgAAAAAAAAAAAAAAAAAAAAAAAABhAgAAWwogICAgewogICAgICAgICJjYXB0aW9uIjogIkpvcm4gQXV0b1BsYWNlOiBQbGFjZSBDdXJyZW50IFRhYiIsCiAgICAgICAgImNvbW1hbmQiOiAiam9ybl9hdXRvX3BsYWNlX3RhYnNfbWFudWFsIgogICAgfSwKICAgIHsKICAgICAgICAiY2FwdGlvbiI6ICJKb3JuIEF1dG9QbGFjZTogUGxhY2UgQWxsIFRhYnMiLAogICAgICAgICJjb21tYW5kIjogImpvcm5fYXV0b19wbGFjZV90YWJzX3BsYWNlX2FsbCIKICAgIH0sCiAgICB7CiAgICAgICAgImNhcHRpb24iOiAiSm9ybiBBdXRvUGxhY2U6IFRvZ2dsZSBBdXRvLVBsYWNlbWVudCIsCiAgICAgICAgImNvbW1hbmQiOiAiam9ybl9hdXRvX3BsYWNlX3RhYnNfdG9nZ2xlIgogICAgfSwKICAgIHsKICAgICAgICAiY2FwdGlvbiI6ICJKb3JuIEF1dG9QbGFjZTogUmVsb2FkIFNldHRpbmdzIiwKICAgICAgICAiY29tbWFuZCI6ICJqb3JuX2F1dG9fcGxhY2VfdGFic19yZWxvYWRfc2V0dGluZ3MiCiAgICB9LAogICAgewogICAgICAgICJjYXB0aW9uIjogIkpvcm4gQXV0b1BsYWNlOiBTaG93IEN1cnJlbnQgUnVsZXMiLAogICAgICAgICJjb21tYW5kIjogImpvcm5fYXV0b19wbGFjZV90YWJzX3Nob3dfcnVsZXMiCiAgICB9Cl0KAAAAAAAAAADuAgAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [2, 1, "revert", null, "AwAAAAAAAAAAAAAAAAAAAAAAAADuAgAAWwogICAgewogICAgICAgICJjYXB0aW9uIjogIkpvcm4gQXV0b1BsYWNlOiBQbGFjZSBDdXJyZW50IFRhYiIsCiAgICAgICAgImNvbW1hbmQiOiAiam9ybl9hdXRvX3BsYWNlX3RhYnNfbWFudWFsIgogICAgfSwKICAgIHsKICAgICAgICAiY2FwdGlvbiI6ICJKb3JuIEF1dG9QbGFjZTogUGxhY2UgQWxsIFRhYnMiLAogICAgICAgICJjb21tYW5kIjogImpvcm5fYXV0b19wbGFjZV90YWJzX3BsYWNlX2FsbCIKICAgIH0sCiAgICB7CiAgICAgICAgImNhcHRpb24iOiAiSm9ybiBBdXRvUGxhY2U6IFRvZ2dsZSBBdXRvLVBsYWNlbWVudCIsCiAgICAgICAgImNvbW1hbmQiOiAiam9ybl9hdXRvX3BsYWNlX3RhYnNfdG9nZ2xlIgogICAgfSwKICAgIHsKICAgICAgICAiY2FwdGlvbiI6ICJKb3JuIEF1dG9QbGFjZTogUmVsb2FkIFNldHRpbmdzIiwKICAgICAgICAiY29tbWFuZCI6ICJqb3JuX2F1dG9fcGxhY2VfdGFic19yZWxvYWRfc2V0dGluZ3MiCiAgICB9LAogICAgewogICAgICAgICJjYXB0aW9uIjogIkpvcm4gQXV0b1BsYWNlOiBTaG93IEN1cnJlbnQgUnVsZXMiLAogICAgICAgICJjb21tYW5kIjogImpvcm5fYXV0b19wbGFjZV90YWJzX3Nob3dfcnVsZXMiCiAgICB9LAogICAgewogICAgICAgICJjYXB0aW9uIjogIkpvcm4gQXV0b1BsYWNlOiBDcmVhdGUgUHJvamVjdCBTZXR0aW5ncyIsCiAgICAgICAgImNvbW1hbmQiOiAiam9ybl9hdXRvX3BsYWNlX3RhYnNfY3JlYXRlX3Byb2plY3Rfc2V0dGluZ3MiCiAgICB9Cl0KAAAAAAAAAABjAwAAAAAAAAAAAAAAAAAAAAAAAGMDAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"]]}, {"file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-settings", "settings": {"buffer_size": 1930, "line_ending": "Unix"}, "undo_stack": [[1, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [4, 1, "revert", null, "AgAAAAAAAAAAAAAAAAAAAAAAAAAqFQAAewogICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09CiAgICAvLyBHTE9CQUwgU0VUVElOR1MKICAgIC8vIFRoZXNlIHNldHRpbmdzIGFwcGx5IHRvIGFsbCBwcm9qZWN0cyB1bmxlc3Mgb3ZlcnJpZGRlbiBpbiBwcm9qZWN0IGZpbGVzCiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0KCiAgICAvLyBFbmFibGUvZGlzYWJsZSBhdXRvLXBsYWNlbWVudCB3aGVuIHRhYnMgYXJlIGFjdGl2YXRlZAogICAgImF1dG9fcGxhY2Vfb25fYWN0aXZhdGlvbiI6IHRydWUsCgogICAgLy8gRW5hYmxlL2Rpc2FibGUgYXV0by1wbGFjZW1lbnQgd2hlbiBmaWxlcyBhcmUgbG9hZGVkCiAgICAiYXV0b19wbGFjZV9vbl9sb2FkIjogdHJ1ZSwKCiAgICAvLyBFbmFibGUvZGlzYWJsZSBkZWJ1ZyBwcmludCBzdGF0ZW1lbnRzCiAgICAiZW5hYmxlX2RlYnVnX3ByaW50cyI6IGZhbHNlLAogICAgCiAgICAvLyBIb3cgdG8gc29ydCB0YWJzIHdpdGhpbiBncm91cHM6ICJhcHBlbmQiLCAicHJlcGVuZCIsICJhbHBoYWJldGljYWwiCiAgICAiZ3JvdXBfc29ydF9tZXRob2QiOiAiYXBwZW5kIiwKICAgIAogICAgLy8gRmlsZSB0eXBlIHJ1bGVzOiBtYXAgZ3JvdXAgaW5kaWNlcyB0byBmaWxlIHBhdHRlcm5zCiAgICAvLyBHcm91cCBpbmRpY2VzIHN0YXJ0IGZyb20gMAogICAgImZpbGVfdHlwZV9ydWxlcyI6IHsKICAgICAgICAiMCI6IFsiLnB5IiwgIi5weXciLCAiKi5weXRob24iXSwKICAgICAgICAiMSI6IFsiLmpzIiwgIi50cyIsICIuanN4IiwgIi50c3giLCAiLmpzb24iXSwKICAgICAgICAiMiI6IFsiLmh0bWwiLCAiLmh0bSIsICIueG1sIiwgIi5jc3MiLCAiLnNjc3MiLCAiLnNhc3MiXSwKICAgICAgICAiMyI6IFsiLm1kIiwgIi50eHQiLCAiLnJzdCIsICIqLm1hcmtkb3duIl0KICAgIH0sCiAgICAKICAgIC8vIERpcmVjdG9yeSBydWxlczogbWFwIGdyb3VwIGluZGljZXMgdG8gZGlyZWN0b3J5IHBhdHRlcm5zCiAgICAvLyBTdXBwb3J0cyBnbG9iIHBhdHRlcm5zIGxpa2UgKi90ZXN0cy8qLCAqL3NyYy8qLCBldGMuCiAgICAiZGlyZWN0b3J5X3J1bGVzIjogewogICAgICAgICIwIjogWyIqL3NyYy8qIiwgIiovbGliLyoiXSwKICAgICAgICAiMSI6IFsiKi90ZXN0cy8qIiwgIiovdGVzdC8qIl0sCiAgICAgICAgIjIiOiBbIiovZG9jcy8qIiwgIiovZG9jdW1lbnRhdGlvbi8qIl0sCiAgICAgICAgIjMiOiBbIiovY29uZmlnLyoiLCAiKi9zZXR0aW5ncy8qIl0KICAgIH0sCiAgICAKICAgIC8vIEdyb3VwIGZvciBmaWxlcyB0aGF0IGFyZSBwYXJ0IG9mIHRoZSBjdXJyZW50IHByb2plY3QKICAgICJwcm9qZWN0X2ZpbGVzX2dyb3VwIjogMCwKICAgIAogICAgLy8gR3JvdXAgZm9yIGZpbGVzIG91dHNpZGUgdGhlIGN1cnJlbnQgcHJvamVjdAogICAgImV4dGVybmFsX2ZpbGVzX2dyb3VwIjogMSwKICAgIAogICAgLy8gR3JvdXAgZm9yIHVuc2F2ZWQvdW50aXRsZWQgZmlsZXMKICAgICJ1bnNhdmVkX2ZpbGVzX2dyb3VwIjogMiwKICAgIAogICAgLy8gRXhjbHVkZSBwYXR0ZXJuczogZmlsZXMgbWF0Y2hpbmcgdGhlc2UgcGF0dGVybnMgd29uJ3QgYmUgYXV0by1wbGFjZWQKICAgICJleGNsdWRlX3BhdHRlcm5zIjogWwogICAgICAgICIqLnRtcCIsCiAgICAgICAgIioudGVtcCIsCiAgICAgICAgIiovLmdpdC8qIiwKICAgICAgICAiKi9ub2RlX21vZHVsZXMvKiIsCiAgICAgICAgIiovX19weWNhY2hlX18vKiIsCiAgICAgICAgIioucHljIgogICAgXSwKICAgIAogICAgLy8gQ3VzdG9tIHJ1bGVzOiBhZHZhbmNlZCBwYXR0ZXJuIG1hdGNoaW5nCiAgICAvLyBFYWNoIHJ1bGUgaGFzOiBwYXR0ZXJuLCBncm91cCwgcHJpb3JpdHkgKGhpZ2hlciBwcmlvcml0eSA9IGNoZWNrZWQgZmlyc3QpCiAgICAiY3VzdG9tX3J1bGVzIjogWwogICAgICAgIHsKICAgICAgICAgICAgIm5hbWUiOiAiQ29uZmlndXJhdGlvbiBmaWxlcyIsCiAgICAgICAgICAgICJwYXR0ZXJuIjogIioue2pzb24seWFtbCx5bWwsdG9tbCxpbmksY2ZnLGNvbmZ9IiwKICAgICAgICAgICAgImdyb3VwIjogMywKICAgICAgICAgICAgInByaW9yaXR5IjogMTAKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgICAgIm5hbWUiOiAiRG9jdW1lbnRhdGlvbiIsCiAgICAgICAgICAgICJwYXR0ZXJuIjogIioue21kLHJzdCx0eHR9IiwKICAgICAgICAgICAgImdyb3VwIjogMiwKICAgICAgICAgICAgInByaW9yaXR5IjogNQogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgICAibmFtZSI6ICJUZXN0IGZpbGVzIiwKICAgICAgICAgICAgInBhdHRlcm4iOiAiKnRlc3QqLnB5IiwKICAgICAgICAgICAgImdyb3VwIjogMSwKICAgICAgICAgICAgInByaW9yaXR5IjogMTUKICAgICAgICB9CiAgICBdLAogICAgCiAgICAvLyBMYXlvdXQgbWFuYWdlbWVudDogYXV0b21hdGljYWxseSBhZGp1c3QgbGF5b3V0IHdoZW4gcGxhY2luZyB0YWJzCiAgICAiYXV0b19hZGp1c3RfbGF5b3V0IjogZmFsc2UsCiAgICAKICAgIC8vIE1pbmltdW0gbnVtYmVyIG9mIGdyb3VwcyB0byBtYWludGFpbgogICAgIm1pbl9ncm91cHMiOiAyLAogICAgCiAgICAvLyBNYXhpbXVtIG51bWJlciBvZiBncm91cHMgdG8gY3JlYXRlCiAgICAibWF4X2dyb3VwcyI6IDQsCiAgICAKICAgIC8vIFByZWZlcnJlZCBsYXlvdXQgY29uZmlndXJhdGlvbnMgZm9yIGRpZmZlcmVudCBncm91cCBjb3VudHMKICAgICJsYXlvdXRfY29uZmlncyI6IHsKICAgICAgICAiMiI6IHsKICAgICAgICAgICAgImNvbHMiOiBbMC4wLCAwLjUsIDEuMF0sCiAgICAgICAgICAgICJyb3dzIjogWzAuMCwgMS4wXSwKICAgICAgICAgICAgImNlbGxzIjogW1swLCAwLCAxLCAxXSwgWzEsIDAsIDIsIDFdXQogICAgICAgIH0sCiAgICAgICAgIjMiOiB7CiAgICAgICAgICAgICJjb2xzIjogWzAuMCwgMC4zMywgMC42NiwgMS4wXSwKICAgICAgICAgICAgInJvd3MiOiBbMC4wLCAxLjBdLAogICAgICAgICAgICAiY2VsbHMiOiBbWzAsIDAsIDEsIDFdLCBbMSwgMCwgMiwgMV0sIFsyLCAwLCAzLCAxXV0KICAgICAgICB9LAogICAgICAgICI0IjogewogICAgICAgICAgICAiY29scyI6IFswLjAsIDAuNSwgMS4wXSwKICAgICAgICAgICAgInJvd3MiOiBbMC4wLCAwLjUsIDEuMF0sCiAgICAgICAgICAgICJjZWxscyI6IFtbMCwgMCwgMSwgMV0sIFsxLCAwLCAyLCAxXSwgWzAsIDEsIDEsIDJdLCBbMSwgMSwgMiwgMl1dCiAgICAgICAgfQogICAgfQoKICAgIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PQogICAgLy8gUFJPSkVDVC1TUEVDSUZJQyBTRVRUSU5HUwogICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09CiAgICAvLwogICAgLy8gWW91IGNhbiBvdmVycmlkZSBhbnkgb2YgdGhlIGFib3ZlIHNldHRpbmdzIG9uIGEgcGVyLXByb2plY3QgYmFzaXMgYnkKICAgIC8vIGFkZGluZyBhICJqb3JuX2F1dG9fcGxhY2VfdGFicyIgc2VjdGlvbiB0byB5b3VyIC5zdWJsaW1lLXByb2plY3QgZmlsZToKICAgIC8vCiAgICAvLyB7CiAgICAvLyAgICAgImZvbGRlcnMiOiBbCiAgICAvLyAgICAgICAgIHsKICAgIC8vICAgICAgICAgICAgICJwYXRoIjogIi4iCiAgICAvLyAgICAgICAgIH0KICAgIC8vICAgICBdLAogICAgLy8gICAgICJzZXR0aW5ncyI6IHsKICAgIC8vICAgICAgICAgImpvcm5fYXV0b19wbGFjZV90YWJzIjogewogICAgLy8gICAgICAgICAgICAgImF1dG9fcGxhY2Vfb25fYWN0aXZhdGlvbiI6IHRydWUsCiAgICAvLyAgICAgICAgICAgICAiZmlsZV90eXBlX3J1bGVzIjogewogICAgLy8gICAgICAgICAgICAgICAgICIwIjogWyIucHkiLCAiLnB5dyJdLAogICAgLy8gICAgICAgICAgICAgICAgICIxIjogWyIuanMiLCAiLnRzIiwgIi5qc3giLCAiLnRzeCJdLAogICAgLy8gICAgICAgICAgICAgICAgICIyIjogWyIuaHRtbCIsICIuY3NzIiwgIi52dWUiXSwKICAgIC8vICAgICAgICAgICAgICAgICAiMyI6IFsiLm1kIiwgIi50eHQiLCAiLmpzb24iXQogICAgLy8gICAgICAgICAgICAgfSwKICAgIC8vICAgICAgICAgICAgICJkaXJlY3RvcnlfcnVsZXMiOiB7CiAgICAvLyAgICAgICAgICAgICAgICAgIjAiOiBbIiovc3JjLyoiLCAiKi9saWIvKiJdLAogICAgLy8gICAgICAgICAgICAgICAgICIxIjogWyIqL3Rlc3RzLyoiLCAiKi90ZXN0LyoiLCAiKi9fX3Rlc3RzX18vKiJdLAogICAgLy8gICAgICAgICAgICAgICAgICIyIjogWyIqL2RvY3MvKiIsICIqL2RvY3VtZW50YXRpb24vKiJdLAogICAgLy8gICAgICAgICAgICAgICAgICIzIjogWyIqL2NvbmZpZy8qIiwgIiovc2V0dGluZ3MvKiJdCiAgICAvLyAgICAgICAgICAgICB9LAogICAgLy8gICAgICAgICAgICAgImN1c3RvbV9ydWxlcyI6IFsKICAgIC8vICAgICAgICAgICAgICAgICB7CiAgICAvLyAgICAgICAgICAgICAgICAgICAgICJuYW1lIjogIlRlc3QgZmlsZXMiLAogICAgLy8gICAgICAgICAgICAgICAgICAgICAicGF0dGVybiI6ICIqdGVzdCoucHkiLAogICAgLy8gICAgICAgICAgICAgICAgICAgICAiZ3JvdXAiOiAxLAogICAgLy8gICAgICAgICAgICAgICAgICAgICAicHJpb3JpdHkiOiAyMAogICAgLy8gICAgICAgICAgICAgICAgIH0sCiAgICAvLyAgICAgICAgICAgICAgICAgewogICAgLy8gICAgICAgICAgICAgICAgICAgICAibmFtZSI6ICJDb25maWcgZmlsZXMiLAogICAgLy8gICAgICAgICAgICAgICAgICAgICAicGF0dGVybiI6ICIqLntqc29uLHlhbWwseW1sLHRvbWx9IiwKICAgIC8vICAgICAgICAgICAgICAgICAgICAgImdyb3VwIjogMywKICAgIC8vICAgICAgICAgICAgICAgICAgICAgInByaW9yaXR5IjogMTUKICAgIC8vICAgICAgICAgICAgICAgICB9CiAgICAvLyAgICAgICAgICAgICBdLAogICAgLy8gICAgICAgICAgICAgInByb2plY3RfZmlsZXNfZ3JvdXAiOiAwLAogICAgLy8gICAgICAgICAgICAgImV4dGVybmFsX2ZpbGVzX2dyb3VwIjogMiwKICAgIC8vICAgICAgICAgICAgICJhdXRvX2FkanVzdF9sYXlvdXQiOiB0cnVlLAogICAgLy8gICAgICAgICAgICAgIm1heF9ncm91cHMiOiA0CiAgICAvLyAgICAgICAgIH0KICAgIC8vICAgICB9CiAgICAvLyB9CiAgICAvLwogICAgLy8gUHJvamVjdC1zcGVjaWZpYyBzZXR0aW5ncyB0YWtlIHByZWNlZGVuY2Ugb3ZlciBnbG9iYWwgc2V0dGluZ3MuCiAgICAvLyBUaGlzIGFsbG93cyB5b3UgdG8gY3VzdG9taXplIHRhYiBwbGFjZW1lbnQgcnVsZXMgZm9yIGVhY2ggcHJvamVjdAogICAgLy8gd2hpbGUgbWFpbnRhaW5pbmcgc2Vuc2libGUgZGVmYXVsdHMgZ2xvYmFsbHkuCn0KAAAAAAAAAACJFgAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAD5BAAAAAAAAPkEAAAAAAAAAAAAAAAA8L8"], [11, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADZBAAAAAAAANkEAAAAAAAAAAAAAAAA8L8"], [12, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADZBAAAAAAAANkEAAAAAAAAAAAAAAAA8L8"], [13, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADZBAAAAAAAANkEAAAAAAAAAAAAAAAA8L8"], [28, 1, "cut", null, "AwAAAO4FAAAAAAAA7gUAAAAAAAAgAAAAR3JvdXAgZm9yIHVuc2F2ZWQvdW50aXRsZWQgZmlsZXOXBQAAAAAAAJcFAAAAAAAAKwAAAEdyb3VwIGZvciBmaWxlcyBvdXRzaWRlIHRoZSBjdXJyZW50IHByb2plY3Q4BQAAAAAAADgFAAAAAAAANAAAAEdyb3VwIGZvciBmaWxlcyB0aGF0IGFyZSBwYXJ0IG9mIHRoZSBjdXJyZW50IHByb2plY3Q", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAMAAAA4BQAAAAAAAGwFAAAAAAAA////////73+XBQAAAAAAAMIFAAAAAAAA////////73/uBQAAAAAAAA4GAAAAAAAA////////738"], [29, 1, "jorn_delete_line_and_align", null, "AwAAAIgFAAAAAAAAiAUAAAAAAAAIAAAAICAgIC8vIApcBQAAAAAAAFwFAAAAAAAACAAAACAgICAvLyAKMQUAAAAAAAAxBQAAAAAAAAgAAAAgICAgLy8gCg", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAMAAAA4BQAAAAAAADgFAAAAAAAAAAAAAAAA8L9jBQAAAAAAAGMFAAAAAAAAAAAAAAAA8L+PBQAAAAAAAI8FAAAAAAAAAAAAAAAA8L8"], [33, 1, "join_lines", null, "AgAAAE4FAAAAAAAATwUAAAAAAAAFAAAACiAgICBOBQAAAAAAAE4FAAAAAAAAAQAAACA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABEBQAAAAAAAEQFAAAAAAAAAAAAAAAA8L8"], [36, 1, "join_lines", null, "AgAAAG0FAAAAAAAAbgUAAAAAAAAFAAAACiAgICBtBQAAAAAAAG0FAAAAAAAAAQAAACA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABqBQAAAAAAAGoFAAAAAAAAAAAAAAAA8L8"], [44, 1, "insert", {"characters": " //"}, "CQAAAIsFAAAAAAAAjAUAAAAAAAAAAAAAbQUAAAAAAABuBQAAAAAAAAAAAABOBQAAAAAAAE8FAAAAAAAAAAAAAI4FAAAAAAAAjwUAAAAAAAAAAAAAbwUAAAAAAABwBQAAAAAAAAAAAABPBQAAAAAAAFAFAAAAAAAAAAAAAJEFAAAAAAAAkgUAAAAAAAAAAAAAcQUAAAAAAAByBQAAAAAAAAAAAABQBQAAAAAAAFEFAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAMAAABOBQAAAAAAAE4FAAAAAAAA////////739tBQAAAAAAAG0FAAAAAAAA////////73+LBQAAAAAAAIsFAAAAAAAA////////738"], [45, 1, "insert", {"characters": " "}, "AwAAAJQFAAAAAAAAlQUAAAAAAAAAAAAAcwUAAAAAAAB0BQAAAAAAAAAAAABRBQAAAAAAAFIFAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAMAAABRBQAAAAAAAFEFAAAAAAAAAAAAAAAA8L9zBQAAAAAAAHMFAAAAAAAAAAAAAAAA8L+UBQAAAAAAAJQFAAAAAAAAAAAAAAAA8L8"], [46, 1, "paste", null, "AwAAAJcFAAAAAAAAtwUAAAAAAAAAAAAAdQUAAAAAAACgBQAAAAAAAAAAAABSBQAAAAAAAIYFAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAMAAABSBQAAAAAAAFIFAAAAAAAAAAAAAAAA8L91BQAAAAAAAHUFAAAAAAAAAAAAAAAA8L+XBQAAAAAAAJcFAAAAAAAAAAAAAAAA8L8"], [53, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAWBgAAAAAAADAFAAAAAAAAAAAAAAAA8L8"], [60, 1, "revert", null, "AgAAAAAAAAAAAAAAAAAAAAAAAACWFgAAewogICAgLy8gPT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09CiAgICAvLyBHTE9CQUwgU0VUVElOR1MKICAgIC8vIFRoZXNlIHNldHRpbmdzIGFwcGx5IHRvIGFsbCBwcm9qZWN0cyB1bmxlc3Mgb3ZlcnJpZGRlbiBpbiBwcm9qZWN0IGZpbGVzCiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0KCiAgICAvLyBFbmFibGUvZGlzYWJsZSBhdXRvLXBsYWNlbWVudCB3aGVuIHRhYnMgYXJlIGFjdGl2YXRlZAogICAgImF1dG9fcGxhY2Vfb25fYWN0aXZhdGlvbiI6IHRydWUsCgogICAgLy8gRW5hYmxlL2Rpc2FibGUgYXV0by1wbGFjZW1lbnQgd2hlbiBmaWxlcyBhcmUgbG9hZGVkCiAgICAiYXV0b19wbGFjZV9vbl9sb2FkIjogdHJ1ZSwKCiAgICAvLyBFbmFibGUvZGlzYWJsZSBkZWJ1ZyBwcmludCBzdGF0ZW1lbnRzCiAgICAiZW5hYmxlX2RlYnVnX3ByaW50cyI6IGZhbHNlLAogICAgCiAgICAvLyBIb3cgdG8gc29ydCB0YWJzIHdpdGhpbiBncm91cHM6ICJhcHBlbmQiLCAicHJlcGVuZCIsICJhbHBoYWJldGljYWwiCiAgICAiZ3JvdXBfc29ydF9tZXRob2QiOiAiYXBwZW5kIiwKICAgIAogICAgLy8gRmlsZSB0eXBlIHJ1bGVzOiBtYXAgZ3JvdXAgaW5kaWNlcyB0byBmaWxlIHBhdHRlcm5zCiAgICAvLyBHcm91cCBpbmRpY2VzIHN0YXJ0IGZyb20gMAogICAgImZpbGVfdHlwZV9ydWxlcyI6IHsKICAgICAgICAiMCI6IFsiLnB5IiwgIi5weXciLCAiKi5weXRob24iXSwKICAgICAgICAiMSI6IFsiLmpzIiwgIi50cyIsICIuanN4IiwgIi50c3giLCAiLmpzb24iXSwKICAgICAgICAiMiI6IFsiLmh0bWwiLCAiLmh0bSIsICIueG1sIiwgIi5jc3MiLCAiLnNjc3MiLCAiLnNhc3MiXSwKICAgICAgICAiMyI6IFsiLm1kIiwgIi50eHQiLCAiLnJzdCIsICIqLm1hcmtkb3duIl0KICAgIH0sCiAgICAKICAgIC8vIERpcmVjdG9yeSBydWxlczogbWFwIGdyb3VwIGluZGljZXMgdG8gZGlyZWN0b3J5IHBhdHRlcm5zCiAgICAvLyBTdXBwb3J0cyBnbG9iIHBhdHRlcm5zIGxpa2UgKi90ZXN0cy8qLCAqL3NyYy8qLCBldGMuCiAgICAiZGlyZWN0b3J5X3J1bGVzIjogewogICAgICAgICIwIjogWyIqL3NyYy8qIiwgIiovbGliLyoiXSwKICAgICAgICAiMSI6IFsiKi90ZXN0cy8qIiwgIiovdGVzdC8qIl0sCiAgICAgICAgIjIiOiBbIiovZG9jcy8qIiwgIiovZG9jdW1lbnRhdGlvbi8qIl0sCiAgICAgICAgIjMiOiBbIiovY29uZmlnLyoiLCAiKi9zZXR0aW5ncy8qIl0KICAgIH0sCiAgICAKICAgICJwcm9qZWN0X2ZpbGVzX2dyb3VwIjogMCwgLy8gR3JvdXAgZm9yIGZpbGVzIHRoYXQgYXJlIHBhcnQgb2YgdGhlIGN1cnJlbnQgcHJvamVjdAogICAgImV4dGVybmFsX2ZpbGVzX2dyb3VwIjogMSwgLy8gR3JvdXAgZm9yIGZpbGVzIG91dHNpZGUgdGhlIGN1cnJlbnQgcHJvamVjdAogICAgInVuc2F2ZWRfZmlsZXNfZ3JvdXAiOiAyLCAvLyBHcm91cCBmb3IgdW5zYXZlZC91bnRpdGxlZCBmaWxlcwogICAgCiAgICAvLyBFeGNsdWRlIHBhdHRlcm5zOiBmaWxlcyBtYXRjaGluZyB0aGVzZSBwYXR0ZXJucyB3b24ndCBiZSBhdXRvLXBsYWNlZAogICAgImV4Y2x1ZGVfcGF0dGVybnMiOiBbCiAgICAgICAgIioudG1wIiwKICAgICAgICAiKi50ZW1wIiwKICAgICAgICAiKi8uZ2l0LyoiLAogICAgICAgICIqL25vZGVfbW9kdWxlcy8qIiwKICAgICAgICAiKi9fX3B5Y2FjaGVfXy8qIiwKICAgICAgICAiKi5weWMiCiAgICBdLAogICAgCiAgICAvLyBDdXN0b20gcnVsZXM6IGFkdmFuY2VkIHBhdHRlcm4gbWF0Y2hpbmcKICAgIC8vIEVhY2ggcnVsZSBoYXM6IHBhdHRlcm4sIGdyb3VwLCBwcmlvcml0eSAoaGlnaGVyIHByaW9yaXR5ID0gY2hlY2tlZCBmaXJzdCkKICAgICJjdXN0b21fcnVsZXMiOiBbCiAgICAgICAgewogICAgICAgICAgICAibmFtZSI6ICJDb25maWd1cmF0aW9uIGZpbGVzIiwKICAgICAgICAgICAgInBhdHRlcm4iOiAiKi57anNvbix5YW1sLHltbCx0b21sLGluaSxjZmcsY29uZn0iLAogICAgICAgICAgICAiZ3JvdXAiOiAzLAogICAgICAgICAgICAicHJpb3JpdHkiOiAxMAogICAgICAgIH0sCiAgICAgICAgewogICAgICAgICAgICAibmFtZSI6ICJEb2N1bWVudGF0aW9uIiwKICAgICAgICAgICAgInBhdHRlcm4iOiAiKi57bWQscnN0LHR4dH0iLAogICAgICAgICAgICAiZ3JvdXAiOiAyLAogICAgICAgICAgICAicHJpb3JpdHkiOiA1CiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICAgICJuYW1lIjogIlRlc3QgZmlsZXMiLAogICAgICAgICAgICAicGF0dGVybiI6ICIqdGVzdCoucHkiLAogICAgICAgICAgICAiZ3JvdXAiOiAxLAogICAgICAgICAgICAicHJpb3JpdHkiOiAxNQogICAgICAgIH0KICAgIF0sCiAgICAKICAgIC8vIExheW91dCBtYW5hZ2VtZW50OiBhdXRvbWF0aWNhbGx5IGFkanVzdCBsYXlvdXQgd2hlbiBwbGFjaW5nIHRhYnMKICAgICJhdXRvX2FkanVzdF9sYXlvdXQiOiBmYWxzZSwKCiAgICAvLyBXaGF0IHRvIGRvIHdoZW4gdGFyZ2V0IGdyb3VwIGRvZXNuJ3QgZXhpc3QgYW5kIGF1dG9fYWRqdXN0X2xheW91dCBpcyBmYWxzZQogICAgLy8gT3B0aW9uczogInNraXAiLCAibGFzdF9ncm91cCIsICJmaXJzdF9ncm91cCIKICAgIC8vIC0gInNraXAiOiBEb24ndCBwbGFjZSB0aGUgdGFiIChyZXNwZWN0cyBleGlzdGluZyBsYXlvdXQpCiAgICAvLyAtICJsYXN0X2dyb3VwIjogUGxhY2UgaW4gdGhlIHJpZ2h0bW9zdCBleGlzdGluZyBncm91cAogICAgLy8gLSAiZmlyc3RfZ3JvdXAiOiBQbGFjZSBpbiB0aGUgbGVmdG1vc3QgZXhpc3RpbmcgZ3JvdXAKICAgICJtaXNzaW5nX2dyb3VwX2JlaGF2aW9yIjogInNraXAiLAoKICAgIC8vIExheW91dCBtb2RlOiBob3cgdG8gaGFuZGxlIGdyb3VwIGNyZWF0aW9uCiAgICAvLyAtICJjb21wYWN0IjogT25seSBjcmVhdGUgZ3JvdXBzIGZvciB0YWJzIHRoYXQgYWN0dWFsbHkgZXhpc3QgKHJlY29tbWVuZGVkKQogICAgLy8gLSAibGl0ZXJhbCI6IENyZWF0ZSBncm91cHMgZXhhY3RseSBhcyBzcGVjaWZpZWQgaW4gcnVsZXMgKG1heSBsZWF2ZSBlbXB0eSBncm91cHMpCiAgICAibGF5b3V0X21vZGUiOiAiY29tcGFjdCIsCgogICAgLy8gTGF5b3V0IHR5cGUgZm9yIGF1dG8tZ2VuZXJhdGVkIGxheW91dHM6ICJjb2x1bW5zIiwgInJvd3MiLCAiZ3JpZCIKICAgICJsYXlvdXRfdHlwZSI6ICJjb2x1bW5zIiwKCiAgICAvLyBDdXN0b20gbGF5b3V0cyBmb3Igc3BlY2lmaWMgZ3JvdXAgY291bnRzIChvcHRpb25hbCkKICAgICJsYXlvdXRfY29uZmlncyI6IHsKICAgICAgICAiOCI6IHsKICAgICAgICAgICAgImNvbHMiOiBbMC4wLCAwLjMzLCAwLjY2LCAxLjBdLAogICAgICAgICAgICAicm93cyI6IFswLjAsIDAuMzMsIDAuNjYsIDEuMF0sCiAgICAgICAgICAgICJjZWxscyI6IFsKICAgICAgICAgICAgICAgIFswLCAwLCAxLCAxXSwgWzEsIDAsIDIsIDFdLCBbMiwgMCwgMywgMV0sCiAgICAgICAgICAgICAgICBbMCwgMSwgMSwgMl0sIFsxLCAxLCAyLCAyXSwgWzIsIDEsIDMsIDJdLAogICAgICAgICAgICAgICAgWzAsIDIsIDEsIDNdLCBbMSwgMiwgMiwgM10KICAgICAgICAgICAgXQogICAgICAgIH0KICAgIH0KCiAgICAvLyA9PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT0KICAgIC8vIFBST0pFQ1QtU1BFQ0lGSUMgU0VUVElOR1MKICAgIC8vID09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PT09PQogICAgLy8KICAgIC8vIFlvdSBjYW4gb3ZlcnJpZGUgYW55IG9mIHRoZSBhYm92ZSBzZXR0aW5ncyBvbiBhIHBlci1wcm9qZWN0IGJhc2lzIGJ5CiAgICAvLyBhZGRpbmcgYSAiam9ybl9hdXRvX3BsYWNlX3RhYnMiIHNlY3Rpb24gdG8geW91ciAuc3VibGltZS1wcm9qZWN0IGZpbGU6CiAgICAvLwogICAgLy8gewogICAgLy8gICAgICJmb2xkZXJzIjogWwogICAgLy8gICAgICAgICB7CiAgICAvLyAgICAgICAgICAgICAicGF0aCI6ICIuIgogICAgLy8gICAgICAgICB9CiAgICAvLyAgICAgXSwKICAgIC8vICAgICAic2V0dGluZ3MiOiB7CiAgICAvLyAgICAgICAgICJqb3JuX2F1dG9fcGxhY2VfdGFicyI6IHsKICAgIC8vICAgICAgICAgICAgICJhdXRvX3BsYWNlX29uX2FjdGl2YXRpb24iOiB0cnVlLAogICAgLy8gICAgICAgICAgICAgImZpbGVfdHlwZV9ydWxlcyI6IHsKICAgIC8vICAgICAgICAgICAgICAgICAiMCI6IFsiLnB5IiwgIi5weXciXSwKICAgIC8vICAgICAgICAgICAgICAgICAiMSI6IFsiLmpzIiwgIi50cyIsICIuanN4IiwgIi50c3giXSwKICAgIC8vICAgICAgICAgICAgICAgICAiMiI6IFsiLmh0bWwiLCAiLmNzcyIsICIudnVlIl0sCiAgICAvLyAgICAgICAgICAgICAgICAgIjMiOiBbIi5tZCIsICIudHh0IiwgIi5qc29uIl0KICAgIC8vICAgICAgICAgICAgIH0sCiAgICAvLyAgICAgICAgICAgICAiZGlyZWN0b3J5X3J1bGVzIjogewogICAgLy8gICAgICAgICAgICAgICAgICIwIjogWyIqL3NyYy8qIiwgIiovbGliLyoiXSwKICAgIC8vICAgICAgICAgICAgICAgICAiMSI6IFsiKi90ZXN0cy8qIiwgIiovdGVzdC8qIiwgIiovX190ZXN0c19fLyoiXSwKICAgIC8vICAgICAgICAgICAgICAgICAiMiI6IFsiKi9kb2NzLyoiLCAiKi9kb2N1bWVudGF0aW9uLyoiXSwKICAgIC8vICAgICAgICAgICAgICAgICAiMyI6IFsiKi9jb25maWcvKiIsICIqL3NldHRpbmdzLyoiXQogICAgLy8gICAgICAgICAgICAgfSwKICAgIC8vICAgICAgICAgICAgICJjdXN0b21fcnVsZXMiOiBbCiAgICAvLyAgICAgICAgICAgICAgICAgewogICAgLy8gICAgICAgICAgICAgICAgICAgICAibmFtZSI6ICJUZXN0IGZpbGVzIiwKICAgIC8vICAgICAgICAgICAgICAgICAgICAgInBhdHRlcm4iOiAiKnRlc3QqLnB5IiwKICAgIC8vICAgICAgICAgICAgICAgICAgICAgImdyb3VwIjogMSwKICAgIC8vICAgICAgICAgICAgICAgICAgICAgInByaW9yaXR5IjogMjAKICAgIC8vICAgICAgICAgICAgICAgICB9LAogICAgLy8gICAgICAgICAgICAgICAgIHsKICAgIC8vICAgICAgICAgICAgICAgICAgICAgIm5hbWUiOiAiQ29uZmlnIGZpbGVzIiwKICAgIC8vICAgICAgICAgICAgICAgICAgICAgInBhdHRlcm4iOiAiKi57anNvbix5YW1sLHltbCx0b21sfSIsCiAgICAvLyAgICAgICAgICAgICAgICAgICAgICJncm91cCI6IDMsCiAgICAvLyAgICAgICAgICAgICAgICAgICAgICJwcmlvcml0eSI6IDE1CiAgICAvLyAgICAgICAgICAgICAgICAgfQogICAgLy8gICAgICAgICAgICAgXSwKICAgIC8vICAgICAgICAgICAgICJwcm9qZWN0X2ZpbGVzX2dyb3VwIjogMCwKICAgIC8vICAgICAgICAgICAgICJleHRlcm5hbF9maWxlc19ncm91cCI6IDIsCiAgICAvLyAgICAgICAgICAgICAiYXV0b19hZGp1c3RfbGF5b3V0IjogdHJ1ZSwKICAgIC8vICAgICAgICAgICAgICJtYXhfZ3JvdXBzIjogNAogICAgLy8gICAgICAgICB9CiAgICAvLyAgICAgfQogICAgLy8gfQogICAgLy8KICAgIC8vIFByb2plY3Qtc3BlY2lmaWMgc2V0dGluZ3MgdGFrZSBwcmVjZWRlbmNlIG92ZXIgZ2xvYmFsIHNldHRpbmdzLgogICAgLy8gVGhpcyBhbGxvd3MgeW91IHRvIGN1c3RvbWl6ZSB0YWIgcGxhY2VtZW50IHJ1bGVzIGZvciBlYWNoIHByb2plY3QKICAgIC8vIHdoaWxlIG1haW50YWluaW5nIHNlbnNpYmxlIGRlZmF1bHRzIGdsb2JhbGx5Lgp9CgAAAAAAAAAAnxYAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAArBQAAAAAAACsFAAAAAAAAAAAAAAAA8L8"], [64, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAwBQAAAAAAADAFAAAAAAAAAAAAAAAA8L8"], [65, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAwBQAAAAAAADAFAAAAAAAAAAAAAAAA8L8"], [66, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAwBQAAAAAAADAFAAAAAAAAAAAAAAAA8L8"], [67, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAwBQAAAAAAADAFAAAAAAAAAAAAAAAA8L8"]]}, {"file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-project", "settings": {"buffer_size": 537, "line_ending": "Unix"}}, {"file": "refs/my_sublime_packages/User/Plain text.sublime-settings", "settings": {"buffer_size": 33, "line_ending": "Windows"}}, {"file": "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.py", "settings": {"buffer_size": 32776, "encoding": "UTF-8", "line_ending": "Unix"}, "undo_stack": [[5, 1, "revert", null, "AgAAAAAAAAAAAAAAAAAAAAAAAAAMcgAAaW1wb3J0IHN1YmxpbWUKaW1wb3J0IHN1YmxpbWVfcGx1Z2luCmltcG9ydCBvcwppbXBvcnQgdGltZQppbXBvcnQgZm5tYXRjaApmcm9tIGNvbGxlY3Rpb25zIGltcG9ydCBkZWZhdWx0ZGljdCwgZGVxdWUKClBMVUdJTl9OQU1FID0gIkpvcm5fQXV0b1BsYWNlVGFicyIKTUFYX1BMQUNFTUVOVFNfUEVSX1NFQ09ORCA9IDUKCgpjbGFzcyBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kKHN1YmxpbWVfcGx1Z2luLkV2ZW50TGlzdGVuZXIpOgogICAgIiIiCiAgICBNYWluIHBsdWdpbiB0aGF0IGF1dG9tYXRpY2FsbHkgcGxhY2VzIHRhYnMgaW4gYXBwcm9wcmlhdGUgZ3JvdXBzIGJhc2VkIG9uOgogICAgLSBGaWxlIHR5cGUvZXh0ZW5zaW9uIHBhdHRlcm5zCiAgICAtIERpcmVjdG9yeSBwYXR0ZXJucyAgCiAgICAtIFByb2plY3QgbWVtYmVyc2hpcAogICAgLSBDdXN0b20gdXNlci1kZWZpbmVkIHJ1bGVzCiAgICAKICAgIFByZXZlbnRzIGluZmluaXRlIGxvb3BzIHZpYToKICAgIDEpIFJlY3Vyc2lvbiBndWFyZCAoX2lzX3BsYWNpbmcpCiAgICAyKSBGcmVxdWVuY3ktYmFzZWQgbGltaXQgKE1BWF9QTEFDRU1FTlRTX1BFUl9TRUNPTkQpCiAgICAzKSBQbGFjZW1lbnQgaGlzdG9yeSB0cmFja2luZwogICAgIiIiCgogICAgX2luc3RhbmNlID0gTm9uZQoKICAgIGRlZiBfX2luaXRfXyhzZWxmKToKICAgICAgICBzdXBlcigpLl9faW5pdF9fKCkKICAgICAgICBzZWxmLnNldHRpbmdzID0gc3VibGltZS5sb2FkX3NldHRpbmdzKGYie1BMVUdJTl9OQU1FfS5zdWJsaW1lLXNldHRpbmdzIikKICAgICAgICBzZWxmLl9pc19wbGFjaW5nID0gRmFsc2UKICAgICAgICBzZWxmLl9wbGFjZW1lbnRfdGltZXN0YW1wcyA9IGRlcXVlKCkKICAgICAgICBzZWxmLl9sYXN0X3BsYWNlbWVudHMgPSBkZWZhdWx0ZGljdChsYW1iZGE6IGRlZmF1bHRkaWN0KHR1cGxlKSkKICAgICAgICBzZWxmLl9wcm9qZWN0X3NldHRpbmdzX2NhY2hlID0ge30gICMgQ2FjaGUgcHJvamVjdCBzZXR0aW5ncyBieSB3aW5kb3cgSUQKICAgICAgICBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kLl9pbnN0YW5jZSA9IHNlbGYKCiAgICBAY2xhc3NtZXRob2QKICAgIGRlZiBpbnN0YW5jZShjbHMpOgogICAgICAgICIiIlVzZWQgYnkgbWFudWFsIHBsYWNlbWVudCBjb21tYW5kcyB0byByZWZlcmVuY2UgdGhpcyBwbHVnaW4gaW5zdGFuY2UuIiIiCiAgICAgICAgcmV0dXJuIGNscy5faW5zdGFuY2UKCiAgICBkZWYgX2RlYnVnX3ByaW50KHNlbGYsIG1lc3NhZ2UpOgogICAgICAgICIiIlByaW50IGRlYnVnIG1lc3NhZ2Ugb25seSBpZiBkZWJ1ZyBtb2RlIGlzIGVuYWJsZWQuIiIiCiAgICAgICAgaWYgc2VsZi5zZXR0aW5ncy5nZXQoImVuYWJsZV9kZWJ1Z19wcmludHMiLCBGYWxzZSk6CiAgICAgICAgICAgIHByaW50KGYiW3tQTFVHSU5fTkFNRX1dIHttZXNzYWdlfSIpCgogICAgZGVmIF9nZXRfZWZmZWN0aXZlX3NldHRpbmdzKHNlbGYsIHdpbmRvdyk6CiAgICAgICAgIiIiR2V0IGVmZmVjdGl2ZSBzZXR0aW5ncyBjb21iaW5pbmcgZ2xvYmFsIGFuZCBwcm9qZWN0LXNwZWNpZmljIHNldHRpbmdzLiIiIgogICAgICAgIGlmIG5vdCB3aW5kb3c6CiAgICAgICAgICAgIHJldHVybiBzZWxmLnNldHRpbmdzCgogICAgICAgIHdpbmRvd19pZCA9IHdpbmRvdy5pZCgpCgogICAgICAgICMgQ2hlY2sgY2FjaGUgZmlyc3QKICAgICAgICBpZiB3aW5kb3dfaWQgaW4gc2VsZi5fcHJvamVjdF9zZXR0aW5nc19jYWNoZToKICAgICAgICAgICAgcmV0dXJuIHNlbGYuX3Byb2plY3Rfc2V0dGluZ3NfY2FjaGVbd2luZG93X2lkXQoKICAgICAgICAjIFN0YXJ0IHdpdGggZ2xvYmFsIHNldHRpbmdzCiAgICAgICAgZWZmZWN0aXZlX3NldHRpbmdzID0ge30KCiAgICAgICAgIyBDb3B5IGFsbCBnbG9iYWwgc2V0dGluZ3MKICAgICAgICBmb3Iga2V5IGluIFsiYXV0b19wbGFjZV9vbl9hY3RpdmF0aW9uIiwgImF1dG9fcGxhY2Vfb25fbG9hZCIsICJlbmFibGVfZGVidWdfcHJpbnRzIiwKICAgICAgICAgICAgICAgICAgICJncm91cF9zb3J0X21ldGhvZCIsICJmaWxlX3R5cGVfcnVsZXMiLCAiZGlyZWN0b3J5X3J1bGVzIiwgInByb2plY3RfZmlsZXNfZ3JvdXAiLAogICAgICAgICAgICAgICAgICAgImV4dGVybmFsX2ZpbGVzX2dyb3VwIiwgInVuc2F2ZWRfZmlsZXNfZ3JvdXAiLCAiZXhjbHVkZV9wYXR0ZXJucyIsICJjdXN0b21fcnVsZXMiLAogICAgICAgICAgICAgICAgICAgImF1dG9fYWRqdXN0X2xheW91dCIsICJtaXNzaW5nX2dyb3VwX2JlaGF2aW9yIiwgImxheW91dF90eXBlIiwgImxheW91dF9jb25maWdzIl06CiAgICAgICAgICAgIGVmZmVjdGl2ZV9zZXR0aW5nc1trZXldID0gc2VsZi5zZXR0aW5ncy5nZXQoa2V5KQoKICAgICAgICAjIEdldCBwcm9qZWN0LXNwZWNpZmljIHNldHRpbmdzCiAgICAgICAgcHJvamVjdF9kYXRhID0gd2luZG93LnByb2plY3RfZGF0YSgpCiAgICAgICAgaWYgcHJvamVjdF9kYXRhIGFuZCAic2V0dGluZ3MiIGluIHByb2plY3RfZGF0YToKICAgICAgICAgICAgcHJvamVjdF9zZXR0aW5ncyA9IHByb2plY3RfZGF0YVsic2V0dGluZ3MiXS5nZXQoImpvcm5fYXV0b19wbGFjZV90YWJzIiwge30pCiAgICAgICAgICAgIGlmIHByb2plY3Rfc2V0dGluZ3M6CiAgICAgICAgICAgICAgICBzZWxmLl9kZWJ1Z19wcmludChmIkZvdW5kIHByb2plY3Qtc3BlY2lmaWMgc2V0dGluZ3M6IHtsaXN0KHByb2plY3Rfc2V0dGluZ3Mua2V5cygpKX0iKQogICAgICAgICAgICAgICAgIyBPdmVycmlkZSBnbG9iYWwgc2V0dGluZ3Mgd2l0aCBwcm9qZWN0LXNwZWNpZmljIG9uZXMKICAgICAgICAgICAgICAgIGVmZmVjdGl2ZV9zZXR0aW5ncy51cGRhdGUocHJvamVjdF9zZXR0aW5ncykKCiAgICAgICAgIyBDYWNoZSB0aGUgcmVzdWx0CiAgICAgICAgc2VsZi5fcHJvamVjdF9zZXR0aW5nc19jYWNoZVt3aW5kb3dfaWRdID0gZWZmZWN0aXZlX3NldHRpbmdzCgogICAgICAgIHJldHVybiBlZmZlY3RpdmVfc2V0dGluZ3MKCiAgICBkZWYgX2NsZWFyX3NldHRpbmdzX2NhY2hlKHNlbGYsIHdpbmRvd19pZD1Ob25lKToKICAgICAgICAiIiJDbGVhciBzZXR0aW5ncyBjYWNoZSBmb3IgYSBzcGVjaWZpYyB3aW5kb3cgb3IgYWxsIHdpbmRvd3MuIiIiCiAgICAgICAgaWYgd2luZG93X2lkOgogICAgICAgICAgICBzZWxmLl9wcm9qZWN0X3NldHRpbmdzX2NhY2hlLnBvcCh3aW5kb3dfaWQsIE5vbmUpCiAgICAgICAgZWxzZToKICAgICAgICAgICAgc2VsZi5fcHJvamVjdF9zZXR0aW5nc19jYWNoZS5jbGVhcigpCgogICAgZGVmIF9nZXRfc2V0dGluZyhzZWxmLCBrZXksIGRlZmF1bHQ9Tm9uZSwgd2luZG93PU5vbmUpOgogICAgICAgICIiIkdldCBhIHNldHRpbmcgdmFsdWUgZnJvbSBlZmZlY3RpdmUgc2V0dGluZ3MgKGdsb2JhbCArIHByb2plY3Qtc3BlY2lmaWMpLiIiIgogICAgICAgIGlmIHdpbmRvdzoKICAgICAgICAgICAgZWZmZWN0aXZlX3NldHRpbmdzID0gc2VsZi5fZ2V0X2VmZmVjdGl2ZV9zZXR0aW5ncyh3aW5kb3cpCiAgICAgICAgICAgIHJldHVybiBlZmZlY3RpdmVfc2V0dGluZ3MuZ2V0KGtleSwgZGVmYXVsdCkKICAgICAgICBlbHNlOgogICAgICAgICAgICByZXR1cm4gc2VsZi5zZXR0aW5ncy5nZXQoa2V5LCBkZWZhdWx0KQoKICAgIGRlZiBfY2hlY2tfcGxhY2VtZW50X2ZyZXF1ZW5jeShzZWxmKToKICAgICAgICAiIiJSYXRlIGxpbWl0aW5nIHRvIHByZXZlbnQgZXhjZXNzaXZlIHBsYWNlbWVudHMuIiIiCiAgICAgICAgbm93ID0gdGltZS50aW1lKCkKICAgICAgICBzZWxmLl9wbGFjZW1lbnRfdGltZXN0YW1wcy5hcHBlbmQobm93KQogICAgICAgIAogICAgICAgICMgUmVtb3ZlIHRpbWVzdGFtcHMgb2xkZXIgdGhhbiAxIHNlY29uZAogICAgICAgIHdoaWxlIChzZWxmLl9wbGFjZW1lbnRfdGltZXN0YW1wcyBhbmQgCiAgICAgICAgICAgICAgIG5vdyAtIHNlbGYuX3BsYWNlbWVudF90aW1lc3RhbXBzWzBdID4gMS4wKToKICAgICAgICAgICAgc2VsZi5fcGxhY2VtZW50X3RpbWVzdGFtcHMucG9wbGVmdCgpCiAgICAgICAgCiAgICAgICAgcmV0dXJuIGxlbihzZWxmLl9wbGFjZW1lbnRfdGltZXN0YW1wcykgPD0gTUFYX1BMQUNFTUVOVFNfUEVSX1NFQ09ORAoKICAgIGRlZiBvbl9hY3RpdmF0ZWRfYXN5bmMoc2VsZiwgdmlldyk6CiAgICAgICAgIiIiSGFuZGxlIHRhYiBhY3RpdmF0aW9uIGZvciBhdXRvLXBsYWNlbWVudC4iIiIKICAgICAgICBpZiBub3QgdmlldyBvciBub3Qgdmlldy53aW5kb3coKSBvciBzZWxmLl9pc19wbGFjaW5nOgogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgd2luZG93ID0gdmlldy53aW5kb3coKQogICAgICAgIGlmIG5vdCBzZWxmLl9nZXRfc2V0dGluZygiYXV0b19wbGFjZV9vbl9hY3RpdmF0aW9uIiwgVHJ1ZSwgd2luZG93KToKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIGlmIG5vdCBzZWxmLl9zaG91bGRfYXV0b19wbGFjZSh2aWV3KToKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIGlmIG5vdCBzZWxmLl9jaGVja19wbGFjZW1lbnRfZnJlcXVlbmN5KCk6CiAgICAgICAgICAgIHNlbGYuX2RlYnVnX3ByaW50KCJSYXRlIGxpbWl0IGV4Y2VlZGVkLCBza2lwcGluZyBwbGFjZW1lbnQiKQogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgc2VsZi5fcGxhY2VfdGFiKHZpZXcpCgogICAgZGVmIG9uX2xvYWRfYXN5bmMoc2VsZiwgdmlldyk6CiAgICAgICAgIiIiSGFuZGxlIGZpbGUgbG9hZCBmb3IgYXV0by1wbGFjZW1lbnQuIiIiCiAgICAgICAgaWYgbm90IHZpZXcgb3Igbm90IHZpZXcud2luZG93KCkgb3Igc2VsZi5faXNfcGxhY2luZzoKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIHdpbmRvdyA9IHZpZXcud2luZG93KCkKICAgICAgICBpZiBub3Qgc2VsZi5fZ2V0X3NldHRpbmcoImF1dG9fcGxhY2Vfb25fbG9hZCIsIFRydWUsIHdpbmRvdyk6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICBpZiBub3Qgc2VsZi5fc2hvdWxkX2F1dG9fcGxhY2Uodmlldyk6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICBpZiBub3Qgc2VsZi5fY2hlY2tfcGxhY2VtZW50X2ZyZXF1ZW5jeSgpOgogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgIyBTbWFsbCBkZWxheSB0byBlbnN1cmUgZmlsZSBpcyBmdWxseSBsb2FkZWQKICAgICAgICBzdWJsaW1lLnNldF90aW1lb3V0X2FzeW5jKGxhbWJkYTogc2VsZi5fcGxhY2VfdGFiKHZpZXcpLCAxMDApCgogICAgZGVmIG9uX3dpbmRvd19jb21tYW5kKHNlbGYsIHdpbmRvdywgY29tbWFuZF9uYW1lLCBhcmdzKToKICAgICAgICAiIiJIYW5kbGUgd2luZG93IGNvbW1hbmRzIHRoYXQgbWlnaHQgY2hhbmdlIHByb2plY3Qgc2V0dGluZ3MuIiIiCiAgICAgICAgaWYgY29tbWFuZF9uYW1lIGluIFsib3Blbl9wcm9qZWN0IiwgImNsb3NlX3Byb2plY3QiLCAic3dpdGNoX3Byb2plY3QiXToKICAgICAgICAgICAgIyBDbGVhciBzZXR0aW5ncyBjYWNoZSB3aGVuIHByb2plY3QgY2hhbmdlcwogICAgICAgICAgICBzZWxmLl9jbGVhcl9zZXR0aW5nc19jYWNoZSh3aW5kb3cuaWQoKSkKCiAgICBkZWYgb25fcG9zdF93aW5kb3dfY29tbWFuZChzZWxmLCB3aW5kb3csIGNvbW1hbmRfbmFtZSwgYXJncyk6CiAgICAgICAgIiIiSGFuZGxlIHBvc3Qtd2luZG93IGNvbW1hbmRzIHRoYXQgbWlnaHQgY2hhbmdlIHByb2plY3Qgc2V0dGluZ3MuIiIiCiAgICAgICAgaWYgY29tbWFuZF9uYW1lIGluIFsib3Blbl9wcm9qZWN0IiwgImNsb3NlX3Byb2plY3QiLCAic3dpdGNoX3Byb2plY3QiXToKICAgICAgICAgICAgIyBDbGVhciBzZXR0aW5ncyBjYWNoZSB3aGVuIHByb2plY3QgY2hhbmdlcwogICAgICAgICAgICBzZWxmLl9jbGVhcl9zZXR0aW5nc19jYWNoZSh3aW5kb3cuaWQoKSkKCiAgICBkZWYgX3Nob3VsZF9hdXRvX3BsYWNlKHNlbGYsIHZpZXcpOgogICAgICAgICIiIkRldGVybWluZSBpZiBhIHZpZXcgc2hvdWxkIGJlIGF1dG8tcGxhY2VkLiIiIgogICAgICAgIGlmIG5vdCB2aWV3IG9yIG5vdCB2aWV3LndpbmRvdygpOgogICAgICAgICAgICByZXR1cm4gRmFsc2UKCiAgICAgICAgIyBTa2lwIGlmIGFscmVhZHkgaW4gY29ycmVjdCBncm91cAogICAgICAgIHRhcmdldF9ncm91cCA9IHNlbGYuX2RldGVybWluZV90YXJnZXRfZ3JvdXAodmlldykKICAgICAgICBpZiB0YXJnZXRfZ3JvdXAgaXMgTm9uZToKICAgICAgICAgICAgcmV0dXJuIEZhbHNlCgogICAgICAgIGN1cnJlbnRfZ3JvdXAsIF8gPSB2aWV3LndpbmRvdygpLmdldF92aWV3X2luZGV4KHZpZXcpCiAgICAgICAgcmV0dXJuIGN1cnJlbnRfZ3JvdXAgIT0gdGFyZ2V0X2dyb3VwCgogICAgZGVmIF9kZXRlcm1pbmVfdGFyZ2V0X2dyb3VwKHNlbGYsIHZpZXcpOgogICAgICAgICIiIkRldGVybWluZSB0aGUgdGFyZ2V0IGdyb3VwIGZvciBhIHZpZXcgYmFzZWQgb24gcGxhY2VtZW50IHJ1bGVzLiIiIgogICAgICAgIHdpbmRvdyA9IHZpZXcud2luZG93KCkKICAgICAgICBpZiBub3Qgd2luZG93OgogICAgICAgICAgICByZXR1cm4gTm9uZQoKICAgICAgICBmaWxlX3BhdGggPSB2aWV3LmZpbGVfbmFtZSgpCiAgICAgICAgaWYgbm90IGZpbGVfcGF0aDoKICAgICAgICAgICAgcmV0dXJuIHNlbGYuX2dldF9ncm91cF9mb3JfdW5zYXZlZCh2aWV3KQoKICAgICAgICAjIENoZWNrIGlmIGZpbGUgc2hvdWxkIGJlIGV4Y2x1ZGVkCiAgICAgICAgaWYgc2VsZi5fc2hvdWxkX2V4Y2x1ZGVfZmlsZShmaWxlX3BhdGgsIHdpbmRvdyk6CiAgICAgICAgICAgIHJldHVybiBOb25lCgogICAgICAgICMgQ2hlY2sgY3VzdG9tIHJ1bGVzIGZpcnN0IChoaWdoZXN0IHByaW9yaXR5KQogICAgICAgIHRhcmdldF9ncm91cCA9IHNlbGYuX2NoZWNrX2N1c3RvbV9ydWxlcyhmaWxlX3BhdGgsIHdpbmRvdykKICAgICAgICBpZiB0YXJnZXRfZ3JvdXAgaXMgbm90IE5vbmU6CiAgICAgICAgICAgIHJldHVybiB0YXJnZXRfZ3JvdXAKCiAgICAgICAgIyBDaGVjayBmaWxlIHR5cGUgcnVsZXMKICAgICAgICB0YXJnZXRfZ3JvdXAgPSBzZWxmLl9jaGVja19maWxlX3R5cGVfcnVsZXMoZmlsZV9wYXRoLCB3aW5kb3cpCiAgICAgICAgaWYgdGFyZ2V0X2dyb3VwIGlzIG5vdCBOb25lOgogICAgICAgICAgICByZXR1cm4gdGFyZ2V0X2dyb3VwCgogICAgICAgICMgQ2hlY2sgZGlyZWN0b3J5IHJ1bGVzCiAgICAgICAgdGFyZ2V0X2dyb3VwID0gc2VsZi5fY2hlY2tfZGlyZWN0b3J5X3J1bGVzKGZpbGVfcGF0aCwgd2luZG93KQogICAgICAgIGlmIHRhcmdldF9ncm91cCBpcyBub3QgTm9uZToKICAgICAgICAgICAgcmV0dXJuIHRhcmdldF9ncm91cAoKICAgICAgICAjIENoZWNrIHByb2plY3QgbWVtYmVyc2hpcAogICAgICAgIHRhcmdldF9ncm91cCA9IHNlbGYuX2NoZWNrX3Byb2plY3RfcnVsZXModmlldywgZmlsZV9wYXRoLCB3aW5kb3cpCiAgICAgICAgaWYgdGFyZ2V0X2dyb3VwIGlzIG5vdCBOb25lOgogICAgICAgICAgICByZXR1cm4gdGFyZ2V0X2dyb3VwCgogICAgICAgIHJldHVybiBOb25lCgogICAgZGVmIF9jaGVja19maWxlX3R5cGVfcnVsZXMoc2VsZiwgZmlsZV9wYXRoLCB3aW5kb3c9Tm9uZSk6CiAgICAgICAgIiIiQ2hlY2sgZmlsZSB0eXBlL2V4dGVuc2lvbiBydWxlcy4iIiIKICAgICAgICBmaWxlX2V4dCA9IG9zLnBhdGguc3BsaXRleHQoZmlsZV9wYXRoKVsxXS5sb3dlcigpCiAgICAgICAgZmlsZV9uYW1lID0gb3MucGF0aC5iYXNlbmFtZShmaWxlX3BhdGgpCgogICAgICAgIHR5cGVfcnVsZXMgPSBzZWxmLl9nZXRfc2V0dGluZygiZmlsZV90eXBlX3J1bGVzIiwge30sIHdpbmRvdykKCiAgICAgICAgZm9yIGdyb3VwX2luZGV4LCBwYXR0ZXJucyBpbiB0eXBlX3J1bGVzLml0ZW1zKCk6CiAgICAgICAgICAgIHRyeToKICAgICAgICAgICAgICAgIGdyb3VwX251bSA9IGludChncm91cF9pbmRleCkKICAgICAgICAgICAgICAgIGZvciBwYXR0ZXJuIGluIHBhdHRlcm5zOgogICAgICAgICAgICAgICAgICAgIGlmIChwYXR0ZXJuLnN0YXJ0c3dpdGgoJy4nKSBhbmQgZmlsZV9leHQgPT0gcGF0dGVybi5sb3dlcigpKSBvciBcCiAgICAgICAgICAgICAgICAgICAgICAgZm5tYXRjaC5mbm1hdGNoKGZpbGVfbmFtZS5sb3dlcigpLCBwYXR0ZXJuLmxvd2VyKCkpOgogICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gZ3JvdXBfbnVtCiAgICAgICAgICAgIGV4Y2VwdCAoVmFsdWVFcnJvciwgVHlwZUVycm9yKToKICAgICAgICAgICAgICAgIGNvbnRpbnVlCgogICAgICAgIHJldHVybiBOb25lCgogICAgZGVmIF9jaGVja19kaXJlY3RvcnlfcnVsZXMoc2VsZiwgZmlsZV9wYXRoLCB3aW5kb3c9Tm9uZSk6CiAgICAgICAgIiIiQ2hlY2sgZGlyZWN0b3J5LWJhc2VkIHJ1bGVzLiIiIgogICAgICAgIGRpcl9wYXRoID0gb3MucGF0aC5kaXJuYW1lKGZpbGVfcGF0aCkKICAgICAgICBkaXJfcnVsZXMgPSBzZWxmLl9nZXRfc2V0dGluZygiZGlyZWN0b3J5X3J1bGVzIiwge30sIHdpbmRvdykKCiAgICAgICAgZm9yIGdyb3VwX2luZGV4LCBwYXR0ZXJucyBpbiBkaXJfcnVsZXMuaXRlbXMoKToKICAgICAgICAgICAgdHJ5OgogICAgICAgICAgICAgICAgZ3JvdXBfbnVtID0gaW50KGdyb3VwX2luZGV4KQogICAgICAgICAgICAgICAgZm9yIHBhdHRlcm4gaW4gcGF0dGVybnM6CiAgICAgICAgICAgICAgICAgICAgaWYgZm5tYXRjaC5mbm1hdGNoKGRpcl9wYXRoLCBwYXR0ZXJuKToKICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGdyb3VwX251bQogICAgICAgICAgICBleGNlcHQgKFZhbHVlRXJyb3IsIFR5cGVFcnJvcik6CiAgICAgICAgICAgICAgICBjb250aW51ZQoKICAgICAgICByZXR1cm4gTm9uZQoKICAgIGRlZiBfY2hlY2tfcHJvamVjdF9ydWxlcyhzZWxmLCB2aWV3LCBmaWxlX3BhdGgsIHdpbmRvdz1Ob25lKToKICAgICAgICAiIiJDaGVjayBwcm9qZWN0IG1lbWJlcnNoaXAgcnVsZXMuIiIiCiAgICAgICAgaWYgbm90IHdpbmRvdzoKICAgICAgICAgICAgd2luZG93ID0gdmlldy53aW5kb3coKQogICAgICAgIHByb2plY3RfZm9sZGVycyA9IHdpbmRvdy5mb2xkZXJzKCkgaWYgd2luZG93IGVsc2UgW10KCiAgICAgICAgaXNfaW5fcHJvamVjdCA9IGFueShmaWxlX3BhdGguc3RhcnRzd2l0aChmb2xkZXIpIGZvciBmb2xkZXIgaW4gcHJvamVjdF9mb2xkZXJzKQoKICAgICAgICBpZiBpc19pbl9wcm9qZWN0OgogICAgICAgICAgICByZXR1cm4gc2VsZi5fZ2V0X3NldHRpbmcoInByb2plY3RfZmlsZXNfZ3JvdXAiLCBOb25lLCB3aW5kb3cpCiAgICAgICAgZWxzZToKICAgICAgICAgICAgcmV0dXJuIHNlbGYuX2dldF9zZXR0aW5nKCJleHRlcm5hbF9maWxlc19ncm91cCIsIE5vbmUsIHdpbmRvdykKCiAgICBkZWYgX2dldF9ncm91cF9mb3JfdW5zYXZlZChzZWxmLCB2aWV3KToKICAgICAgICAiIiJEZXRlcm1pbmUgZ3JvdXAgZm9yIHVuc2F2ZWQgZmlsZXMuIiIiCiAgICAgICAgd2luZG93ID0gdmlldy53aW5kb3coKQogICAgICAgIHJldHVybiBzZWxmLl9nZXRfc2V0dGluZygidW5zYXZlZF9maWxlc19ncm91cCIsIE5vbmUsIHdpbmRvdykKCiAgICBkZWYgX3Nob3VsZF9leGNsdWRlX2ZpbGUoc2VsZiwgZmlsZV9wYXRoLCB3aW5kb3c9Tm9uZSk6CiAgICAgICAgIiIiQ2hlY2sgaWYgZmlsZSBzaG91bGQgYmUgZXhjbHVkZWQgZnJvbSBhdXRvLXBsYWNlbWVudC4iIiIKICAgICAgICBleGNsdWRlX3BhdHRlcm5zID0gc2VsZi5fZ2V0X3NldHRpbmcoImV4Y2x1ZGVfcGF0dGVybnMiLCBbXSwgd2luZG93KQogICAgICAgIGZpbGVfbmFtZSA9IG9zLnBhdGguYmFzZW5hbWUoZmlsZV9wYXRoKQoKICAgICAgICBmb3IgcGF0dGVybiBpbiBleGNsdWRlX3BhdHRlcm5zOgogICAgICAgICAgICBpZiBmbm1hdGNoLmZubWF0Y2goZmlsZV9wYXRoLCBwYXR0ZXJuKSBvciBmbm1hdGNoLmZubWF0Y2goZmlsZV9uYW1lLCBwYXR0ZXJuKToKICAgICAgICAgICAgICAgIHJldHVybiBUcnVlCiAgICAgICAgcmV0dXJuIEZhbHNlCgogICAgZGVmIF9jaGVja19jdXN0b21fcnVsZXMoc2VsZiwgZmlsZV9wYXRoLCB3aW5kb3c9Tm9uZSk6CiAgICAgICAgIiIiQ2hlY2sgY3VzdG9tIHJ1bGVzIHdpdGggcHJpb3JpdHkgb3JkZXJpbmcuIiIiCiAgICAgICAgY3VzdG9tX3J1bGVzID0gc2VsZi5fZ2V0X3NldHRpbmcoImN1c3RvbV9ydWxlcyIsIFtdLCB3aW5kb3cpCiAgICAgICAgaWYgbm90IGN1c3RvbV9ydWxlczoKICAgICAgICAgICAgcmV0dXJuIE5vbmUKCiAgICAgICAgIyBTb3J0IGJ5IHByaW9yaXR5IChoaWdoZXIgZmlyc3QpCiAgICAgICAgc29ydGVkX3J1bGVzID0gc29ydGVkKGN1c3RvbV9ydWxlcywga2V5PWxhbWJkYSByOiByLmdldCgicHJpb3JpdHkiLCAwKSwgcmV2ZXJzZT1UcnVlKQoKICAgICAgICBmaWxlX25hbWUgPSBvcy5wYXRoLmJhc2VuYW1lKGZpbGVfcGF0aCkKICAgICAgICBmb3IgcnVsZSBpbiBzb3J0ZWRfcnVsZXM6CiAgICAgICAgICAgIHBhdHRlcm4gPSBydWxlLmdldCgicGF0dGVybiIsICIiKQogICAgICAgICAgICBpZiBwYXR0ZXJuIGFuZCAoZm5tYXRjaC5mbm1hdGNoKGZpbGVfbmFtZSwgcGF0dGVybikgb3IgZm5tYXRjaC5mbm1hdGNoKGZpbGVfcGF0aCwgcGF0dGVybikpOgogICAgICAgICAgICAgICAgcmV0dXJuIHJ1bGUuZ2V0KCJncm91cCIpCgogICAgICAgIHJldHVybiBOb25lCgogICAgZGVmIF9wbGFjZV90YWIoc2VsZiwgdmlldyk6CiAgICAgICAgIiIiUGxhY2UgYSB0YWIgaW4gaXRzIHRhcmdldCBncm91cC4iIiIKICAgICAgICBpZiBzZWxmLl9pc19wbGFjaW5nOgogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgd2luZG93ID0gdmlldy53aW5kb3coKQogICAgICAgIGlmIG5vdCB3aW5kb3c6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICB0YXJnZXRfZ3JvdXAgPSBzZWxmLl9kZXRlcm1pbmVfdGFyZ2V0X2dyb3VwKHZpZXcpCiAgICAgICAgaWYgdGFyZ2V0X2dyb3VwIGlzIE5vbmU6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICBjdXJyZW50X2dyb3VwLCBjdXJyZW50X2luZGV4ID0gd2luZG93LmdldF92aWV3X2luZGV4KHZpZXcpCiAgICAgICAgCiAgICAgICAgIyBFbnN1cmUgdGFyZ2V0IGdyb3VwIGV4aXN0cywgY3JlYXRlIGlmIG5lZWRlZAogICAgICAgIGlmIHRhcmdldF9ncm91cCA+PSB3aW5kb3cubnVtX2dyb3VwcygpOgogICAgICAgICAgICBpZiBzZWxmLl9nZXRfc2V0dGluZygiYXV0b19hZGp1c3RfbGF5b3V0IiwgRmFsc2UsIHdpbmRvdyk6CiAgICAgICAgICAgICAgICBzZWxmLl9jcmVhdGVfbGF5b3V0X2Zvcl9ncm91cHMod2luZG93LCB0YXJnZXRfZ3JvdXAgKyAxKQogICAgICAgICAgICBlbHNlOgogICAgICAgICAgICAgICAgIyBUYXJnZXQgZ3JvdXAgZG9lc24ndCBleGlzdCBhbmQgYXV0by1sYXlvdXQgaXMgZGlzYWJsZWQKICAgICAgICAgICAgICAgIGZhbGxiYWNrX2JlaGF2aW9yID0gc2VsZi5fZ2V0X3NldHRpbmcoIm1pc3NpbmdfZ3JvdXBfYmVoYXZpb3IiLCAic2tpcCIsIHdpbmRvdykKCiAgICAgICAgICAgICAgICBpZiBmYWxsYmFja19iZWhhdmlvciA9PSAic2tpcCI6CiAgICAgICAgICAgICAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJUYXJnZXQgZ3JvdXAge3RhcmdldF9ncm91cH0gZG9lc24ndCBleGlzdCAob25seSB7d2luZG93Lm51bV9ncm91cHMoKX0gZ3JvdXBzKSwgc2tpcHBpbmciKQogICAgICAgICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICAgICAgZWxpZiBmYWxsYmFja19iZWhhdmlvciA9PSAibGFzdF9ncm91cCI6CiAgICAgICAgICAgICAgICAgICAgdGFyZ2V0X2dyb3VwID0gd2luZG93Lm51bV9ncm91cHMoKSAtIDEKICAgICAgICAgICAgICAgICAgICBzZWxmLl9kZWJ1Z19wcmludChmIlRhcmdldCBncm91cCBkb2Vzbid0IGV4aXN0LCB1c2luZyBsYXN0IGdyb3VwICh7dGFyZ2V0X2dyb3VwfSkiKQogICAgICAgICAgICAgICAgZWxpZiBmYWxsYmFja19iZWhhdmlvciA9PSAiZmlyc3RfZ3JvdXAiOgogICAgICAgICAgICAgICAgICAgIHRhcmdldF9ncm91cCA9IDAKICAgICAgICAgICAgICAgICAgICBzZWxmLl9kZWJ1Z19wcmludChmIlRhcmdldCBncm91cCBkb2Vzbid0IGV4aXN0LCB1c2luZyBmaXJzdCBncm91cCAoe3RhcmdldF9ncm91cH0pIikKICAgICAgICAgICAgICAgIGVsc2U6CiAgICAgICAgICAgICAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJUYXJnZXQgZ3JvdXAge3RhcmdldF9ncm91cH0gZG9lc24ndCBleGlzdCwgc2tpcHBpbmcgKHVua25vd24gZmFsbGJhY2sgYmVoYXZpb3IpIikKICAgICAgICAgICAgICAgICAgICByZXR1cm4KCiAgICAgICAgaWYgY3VycmVudF9ncm91cCA9PSB0YXJnZXRfZ3JvdXA6CiAgICAgICAgICAgIHJldHVybgoKICAgICAgICBzZWxmLl9pc19wbGFjaW5nID0gVHJ1ZQogICAgICAgIHRyeToKICAgICAgICAgICAgIyBEZXRlcm1pbmUgdGFyZ2V0IGluZGV4IHdpdGhpbiBncm91cAogICAgICAgICAgICB0YXJnZXRfaW5kZXggPSBzZWxmLl9nZXRfdGFyZ2V0X2luZGV4KHZpZXcsIHRhcmdldF9ncm91cCwgd2luZG93KQogICAgICAgICAgICAKICAgICAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJNb3ZpbmcgdGFiIGZyb20gZ3JvdXAge2N1cnJlbnRfZ3JvdXB9IHRvIGdyb3VwIHt0YXJnZXRfZ3JvdXB9LCBpbmRleCB7dGFyZ2V0X2luZGV4fSIpCiAgICAgICAgICAgIHdpbmRvdy5zZXRfdmlld19pbmRleCh2aWV3LCB0YXJnZXRfZ3JvdXAsIHRhcmdldF9pbmRleCkKICAgICAgICAgICAgCiAgICAgICAgICAgICMgVHJhY2sgdGhpcyBwbGFjZW1lbnQKICAgICAgICAgICAgc2VsZi5fbGFzdF9wbGFjZW1lbnRzW3dpbmRvdy5pZCgpXVt2aWV3LmlkKCldID0gKHRhcmdldF9ncm91cCwgdGFyZ2V0X2luZGV4LCB0aW1lLnRpbWUoKSkKICAgICAgICAgICAgCiAgICAgICAgZmluYWxseToKICAgICAgICAgICAgc2VsZi5faXNfcGxhY2luZyA9IEZhbHNlCgogICAgZGVmIF9nZXRfdGFyZ2V0X2luZGV4KHNlbGYsIHZpZXcsIHRhcmdldF9ncm91cCwgd2luZG93PU5vbmUpOgogICAgICAgICIiIkRldGVybWluZSB0aGUgdGFyZ2V0IGluZGV4IHdpdGhpbiBhIGdyb3VwLiIiIgogICAgICAgIGlmIG5vdCB3aW5kb3c6CiAgICAgICAgICAgIHdpbmRvdyA9IHZpZXcud2luZG93KCkKICAgICAgICB2aWV3c19pbl9ncm91cCA9IHdpbmRvdy52aWV3c19pbl9ncm91cCh0YXJnZXRfZ3JvdXApCgogICAgICAgIHNvcnRfbWV0aG9kID0gc2VsZi5fZ2V0X3NldHRpbmcoImdyb3VwX3NvcnRfbWV0aG9kIiwgImFwcGVuZCIsIHdpbmRvdykKCiAgICAgICAgaWYgc29ydF9tZXRob2QgPT0gInByZXBlbmQiOgogICAgICAgICAgICByZXR1cm4gMAogICAgICAgIGVsaWYgc29ydF9tZXRob2QgPT0gImFwcGVuZCI6CiAgICAgICAgICAgIHJldHVybiBsZW4odmlld3NfaW5fZ3JvdXApCiAgICAgICAgZWxpZiBzb3J0X21ldGhvZCA9PSAiYWxwaGFiZXRpY2FsIjoKICAgICAgICAgICAgcmV0dXJuIHNlbGYuX2dldF9hbHBoYWJldGljYWxfaW5kZXgodmlldywgdmlld3NfaW5fZ3JvdXApCiAgICAgICAgZWxzZToKICAgICAgICAgICAgcmV0dXJuIGxlbih2aWV3c19pbl9ncm91cCkKCiAgICBkZWYgX2dldF9hbHBoYWJldGljYWxfaW5kZXgoc2VsZiwgdmlldywgdmlld3NfaW5fZ3JvdXApOgogICAgICAgICIiIkdldCBpbmRleCBmb3IgYWxwaGFiZXRpY2FsIGluc2VydGlvbi4iIiIKICAgICAgICB2aWV3X25hbWUgPSBvcy5wYXRoLmJhc2VuYW1lKHZpZXcuZmlsZV9uYW1lKCkgb3Igdmlldy5uYW1lKCkgb3IgIiIpCiAgICAgICAgCiAgICAgICAgZm9yIGksIGV4aXN0aW5nX3ZpZXcgaW4gZW51bWVyYXRlKHZpZXdzX2luX2dyb3VwKToKICAgICAgICAgICAgZXhpc3RpbmdfbmFtZSA9IG9zLnBhdGguYmFzZW5hbWUoZXhpc3Rpbmdfdmlldy5maWxlX25hbWUoKSBvciBleGlzdGluZ192aWV3Lm5hbWUoKSBvciAiIikKICAgICAgICAgICAgaWYgdmlld19uYW1lLmxvd2VyKCkgPCBleGlzdGluZ19uYW1lLmxvd2VyKCk6CiAgICAgICAgICAgICAgICByZXR1cm4gaQogICAgICAgIAogICAgICAgIHJldHVybiBsZW4odmlld3NfaW5fZ3JvdXApCgogICAgZGVmIF9jcmVhdGVfbGF5b3V0X2Zvcl9ncm91cHMoc2VsZiwgd2luZG93LCBudW1fZ3JvdXBzKToKICAgICAgICAiIiJDcmVhdGUgYSBsYXlvdXQgd2l0aCB0aGUgc3BlY2lmaWVkIG51bWJlciBvZiBncm91cHMuIiIiCiAgICAgICAgY3VycmVudF9ncm91cHMgPSB3aW5kb3cubnVtX2dyb3VwcygpCiAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJOZWVkIHtudW1fZ3JvdXBzfSBncm91cHMsIGN1cnJlbnQ6IHtjdXJyZW50X2dyb3Vwc30iKQoKICAgICAgICBpZiBudW1fZ3JvdXBzIDw9IGN1cnJlbnRfZ3JvdXBzOgogICAgICAgICAgICBzZWxmLl9kZWJ1Z19wcmludChmIkFscmVhZHkgaGF2ZSBlbm91Z2ggZ3JvdXBzIikKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgICMgQ2hlY2sgaWYgd2UgaGF2ZSBhIGN1c3RvbSBsYXlvdXQgZm9yIHRoaXMgZ3JvdXAgY291bnQKICAgICAgICBsYXlvdXRfY29uZmlncyA9IHNlbGYuX2dldF9zZXR0aW5nKCJsYXlvdXRfY29uZmlncyIsIHt9LCB3aW5kb3cpCiAgICAgICAgY3VzdG9tX2xheW91dCA9IGxheW91dF9jb25maWdzLmdldChzdHIobnVtX2dyb3VwcykpCgogICAgICAgIGlmIGN1c3RvbV9sYXlvdXQ6CiAgICAgICAgICAgIHNlbGYuX2RlYnVnX3ByaW50KGYiVXNpbmcgY3VzdG9tIGxheW91dCBmb3Ige251bV9ncm91cHN9IGdyb3VwcyIpCiAgICAgICAgICAgIHNlbGYuX2FwcGx5X2xheW91dCh3aW5kb3csIGN1c3RvbV9sYXlvdXQpCiAgICAgICAgICAgIHJldHVybgoKICAgICAgICAjIEdlbmVyYXRlIGxheW91dCBiYXNlZCBvbiBsYXlvdXQgdHlwZQogICAgICAgIGxheW91dF90eXBlID0gc2VsZi5fZ2V0X3NldHRpbmcoImxheW91dF90eXBlIiwgImNvbHVtbnMiLCB3aW5kb3cpCgogICAgICAgIGlmIGxheW91dF90eXBlID09ICJjb2x1bW5zIjoKICAgICAgICAgICAgbGF5b3V0ID0gc2VsZi5fY3JlYXRlX2NvbHVtbnNfbGF5b3V0KG51bV9ncm91cHMpCiAgICAgICAgZWxpZiBsYXlvdXRfdHlwZSA9PSAicm93cyI6CiAgICAgICAgICAgIGxheW91dCA9IHNlbGYuX2NyZWF0ZV9yb3dzX2xheW91dChudW1fZ3JvdXBzKQogICAgICAgIGVsaWYgbGF5b3V0X3R5cGUgPT0gImdyaWQiOgogICAgICAgICAgICBsYXlvdXQgPSBzZWxmLl9jcmVhdGVfZ3JpZF9sYXlvdXQobnVtX2dyb3VwcykKICAgICAgICBlbHNlOgogICAgICAgICAgICAjIERlZmF1bHQgdG8gY29sdW1ucwogICAgICAgICAgICBsYXlvdXQgPSBzZWxmLl9jcmVhdGVfY29sdW1uc19sYXlvdXQobnVtX2dyb3VwcykKCiAgICAgICAgc2VsZi5fZGVidWdfcHJpbnQoZiJDcmVhdGluZyB7bGF5b3V0X3R5cGV9IGxheW91dCBmb3Ige251bV9ncm91cHN9IGdyb3VwcyIpCiAgICAgICAgc2VsZi5fYXBwbHlfbGF5b3V0KHdpbmRvdywgbGF5b3V0KQoKICAgIGRlZiBfY3JlYXRlX2NvbHVtbnNfbGF5b3V0KHNlbGYsIG51bV9ncm91cHMpOgogICAgICAgICIiIkNyZWF0ZSBhIHNpbXBsZSBjb2x1bW5zIGxheW91dC4iIiIKICAgICAgICBjb2xzID0gW2kgLyBudW1fZ3JvdXBzIGZvciBpIGluIHJhbmdlKG51bV9ncm91cHMgKyAxKV0KICAgICAgICByb3dzID0gWzAuMCwgMS4wXQogICAgICAgIGNlbGxzID0gW1tpLCAwLCBpICsgMSwgMV0gZm9yIGkgaW4gcmFuZ2UobnVtX2dyb3VwcyldCiAgICAgICAgcmV0dXJuIHsiY29scyI6IGNvbHMsICJyb3dzIjogcm93cywgImNlbGxzIjogY2VsbHN9CgogICAgZGVmIF9jcmVhdGVfcm93c19sYXlvdXQoc2VsZiwgbnVtX2dyb3Vwcyk6CiAgICAgICAgIiIiQ3JlYXRlIGEgc2ltcGxlIHJvd3MgbGF5b3V0LiIiIgogICAgICAgIGNvbHMgPSBbMC4wLCAxLjBdCiAgICAgICAgcm93cyA9IFtpIC8gbnVtX2dyb3VwcyBmb3IgaSBpbiByYW5nZShudW1fZ3JvdXBzICsgMSldCiAgICAgICAgY2VsbHMgPSBbWzAsIGksIDEsIGkgKyAxXSBmb3IgaSBpbiByYW5nZShudW1fZ3JvdXBzKV0KICAgICAgICByZXR1cm4geyJjb2xzIjogY29scywgInJvd3MiOiByb3dzLCAiY2VsbHMiOiBjZWxsc30KCiAgICBkZWYgX2NyZWF0ZV9ncmlkX2xheW91dChzZWxmLCBudW1fZ3JvdXBzKToKICAgICAgICAiIiJDcmVhdGUgYSBncmlkIGxheW91dC4iIiIKICAgICAgICBpbXBvcnQgbWF0aAogICAgICAgIG51bV9jb2x1bW5zID0gaW50KG1hdGguc3FydChudW1fZ3JvdXBzKSkKICAgICAgICBudW1fcm93cyA9IChudW1fZ3JvdXBzICsgbnVtX2NvbHVtbnMgLSAxKSAvLyBudW1fY29sdW1ucwoKICAgICAgICBpZiBudW1fY29sdW1ucyA9PSAwOgogICAgICAgICAgICBudW1fY29sdW1ucyA9IDEKCiAgICAgICAgY29scyA9IFtpIC8gbnVtX2NvbHVtbnMgZm9yIGkgaW4gcmFuZ2UobnVtX2NvbHVtbnMgKyAxKV0KICAgICAgICByb3dzID0gW2kgLyBudW1fcm93cyBmb3IgaSBpbiByYW5nZShudW1fcm93cyArIDEpXQoKICAgICAgICBjZWxscyA9IFtdCiAgICAgICAgZ3JvdXAgPSAwCiAgICAgICAgZm9yIHJvdyBpbiByYW5nZShudW1fcm93cyk6CiAgICAgICAgICAgIGZvciBjb2wgaW4gcmFuZ2UobnVtX2NvbHVtbnMpOgogICAgICAgICAgICAgICAgaWYgZ3JvdXAgPCBudW1fZ3JvdXBzOgogICAgICAgICAgICAgICAgICAgIGNlbGxzLmFwcGVuZChbY29sLCByb3csIGNvbCArIDEsIHJvdyArIDFdKQogICAgICAgICAgICAgICAgICAgIGdyb3VwICs9IDEKCiAgICAgICAgcmV0dXJuIHsiY29scyI6IGNvbHMsICJyb3dzIjogcm93cywgImNlbGxzIjogY2VsbHN9CgogICAgZGVmIF9hcHBseV9sYXlvdXQoc2VsZiwgd2luZG93LCBsYXlvdXQpOgogICAgICAgICIiIkFwcGx5IGEgbGF5b3V0IHRvIHRoZSB3aW5kb3cuIiIiCiAgICAgICAgdHJ5OgogICAgICAgICAgICB3aW5kb3cuc2V0X2xheW91dChsYXlvdXQpCiAgICAgICAgICAgIHNlbGYuX2RlYnVnX3ByaW50KGYiQXBwbGllZCBsYXlvdXQ6IHtsYXlvdXR9IikKICAgICAgICBleGNlcHQgRXhjZXB0aW9uIGFzIGU6CiAgICAgICAgICAgIHNlbGYuX2RlYnVnX3ByaW50KGYiRmFpbGVkIHRvIGFwcGx5IGxheW91dDoge2V9IikKCgoKCgpjbGFzcyBKb3JuQXV0b1BsYWNlVGFic01hbnVhbENvbW1hbmQoc3VibGltZV9wbHVnaW4uV2luZG93Q29tbWFuZCk6CiAgICAiIiJNYW51YWwgY29tbWFuZCB0byBwbGFjZSBjdXJyZW50IHRhYiBhY2NvcmRpbmcgdG8gcnVsZXMuIiIiCiAgICAKICAgIGRlZiBydW4oc2VsZik6CiAgICAgICAgdmlldyA9IHNlbGYud2luZG93LmFjdGl2ZV92aWV3KCkKICAgICAgICBpZiBub3QgdmlldzoKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgCiAgICAgICAgcGx1Z2luID0gSm9ybl9BdXRvUGxhY2VUYWJzQ29tbWFuZC5pbnN0YW5jZSgpCiAgICAgICAgaWYgcGx1Z2luOgogICAgICAgICAgICBwbHVnaW4uX3BsYWNlX3RhYih2aWV3KQogICAgICAgICAgICBzdWJsaW1lLnN0YXR1c19tZXNzYWdlKCJUYWIgcGxhY2VkIGFjY29yZGluZyB0byBydWxlcyIpCgoKY2xhc3MgSm9ybkF1dG9QbGFjZVRhYnNQbGFjZUFsbENvbW1hbmQoc3VibGltZV9wbHVnaW4uV2luZG93Q29tbWFuZCk6CiAgICAiIiJDb21tYW5kIHRvIHBsYWNlIGFsbCB0YWJzIGFjY29yZGluZyB0byBydWxlcy4iIiIKICAgIAogICAgZGVmIHJ1bihzZWxmKToKICAgICAgICBwbHVnaW4gPSBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kLmluc3RhbmNlKCkKICAgICAgICBpZiBub3QgcGx1Z2luOgogICAgICAgICAgICByZXR1cm4KICAgICAgICAKICAgICAgICBwbGFjZWRfY291bnQgPSAwCiAgICAgICAgZm9yIHZpZXcgaW4gc2VsZi53aW5kb3cudmlld3MoKToKICAgICAgICAgICAgaWYgcGx1Z2luLl9zaG91bGRfYXV0b19wbGFjZSh2aWV3KToKICAgICAgICAgICAgICAgIHBsdWdpbi5fcGxhY2VfdGFiKHZpZXcpCiAgICAgICAgICAgICAgICBwbGFjZWRfY291bnQgKz0gMQogICAgICAgIAogICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoZiJQbGFjZWQge3BsYWNlZF9jb3VudH0gdGFicyBhY2NvcmRpbmcgdG8gcnVsZXMiKQoKCmNsYXNzIEpvcm5BdXRvUGxhY2VUYWJzVG9nZ2xlQ29tbWFuZChzdWJsaW1lX3BsdWdpbi5XaW5kb3dDb21tYW5kKToKICAgICIiIlRvZ2dsZSBhdXRvLXBsYWNlbWVudCBvbi9vZmYuIiIiCgogICAgZGVmIHJ1bihzZWxmKToKICAgICAgICBwbHVnaW4gPSBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kLmluc3RhbmNlKCkKICAgICAgICBpZiBub3QgcGx1Z2luOgogICAgICAgICAgICByZXR1cm4KCiAgICAgICAgY3VycmVudCA9IHBsdWdpbi5zZXR0aW5ncy5nZXQoImF1dG9fcGxhY2Vfb25fYWN0aXZhdGlvbiIsIFRydWUpCiAgICAgICAgcGx1Z2luLnNldHRpbmdzLnNldCgiYXV0b19wbGFjZV9vbl9hY3RpdmF0aW9uIiwgbm90IGN1cnJlbnQpCiAgICAgICAgc3VibGltZS5zYXZlX3NldHRpbmdzKGYie1BMVUdJTl9OQU1FfS5zdWJsaW1lLXNldHRpbmdzIikKCiAgICAgICAgc3RhdHVzID0gImVuYWJsZWQiIGlmIG5vdCBjdXJyZW50IGVsc2UgImRpc2FibGVkIgogICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoZiJBdXRvLXBsYWNlbWVudCB7c3RhdHVzfSIpCgoKY2xhc3MgSm9ybkF1dG9QbGFjZVRhYnNSZWxvYWRTZXR0aW5nc0NvbW1hbmQoc3VibGltZV9wbHVnaW4uV2luZG93Q29tbWFuZCk6CiAgICAiIiJSZWxvYWQgcGx1Z2luIHNldHRpbmdzIGFuZCBjbGVhciBwcm9qZWN0IHNldHRpbmdzIGNhY2hlLiIiIgoKICAgIGRlZiBydW4oc2VsZik6CiAgICAgICAgcGx1Z2luID0gSm9ybl9BdXRvUGxhY2VUYWJzQ29tbWFuZC5pbnN0YW5jZSgpCiAgICAgICAgaWYgbm90IHBsdWdpbjoKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIHBsdWdpbi5zZXR0aW5ncyA9IHN1YmxpbWUubG9hZF9zZXR0aW5ncyhmIntQTFVHSU5fTkFNRX0uc3VibGltZS1zZXR0aW5ncyIpCiAgICAgICAgcGx1Z2luLl9jbGVhcl9zZXR0aW5nc19jYWNoZSgpICAjIENsZWFyIGFsbCBjYWNoZWQgcHJvamVjdCBzZXR0aW5ncwogICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoIkF1dG9QbGFjZSBzZXR0aW5ncyByZWxvYWRlZCIpCgoKY2xhc3MgSm9ybkF1dG9QbGFjZVRhYnNTaG93UnVsZXNDb21tYW5kKHN1YmxpbWVfcGx1Z2luLldpbmRvd0NvbW1hbmQpOgogICAgIiIiU2hvdyBjdXJyZW50IHBsYWNlbWVudCBydWxlcyBpbiBhIG5ldyB2aWV3LiIiIgoKICAgIGRlZiBydW4oc2VsZik6CiAgICAgICAgcGx1Z2luID0gSm9ybl9BdXRvUGxhY2VUYWJzQ29tbWFuZC5pbnN0YW5jZSgpCiAgICAgICAgaWYgbm90IHBsdWdpbjoKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIHZpZXcgPSBzZWxmLndpbmRvdy5uZXdfZmlsZSgpCiAgICAgICAgdmlldy5zZXRfbmFtZSgiQXV0b1BsYWNlIFJ1bGVzIikKICAgICAgICB2aWV3LnNldF9zY3JhdGNoKFRydWUpCgogICAgICAgIHJ1bGVzX3RleHQgPSBzZWxmLl9mb3JtYXRfcnVsZXMocGx1Z2luLnNldHRpbmdzKQogICAgICAgIHZpZXcucnVuX2NvbW1hbmQoImFwcGVuZCIsIHsiY2hhcmFjdGVycyI6IHJ1bGVzX3RleHR9KQogICAgICAgIHZpZXcuc2V0X3JlYWRfb25seShUcnVlKQoKICAgIGRlZiBfZm9ybWF0X3J1bGVzKHNlbGYsIHNldHRpbmdzKToKICAgICAgICAiIiJGb3JtYXQgY3VycmVudCBydWxlcyBmb3IgZGlzcGxheS4iIiIKICAgICAgICBwbHVnaW4gPSBKb3JuX0F1dG9QbGFjZVRhYnNDb21tYW5kLmluc3RhbmNlKCkKICAgICAgICBpZiBub3QgcGx1Z2luOgogICAgICAgICAgICByZXR1cm4gIlBsdWdpbiBub3QgYXZhaWxhYmxlIgoKICAgICAgICAjIEdldCBlZmZlY3RpdmUgc2V0dGluZ3MgZm9yIHRoaXMgd2luZG93CiAgICAgICAgZWZmZWN0aXZlX3NldHRpbmdzID0gcGx1Z2luLl9nZXRfZWZmZWN0aXZlX3NldHRpbmdzKHNlbGYud2luZG93KQoKICAgICAgICBsaW5lcyA9IFsiIyBKb3JuIEF1dG9QbGFjZSBUYWJzIC0gQ3VycmVudCBSdWxlc1xuXG4iXQoKICAgICAgICAjIENoZWNrIGlmIHByb2plY3Qtc3BlY2lmaWMgc2V0dGluZ3MgYXJlIGFjdGl2ZQogICAgICAgIHByb2plY3RfZGF0YSA9IHNlbGYud2luZG93LnByb2plY3RfZGF0YSgpCiAgICAgICAgaGFzX3Byb2plY3Rfc2V0dGluZ3MgPSAocHJvamVjdF9kYXRhIGFuZCAic2V0dGluZ3MiIGluIHByb2plY3RfZGF0YSBhbmQKICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICJqb3JuX2F1dG9fcGxhY2VfdGFicyIgaW4gcHJvamVjdF9kYXRhWyJzZXR0aW5ncyJdKQoKICAgICAgICBpZiBoYXNfcHJvamVjdF9zZXR0aW5nczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBQcm9qZWN0LVNwZWNpZmljIFNldHRpbmdzIEFjdGl2ZVxuIikKICAgICAgICAgICAgcHJvamVjdF9zZXR0aW5ncyA9IHByb2plY3RfZGF0YVsic2V0dGluZ3MiXVsiam9ybl9hdXRvX3BsYWNlX3RhYnMiXQogICAgICAgICAgICBsaW5lcy5hcHBlbmQoZiJQcm9qZWN0IG92ZXJyaWRlczogeycsICcuam9pbihwcm9qZWN0X3NldHRpbmdzLmtleXMoKSl9XG5cbiIpCiAgICAgICAgZWxzZToKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBVc2luZyBHbG9iYWwgU2V0dGluZ3MgT25seVxuXG4iKQoKICAgICAgICAjIEF1dG8tcGxhY2VtZW50IHN0YXR1cwogICAgICAgIGF1dG9fb25fYWN0aXZhdGlvbiA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImF1dG9fcGxhY2Vfb25fYWN0aXZhdGlvbiIsIFRydWUpCiAgICAgICAgYXV0b19vbl9sb2FkID0gZWZmZWN0aXZlX3NldHRpbmdzLmdldCgiYXV0b19wbGFjZV9vbl9sb2FkIiwgVHJ1ZSkKICAgICAgICBsaW5lcy5hcHBlbmQoZiJBdXRvLXBsYWNlbWVudCBvbiBhY3RpdmF0aW9uOiB7YXV0b19vbl9hY3RpdmF0aW9ufVxuIikKICAgICAgICBsaW5lcy5hcHBlbmQoZiJBdXRvLXBsYWNlbWVudCBvbiBsb2FkOiB7YXV0b19vbl9sb2FkfVxuXG4iKQoKICAgICAgICAjIEZpbGUgdHlwZSBydWxlcwogICAgICAgIGZpbGVfdHlwZV9ydWxlcyA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImZpbGVfdHlwZV9ydWxlcyIsIHt9KQogICAgICAgIGlmIGZpbGVfdHlwZV9ydWxlczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBGaWxlIFR5cGUgUnVsZXNcbiIpCiAgICAgICAgICAgIGZvciBncm91cCwgcGF0dGVybnMgaW4gZmlsZV90eXBlX3J1bGVzLml0ZW1zKCk6CiAgICAgICAgICAgICAgICBsaW5lcy5hcHBlbmQoZiJHcm91cCB7Z3JvdXB9OiB7JywgJy5qb2luKHBhdHRlcm5zKX1cbiIpCiAgICAgICAgICAgIGxpbmVzLmFwcGVuZCgiXG4iKQoKICAgICAgICAjIERpcmVjdG9yeSBydWxlcwogICAgICAgIGRpcl9ydWxlcyA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImRpcmVjdG9yeV9ydWxlcyIsIHt9KQogICAgICAgIGlmIGRpcl9ydWxlczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBEaXJlY3RvcnkgUnVsZXNcbiIpCiAgICAgICAgICAgIGZvciBncm91cCwgcGF0dGVybnMgaW4gZGlyX3J1bGVzLml0ZW1zKCk6CiAgICAgICAgICAgICAgICBsaW5lcy5hcHBlbmQoZiJHcm91cCB7Z3JvdXB9OiB7JywgJy5qb2luKHBhdHRlcm5zKX1cbiIpCiAgICAgICAgICAgIGxpbmVzLmFwcGVuZCgiXG4iKQoKICAgICAgICAjIEN1c3RvbSBydWxlcwogICAgICAgIGN1c3RvbV9ydWxlcyA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImN1c3RvbV9ydWxlcyIsIFtdKQogICAgICAgIGlmIGN1c3RvbV9ydWxlczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCIjIyBDdXN0b20gUnVsZXNcbiIpCiAgICAgICAgICAgIGZvciBydWxlIGluIHNvcnRlZChjdXN0b21fcnVsZXMsIGtleT1sYW1iZGEgcjogci5nZXQoInByaW9yaXR5IiwgMCksIHJldmVyc2U9VHJ1ZSk6CiAgICAgICAgICAgICAgICBuYW1lID0gcnVsZS5nZXQoIm5hbWUiLCAiVW5uYW1lZCIpCiAgICAgICAgICAgICAgICBwYXR0ZXJuID0gcnVsZS5nZXQoInBhdHRlcm4iLCAiIikKICAgICAgICAgICAgICAgIGdyb3VwID0gcnVsZS5nZXQoImdyb3VwIiwgIj8iKQogICAgICAgICAgICAgICAgcHJpb3JpdHkgPSBydWxlLmdldCgicHJpb3JpdHkiLCAwKQogICAgICAgICAgICAgICAgbGluZXMuYXBwZW5kKGYie25hbWV9OiB7cGF0dGVybn0g4oaSIEdyb3VwIHtncm91cH0gKFByaW9yaXR5OiB7cHJpb3JpdHl9KVxuIikKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCJcbiIpCgogICAgICAgICMgU3BlY2lhbCBncm91cHMKICAgICAgICBwcm9qZWN0X2dyb3VwID0gZWZmZWN0aXZlX3NldHRpbmdzLmdldCgicHJvamVjdF9maWxlc19ncm91cCIpCiAgICAgICAgZXh0ZXJuYWxfZ3JvdXAgPSBlZmZlY3RpdmVfc2V0dGluZ3MuZ2V0KCJleHRlcm5hbF9maWxlc19ncm91cCIpCiAgICAgICAgdW5zYXZlZF9ncm91cCA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoInVuc2F2ZWRfZmlsZXNfZ3JvdXAiKQoKICAgICAgICBsaW5lcy5hcHBlbmQoIiMjIFNwZWNpYWwgR3JvdXBzXG4iKQogICAgICAgIGlmIHByb2plY3RfZ3JvdXAgaXMgbm90IE5vbmU6CiAgICAgICAgICAgIGxpbmVzLmFwcGVuZChmIlByb2plY3QgZmlsZXM6IEdyb3VwIHtwcm9qZWN0X2dyb3VwfVxuIikKICAgICAgICBpZiBleHRlcm5hbF9ncm91cCBpcyBub3QgTm9uZToKICAgICAgICAgICAgbGluZXMuYXBwZW5kKGYiRXh0ZXJuYWwgZmlsZXM6IEdyb3VwIHtleHRlcm5hbF9ncm91cH1cbiIpCiAgICAgICAgaWYgdW5zYXZlZF9ncm91cCBpcyBub3QgTm9uZToKICAgICAgICAgICAgbGluZXMuYXBwZW5kKGYiVW5zYXZlZCBmaWxlczogR3JvdXAge3Vuc2F2ZWRfZ3JvdXB9XG4iKQoKICAgICAgICAjIExheW91dCBzZXR0aW5ncwogICAgICAgIGF1dG9fYWRqdXN0ID0gZWZmZWN0aXZlX3NldHRpbmdzLmdldCgiYXV0b19hZGp1c3RfbGF5b3V0IiwgRmFsc2UpCiAgICAgICAgbWlzc2luZ19iZWhhdmlvciA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoIm1pc3NpbmdfZ3JvdXBfYmVoYXZpb3IiLCAic2tpcCIpCiAgICAgICAgbGF5b3V0X3R5cGUgPSBlZmZlY3RpdmVfc2V0dGluZ3MuZ2V0KCJsYXlvdXRfdHlwZSIsICJjb2x1bW5zIikKICAgICAgICBzb3J0X21ldGhvZCA9IGVmZmVjdGl2ZV9zZXR0aW5ncy5nZXQoImdyb3VwX3NvcnRfbWV0aG9kIiwgImFwcGVuZCIpCgogICAgICAgIGxpbmVzLmFwcGVuZCgiXG4jIyBMYXlvdXQgU2V0dGluZ3NcbiIpCiAgICAgICAgbGluZXMuYXBwZW5kKGYiQXV0by1hZGp1c3QgbGF5b3V0OiB7YXV0b19hZGp1c3R9XG4iKQogICAgICAgIGxpbmVzLmFwcGVuZChmIk1pc3NpbmcgZ3JvdXAgYmVoYXZpb3I6IHttaXNzaW5nX2JlaGF2aW9yfVxuIikKICAgICAgICBsaW5lcy5hcHBlbmQoZiJMYXlvdXQgdHlwZToge2xheW91dF90eXBlfVxuIikKICAgICAgICBsaW5lcy5hcHBlbmQoZiJTb3J0IG1ldGhvZDoge3NvcnRfbWV0aG9kfVxuIikKCiAgICAgICAgIyBDdXN0b20gbGF5b3V0cwogICAgICAgIGxheW91dF9jb25maWdzID0gZWZmZWN0aXZlX3NldHRpbmdzLmdldCgibGF5b3V0X2NvbmZpZ3MiLCB7fSkKICAgICAgICBpZiBsYXlvdXRfY29uZmlnczoKICAgICAgICAgICAgbGluZXMuYXBwZW5kKCJcbiMjIEN1c3RvbSBMYXlvdXRzXG4iKQogICAgICAgICAgICBmb3IgZ3JvdXBfY291bnQsIGxheW91dCBpbiBsYXlvdXRfY29uZmlncy5pdGVtcygpOgogICAgICAgICAgICAgICAgZ3JvdXBfY291bnRfYWN0dWFsID0gbGVuKGxheW91dC5nZXQoImNlbGxzIiwgW10pKQogICAgICAgICAgICAgICAgbGluZXMuYXBwZW5kKGYiR3JvdXBzIHtncm91cF9jb3VudH06IEN1c3RvbSBsYXlvdXQgd2l0aCB7Z3JvdXBfY291bnRfYWN0dWFsfSBncm91cHNcbiIpCgogICAgICAgIHJldHVybiAiIi5qb2luKGxpbmVzKQoKCmNsYXNzIEpvcm5BdXRvUGxhY2VUYWJzQ3JlYXRlUHJvamVjdFNldHRpbmdzQ29tbWFuZChzdWJsaW1lX3BsdWdpbi5XaW5kb3dDb21tYW5kKToKICAgICIiIkNyZWF0ZSBwcm9qZWN0LXNwZWNpZmljIHNldHRpbmdzIHRlbXBsYXRlLiIiIgoKICAgIGRlZiBydW4oc2VsZik6CiAgICAgICAgcHJvamVjdF9kYXRhID0gc2VsZi53aW5kb3cucHJvamVjdF9kYXRhKCkKICAgICAgICBpZiBub3QgcHJvamVjdF9kYXRhOgogICAgICAgICAgICBzdWJsaW1lLmVycm9yX21lc3NhZ2UoIk5vIHByb2plY3QgZmlsZSBpcyBjdXJyZW50bHkgb3Blbi4gUGxlYXNlIHNhdmUgeW91ciBwcm9qZWN0IGZpcnN0LiIpCiAgICAgICAgICAgIHJldHVybgoKICAgICAgICAjIENoZWNrIGlmIHByb2plY3Qgc2V0dGluZ3MgYWxyZWFkeSBleGlzdAogICAgICAgIGlmICJzZXR0aW5ncyIgbm90IGluIHByb2plY3RfZGF0YToKICAgICAgICAgICAgcHJvamVjdF9kYXRhWyJzZXR0aW5ncyJdID0ge30KCiAgICAgICAgaWYgImpvcm5fYXV0b19wbGFjZV90YWJzIiBpbiBwcm9qZWN0X2RhdGFbInNldHRpbmdzIl06CiAgICAgICAgICAgIGlmIG5vdCBzdWJsaW1lLm9rX2NhbmNlbF9kaWFsb2coCiAgICAgICAgICAgICAgICAiUHJvamVjdC1zcGVjaWZpYyBBdXRvUGxhY2Ugc2V0dGluZ3MgYWxyZWFkeSBleGlzdC4gT3ZlcndyaXRlPyIsCiAgICAgICAgICAgICAgICAiT3ZlcndyaXRlIgogICAgICAgICAgICApOgogICAgICAgICAgICAgICAgcmV0dXJuCgogICAgICAgICMgQ3JlYXRlIHRlbXBsYXRlIHNldHRpbmdzCiAgICAgICAgdGVtcGxhdGVfc2V0dGluZ3MgPSB7CiAgICAgICAgICAgICJhdXRvX3BsYWNlX29uX2FjdGl2YXRpb24iOiBUcnVlLAogICAgICAgICAgICAiZmlsZV90eXBlX3J1bGVzIjogewogICAgICAgICAgICAgICAgIjAiOiBbIi5weSIsICIucHl3Il0sCiAgICAgICAgICAgICAgICAiMSI6IFsiLmpzIiwgIi50cyIsICIuanN4IiwgIi50c3giXSwKICAgICAgICAgICAgICAgICIyIjogWyIuaHRtbCIsICIuY3NzIiwgIi5zY3NzIl0sCiAgICAgICAgICAgICAgICAiMyI6IFsiLm1kIiwgIi50eHQiLCAiLmpzb24iXQogICAgICAgICAgICB9LAogICAgICAgICAgICAiZGlyZWN0b3J5X3J1bGVzIjogewogICAgICAgICAgICAgICAgIjAiOiBbIiovc3JjLyoiLCAiKi9saWIvKiJdLAogICAgICAgICAgICAgICAgIjEiOiBbIiovdGVzdHMvKiIsICIqL3Rlc3QvKiJdLAogICAgICAgICAgICAgICAgIjIiOiBbIiovZG9jcy8qIl0sCiAgICAgICAgICAgICAgICAiMyI6IFsiKi9jb25maWcvKiJdCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgICJwcm9qZWN0X2ZpbGVzX2dyb3VwIjogMCwKICAgICAgICAgICAgImV4dGVybmFsX2ZpbGVzX2dyb3VwIjogMSwKICAgICAgICAgICAgImF1dG9fYWRqdXN0X2xheW91dCI6IEZhbHNlLAogICAgICAgICAgICAibWlzc2luZ19ncm91cF9iZWhhdmlvciI6ICJza2lwIiwKICAgICAgICAgICAgImxheW91dF90eXBlIjogImNvbHVtbnMiCiAgICAgICAgfQoKICAgICAgICAjIEFkZCB0byBwcm9qZWN0IGRhdGEKICAgICAgICBwcm9qZWN0X2RhdGFbInNldHRpbmdzIl1bImpvcm5fYXV0b19wbGFjZV90YWJzIl0gPSB0ZW1wbGF0ZV9zZXR0aW5ncwogICAgICAgIHNlbGYud2luZG93LnNldF9wcm9qZWN0X2RhdGEocHJvamVjdF9kYXRhKQoKICAgICAgICAjIENsZWFyIGNhY2hlIHRvIHBpY2sgdXAgbmV3IHNldHRpbmdzCiAgICAgICAgcGx1Z2luID0gSm9ybl9BdXRvUGxhY2VUYWJzQ29tbWFuZC5pbnN0YW5jZSgpCiAgICAgICAgaWYgcGx1Z2luOgogICAgICAgICAgICBwbHVnaW4uX2NsZWFyX3NldHRpbmdzX2NhY2hlKHNlbGYud2luZG93LmlkKCkpCgogICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoIlByb2plY3Qtc3BlY2lmaWMgQXV0b1BsYWNlIHNldHRpbmdzIGNyZWF0ZWQiKQoKICAgIGRlZiBpc19lbmFibGVkKHNlbGYpOgogICAgICAgIHJldHVybiBzZWxmLndpbmRvdy5wcm9qZWN0X2RhdGEoKSBpcyBub3QgTm9uZQoKCmNsYXNzIEpvcm5BdXRvUGxhY2VUYWJzVGVzdExheW91dENvbW1hbmQoc3VibGltZV9wbHVnaW4uV2luZG93Q29tbWFuZCk6CiAgICAiIiJUZXN0IGEgc3BlY2lmaWMgbGF5b3V0IGJ5IGFwcGx5aW5nIGl0IHRvIHRoZSBjdXJyZW50IHdpbmRvdy4iIiIKCiAgICBkZWYgcnVuKHNlbGYpOgogICAgICAgIHBsdWdpbiA9IEpvcm5fQXV0b1BsYWNlVGFic0NvbW1hbmQuaW5zdGFuY2UoKQogICAgICAgIGlmIG5vdCBwbHVnaW46CiAgICAgICAgICAgIHN1YmxpbWUuZXJyb3JfbWVzc2FnZSgiQXV0b1BsYWNlIHBsdWdpbiBub3QgYXZhaWxhYmxlIikKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgICMgR2V0IGF2YWlsYWJsZSBjdXN0b20gbGF5b3V0cwogICAgICAgIGxheW91dF9jb25maWdzID0gcGx1Z2luLl9nZXRfc2V0dGluZygibGF5b3V0X2NvbmZpZ3MiLCB7fSwgc2VsZi53aW5kb3cpCgogICAgICAgICMgQ3JlYXRlIGxpc3Qgb2YgbGF5b3V0IG9wdGlvbnMKICAgICAgICBsYXlvdXRfb3B0aW9ucyA9IFtdCgogICAgICAgICMgQWRkIGN1c3RvbSBsYXlvdXRzCiAgICAgICAgZm9yIGdyb3VwX2NvdW50LCBsYXlvdXQgaW4gbGF5b3V0X2NvbmZpZ3MuaXRlbXMoKToKICAgICAgICAgICAgYWN0dWFsX2dyb3VwcyA9IGxlbihsYXlvdXQuZ2V0KCJjZWxscyIsIFtdKSkKICAgICAgICAgICAgbGF5b3V0X29wdGlvbnMuYXBwZW5kKFtmIkN1c3RvbToge2dyb3VwX2NvdW50fSBncm91cHMiLCBmIih7YWN0dWFsX2dyb3Vwc30gZ3JvdXBzKSJdKQoKICAgICAgICAjIEFkZCBnZW5lcmF0ZWQgbGF5b3V0IG9wdGlvbnMKICAgICAgICBsYXlvdXRfb3B0aW9ucy5leHRlbmQoWwogICAgICAgICAgICBbIkdlbmVyYXRlZDogMiBDb2x1bW5zIiwgImNvbHVtbnMiXSwKICAgICAgICAgICAgWyJHZW5lcmF0ZWQ6IDMgQ29sdW1ucyIsICJjb2x1bW5zIl0sCiAgICAgICAgICAgIFsiR2VuZXJhdGVkOiA0IENvbHVtbnMiLCAiY29sdW1ucyJdLAogICAgICAgICAgICBbIkdlbmVyYXRlZDogOCBDb2x1bW5zIiwgImNvbHVtbnMiXSwKICAgICAgICAgICAgWyJHZW5lcmF0ZWQ6IDIgUm93cyIsICJyb3dzIl0sCiAgICAgICAgICAgIFsiR2VuZXJhdGVkOiAzIFJvd3MiLCAicm93cyJdLAogICAgICAgICAgICBbIkdlbmVyYXRlZDogNCBSb3dzIiwgInJvd3MiXSwKICAgICAgICAgICAgWyJHZW5lcmF0ZWQ6IDJ4MiBHcmlkIiwgImdyaWQiXSwKICAgICAgICAgICAgWyJHZW5lcmF0ZWQ6IDN4MyBHcmlkIiwgImdyaWQiXQogICAgICAgIF0pCgogICAgICAgIGRlZiBvbl9zZWxlY3QoaW5kZXgpOgogICAgICAgICAgICBpZiBpbmRleCA9PSAtMToKICAgICAgICAgICAgICAgIHJldHVybgoKICAgICAgICAgICAgc2VsZWN0ZWQgPSBsYXlvdXRfb3B0aW9uc1tpbmRleF0KICAgICAgICAgICAgbGF5b3V0X25hbWUgPSBzZWxlY3RlZFswXQoKICAgICAgICAgICAgaWYgbGF5b3V0X25hbWUuc3RhcnRzd2l0aCgiQ3VzdG9tOiIpOgogICAgICAgICAgICAgICAgIyBIYW5kbGUgY3VzdG9tIGxheW91dHMKICAgICAgICAgICAgICAgIGdyb3VwX2NvdW50ID0gbGF5b3V0X25hbWUuc3BsaXQoIjoiKVsxXS5zdHJpcCgpLnNwbGl0KClbMF0KICAgICAgICAgICAgICAgIGxheW91dF9jb25maWcgPSBsYXlvdXRfY29uZmlncy5nZXQoZ3JvdXBfY291bnQpCiAgICAgICAgICAgICAgICBpZiBsYXlvdXRfY29uZmlnOgogICAgICAgICAgICAgICAgICAgIHBsdWdpbi5fYXBwbHlfbGF5b3V0KHNlbGYud2luZG93LCBsYXlvdXRfY29uZmlnKQogICAgICAgICAgICAgICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoZiJBcHBsaWVkIGN1c3RvbSBsYXlvdXQgZm9yIHtncm91cF9jb3VudH0gZ3JvdXBzIikKICAgICAgICAgICAgICAgIGVsc2U6CiAgICAgICAgICAgICAgICAgICAgc3VibGltZS5lcnJvcl9tZXNzYWdlKGYiQ3VzdG9tIGxheW91dCBmb3Ige2dyb3VwX2NvdW50fSBncm91cHMgbm90IGZvdW5kIikKCiAgICAgICAgICAgIGVsaWYgbGF5b3V0X25hbWUuc3RhcnRzd2l0aCgiR2VuZXJhdGVkOiIpOgogICAgICAgICAgICAgICAgIyBIYW5kbGUgZ2VuZXJhdGVkIGxheW91dHMKICAgICAgICAgICAgICAgIHBhcnRzID0gbGF5b3V0X25hbWUuc3BsaXQoKQogICAgICAgICAgICAgICAgaWYgIkNvbHVtbnMiIGluIGxheW91dF9uYW1lOgogICAgICAgICAgICAgICAgICAgIG51bV9ncm91cHMgPSBpbnQocGFydHNbMV0pCiAgICAgICAgICAgICAgICAgICAgbGF5b3V0ID0gcGx1Z2luLl9jcmVhdGVfY29sdW1uc19sYXlvdXQobnVtX2dyb3VwcykKICAgICAgICAgICAgICAgICAgICBwbHVnaW4uX2FwcGx5X2xheW91dChzZWxmLndpbmRvdywgbGF5b3V0KQogICAgICAgICAgICAgICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoZiJBcHBsaWVkIHtudW1fZ3JvdXBzfSBjb2x1bW5zIGxheW91dCIpCiAgICAgICAgICAgICAgICBlbGlmICJSb3dzIiBpbiBsYXlvdXRfbmFtZToKICAgICAgICAgICAgICAgICAgICBudW1fZ3JvdXBzID0gaW50KHBhcnRzWzFdKQogICAgICAgICAgICAgICAgICAgIGxheW91dCA9IHBsdWdpbi5fY3JlYXRlX3Jvd3NfbGF5b3V0KG51bV9ncm91cHMpCiAgICAgICAgICAgICAgICAgICAgcGx1Z2luLl9hcHBseV9sYXlvdXQoc2VsZi53aW5kb3csIGxheW91dCkKICAgICAgICAgICAgICAgICAgICBzdWJsaW1lLnN0YXR1c19tZXNzYWdlKGYiQXBwbGllZCB7bnVtX2dyb3Vwc30gcm93cyBsYXlvdXQiKQogICAgICAgICAgICAgICAgZWxpZiAiR3JpZCIgaW4gbGF5b3V0X25hbWU6CiAgICAgICAgICAgICAgICAgICAgaWYgIjJ4MiIgaW4gbGF5b3V0X25hbWU6CiAgICAgICAgICAgICAgICAgICAgICAgIGxheW91dCA9IHBsdWdpbi5fY3JlYXRlX2dyaWRfbGF5b3V0KDQpCiAgICAgICAgICAgICAgICAgICAgICAgIHBsdWdpbi5fYXBwbHlfbGF5b3V0KHNlbGYud2luZG93LCBsYXlvdXQpCiAgICAgICAgICAgICAgICAgICAgICAgIHN1YmxpbWUuc3RhdHVzX21lc3NhZ2UoIkFwcGxpZWQgMngyIGdyaWQgbGF5b3V0IikKICAgICAgICAgICAgICAgICAgICBlbGlmICIzeDMiIGluIGxheW91dF9uYW1lOgogICAgICAgICAgICAgICAgICAgICAgICBsYXlvdXQgPSBwbHVnaW4uX2NyZWF0ZV9ncmlkX2xheW91dCg5KQogICAgICAgICAgICAgICAgICAgICAgICBwbHVnaW4uX2FwcGx5X2xheW91dChzZWxmLndpbmRvdywgbGF5b3V0KQogICAgICAgICAgICAgICAgICAgICAgICBzdWJsaW1lLnN0YXR1c19tZXNzYWdlKCJBcHBsaWVkIDN4MyBncmlkIGxheW91dCIpCgogICAgICAgIGlmIG5vdCBsYXlvdXRfb3B0aW9uczoKICAgICAgICAgICAgc3VibGltZS5lcnJvcl9tZXNzYWdlKCJObyBsYXlvdXRzIGF2YWlsYWJsZSB0byB0ZXN0IikKICAgICAgICAgICAgcmV0dXJuCgogICAgICAgIHNlbGYud2luZG93LnNob3dfcXVpY2tfcGFuZWwobGF5b3V0X29wdGlvbnMsIG9uX3NlbGVjdCkKAAAAAAAAAAAIgAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABJAAAAAAAAAEkAAAAAAAAAAAAAAAAA8L8"], [6, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABJAAAAAAAAAEkAAAAAAAAAAAAAAAAA8L8"]]}, {"file": "my_sublime.sublime-project", "redo_stack": [[156, 1, "paste", null, "AQAAADcDAAAAAAAANwMAAAAAAADRAAAACiAgICAvLyBEaXJlY3RvcnktYmFzZWQgcnVsZXM6IGFzc2lnbiBwYXRocyB0byBncm91cHMKICAgICJkaXJlY3RvcnlfcnVsZXMiOiB7CiAgICAgICAgIjAiOiBbIiovc3JjLyoiLCAiKi9saWIvKiJdLAogICAgICAgICIxIjogWyIqL3Rlc3RzLyoiLCAiKi90ZXN0LyoiXSwKICAgICAgICAiMiI6IFsiKi9kb2NzLyoiLCAiKi9kb2N1bWVudGF0aW9uLyoiXQogICAgfSw", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAIBAAAAAAAAAgEAAAAAAAAAAAAAAAA8L8"]], "settings": {"buffer_size": 823, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[3, 1, "insert", {"characters": ","}, "AQAAADIBAAAAAAAAMwEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAyAQAAAAAAADIBAAAAAAAAAAAAAAAA8L8"], [6, 1, "duplicate_line", null, "AQAAADMBAAAAAAAA8gEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAzAQAAAAAAAHQAAAAAAAAAAAAAAAAA8L8"], [15, 1, "left_delete", null, "AQAAAPEBAAAAAAAA8QEAAAAAAAABAAAALA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADyAQAAAAAAAPIBAAAAAAAAAAAAAAAA8L8"], [24, 1, "insert", {"characters": "¨test"}, "BgAAADkBAAAAAAAAOgEAAAAAAAAAAAAAOgEAAAAAAAA6AQAAAAAAAAgAAABzZXR0aW5nczoBAAAAAAAAOwEAAAAAAAAAAAAAOwEAAAAAAAA8AQAAAAAAAAAAAAA8AQAAAAAAAD0BAAAAAAAAAAAAAD0BAAAAAAAAPgEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA5AQAAAAAAAEEBAAAAAAAAAAAAAAAA8L8"], [27, 1, "insert", {"characters": "\n"}, "BAAAAEYBAAAAAAAARwEAAAAAAAAAAAAARwEAAAAAAABLAQAAAAAAAAAAAABLAQAAAAAAAE8BAAAAAAAAAAAAAE8BAAAAAAAATwEAAAAAAACiAAAACiAgICAgICAgInRhYl9zaXplIjogNCwKICAgICAgICAidHJhbnNsYXRlX3RhYnNfdG9fc3BhY2VzIjogdHJ1ZSwKICAgICAgICAidHJpbV90cmFpbGluZ193aGl0ZV9zcGFjZV9vbl9zYXZlIjogdHJ1ZSwKICAgICAgICAiZW5zdXJlX25ld2xpbmVfYXRfZW9mX29uX3NhdmUiOiB0cnVl", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAADoAQAAAAAAAEYBAAAAAAAAAAAAAAAA8L8"], [28, 1, "insert_snippet", {"contents": "\"$0\""}, "AQAAAE8BAAAAAAAAUQEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABPAQAAAAAAAE8BAAAAAAAAAAAAAAAA8L8"], [29, 1, "insert", {"characters": "ttt"}, "AwAAAFABAAAAAAAAUQEAAAAAAAAAAAAAUQEAAAAAAABSAQAAAAAAAAAAAABSAQAAAAAAAFMBAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABQAQAAAAAAAFABAAAAAAAAAAAAAAAA8L8"], [31, 1, "insert", {"characters": ":"}, "AQAAAFQBAAAAAAAAVQEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABUAQAAAAAAAFQBAAAAAAAAAAAAAAAA8L8"], [32, 1, "insert", {"characters": " "}, "AQAAAFUBAAAAAAAAVgEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABVAQAAAAAAAFUBAAAAAAAAAAAAAAAA8L8"], [33, 1, "insert_snippet", {"contents": "\"$0\""}, "AQAAAFYBAAAAAAAAWAEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABWAQAAAAAAAFYBAAAAAAAAAAAAAAAA8L8"], [35, 1, "insert", {"characters": "\""}, "AQAAAFgBAAAAAAAAWQEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABYAQAAAAAAAFgBAAAAAAAAAAAAAAAA8L8"], [36, 1, "left_delete", null, "AQAAAFgBAAAAAAAAWAEAAAAAAAABAAAAIg", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABZAQAAAAAAAFkBAAAAAAAAAAAAAAAA8L8"], [38, 1, "insert", {"characters": "bbb"}, "AwAAAFcBAAAAAAAAWAEAAAAAAAAAAAAAWAEAAAAAAABZAQAAAAAAAAAAAABZAQAAAAAAAFoBAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABXAQAAAAAAAFcBAAAAAAAAAAAAAAAA8L8"], [43, 1, "left_delete", null, "AQAAADkBAAAAAAAAOQEAAAAAAAACAAAAwqg", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA6AQAAAAAAADoBAAAAAAAAAAAAAACASEA"], [47, 1, "insert", {"characters": "myru"}, "BQAAADkBAAAAAAAAOgEAAAAAAAAAAAAAOgEAAAAAAAA6AQAAAAAAAAQAAAB0ZXN0OgEAAAAAAAA7AQAAAAAAAAAAAAA7AQAAAAAAADwBAAAAAAAAAAAAADwBAAAAAAAAPQEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA5AQAAAAAAAD0BAAAAAAAAAAAAAAAA8L8"], [48, 1, "insert", {"characters": "les"}, "AwAAAD0BAAAAAAAAPgEAAAAAAAAAAAAAPgEAAAAAAAA/AQAAAAAAAAAAAAA/AQAAAAAAAEABAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA9AQAAAAAAAD0BAAAAAAAAAAAAAAAA8L8"], [55, 1, "insert", {"characters": "."}, "AQAAADkBAAAAAAAAOgEAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA5AQAAAAAAADkBAAAAAAAAAAAAAAAA8L8"], [81, 1, "paste", null, "AgAAAHQAAAAAAAAANAMAAAAAAAAAAAAANAMAAAAAAAA0AwAAAAAAAPAAAAAKICAgICJzZXR0aW5ncyI6CiAgICB7CiAgICAgICAgInRhYl9zaXplIjogNCwKICAgICAgICAidHJhbnNsYXRlX3RhYnNfdG9fc3BhY2VzIjogdHJ1ZSwKICAgICAgICAidHJpbV90cmFpbGluZ193aGl0ZV9zcGFjZV9vbl9zYXZlIjogdHJ1ZSwKICAgICAgICAiZW5zdXJlX25ld2xpbmVfYXRfZW9mX29uX3NhdmUiOiB0cnVlCiAgICB9LAogICAgIi5teXJ1bGVzIjoKICAgIHsKICAgICAgICAidHR0IjogImJiYiIKICAgIH0", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABkAQAAAAAAAHQAAAAAAAAAAAAAAADAWkA"], [114, 1, "insert", {"characters": "7"}, "AgAAACMDAAAAAAAAJAMAAAAAAAAAAAAAJAMAAAAAAAAkAwAAAAAAAAEAAAA0", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAkAwAAAAAAACMDAAAAAAAAAAAAAAAA8L8"], [121, 1, "insert", {"characters": "7"}, "AgAAAJQBAAAAAAAAlQEAAAAAAAAAAAAAlQEAAAAAAACVAQAAAAAAAAEAAAAz", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACVAQAAAAAAAJQBAAAAAAAAAAAAAAAA8L8"]]}, {"file": "refs/my_sublime_packages/Jorn_SublimeTabOrganizer/Jorn_SublimeTabOrganizer.sublime-project", "settings": {"buffer_size": 441, "line_ending": "Windows"}}, {"contents": "", "settings": {"buffer_size": 0, "line_ending": "Windows"}}, {"contents": "", "redo_stack": [[1, 1, "insert", {"characters": "org"}, "AwAAAAIAAAAAAAAAAgAAAAAAAAABAAAAZwEAAAAAAAAAAQAAAAAAAAABAAAAcgAAAAAAAAAAAAAAAAAAAAABAAAAbw", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAADAAAAAAAAAAMAAAAAAAAAAAAAAAAA8L8"]], "settings": {"buffer_size": 0, "line_ending": "Windows"}}, {"contents": "", "settings": {"buffer_size": 0, "line_ending": "Windows"}, "undo_stack": [[1, 1, "paste", null, "AQAAAAAAAAAAAAAAQQYAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [4, 1, "insert", {"characters": "considering"}, "CwAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAQAAAAAAAAACAAAAAAAAAAAAAAACAAAAAAAAAAMAAAAAAAAAAAAAAAMAAAAAAAAABAAAAAAAAAAAAAAABAAAAAAAAAAFAAAAAAAAAAAAAAAFAAAAAAAAAAYAAAAAAAAAAAAAAAYAAAAAAAAABwAAAAAAAAAAAAAABwAAAAAAAAAIAAAAAAAAAAAAAAAIAAAAAAAAAAkAAAAAAAAAAAAAAAkAAAAAAAAACgAAAAAAAAAAAAAACgAAAAAAAAALAAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [5, 1, "insert", {"characters": " the"}, "BAAAAAsAAAAAAAAADAAAAAAAAAAAAAAADAAAAAAAAAANAAAAAAAAAAAAAAANAAAAAAAAAA4AAAAAAAAAAAAAAA4AAAAAAAAADwAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAALAAAAAAAAAAsAAAAAAAAAAAAAAAAA8L8"], [6, 1, "insert", {"characters": " layouts"}, "CAAAAA8AAAAAAAAAEAAAAAAAAAAAAAAAEAAAAAAAAAARAAAAAAAAAAAAAAARAAAAAAAAABIAAAAAAAAAAAAAABIAAAAAAAAAEwAAAAAAAAAAAAAAEwAAAAAAAAAUAAAAAAAAAAAAAAAUAAAAAAAAABUAAAAAAAAAAAAAABUAAAAAAAAAFgAAAAAAAAAAAAAAFgAAAAAAAAAXAAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAPAAAAAAAAAA8AAAAAAAAAAAAAAAAA8L8"], [7, 1, "insert", {"characters": " can"}, "BAAAABcAAAAAAAAAGAAAAAAAAAAAAAAAGAAAAAAAAAAZAAAAAAAAAAAAAAAZAAAAAAAAABoAAAAAAAAAAAAAABoAAAAAAAAAGwAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAXAAAAAAAAABcAAAAAAAAAAAAAAAAA8L8"], [8, 1, "insert", {"characters": " be"}, "AwAAABsAAAAAAAAAHAAAAAAAAAAAAAAAHAAAAAAAAAAdAAAAAAAAAAAAAAAdAAAAAAAAAB4AAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAbAAAAAAAAABsAAAAAAAAAAAAAAAAA8L8"], [9, 1, "insert", {"characters": " "}, "AQAAAB4AAAAAAAAAHwAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAeAAAAAAAAAB4AAAAAAAAAAAAAAAAA8L8"], [11, 1, "cut", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAGAGAAAAAAAAAAAAAAAA8L8"]]}, {"contents": "{\n    \"saved_layouts\": [\n        {\n            \"cells\": [[0, 0, 1, 1], [1, 0, 2, 2], [0, 1, 1, 2]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"counterclockwise\",\n            \"rows\": [0.0, 0.114307153577, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 2],\n                [1, 1, 2, 3],\n                [1, 0, 3, 1],\n                [2, 1, 3, 3],\n                [0, 2, 1, 3],\n            ],\n            \"cols\": [0.0, 0.469470218497, 0.75, 1.0],\n            \"nickname\": \"special_2_ringlandskap\",\n            \"rows\": [0.0, 0.364111498258, 0.621118012422, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 2, 1],\n                [2, 0, 3, 4],\n                [0, 1, 1, 3],\n                [0, 3, 2, 4],\n                [1, 1, 2, 2],\n                [1, 2, 2, 3],\n            ],\n            \"cols\": [0.0, 0.43973442288, 0.879468845761, 1.0],\n            \"nickname\": \"special_1_small\",\n            \"rows\": [0.0, 0.0533049040512, 0.5, 0.946695095949, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 1, 3],\n                [0, 3, 1, 4],\n                [1, 0, 3, 1],\n                [1, 3, 3, 4],\n                [3, 0, 4, 1],\n                [3, 1, 4, 3],\n                [3, 3, 4, 4],\n                [1, 1, 2, 3],\n                [2, 1, 3, 2],\n                [2, 2, 3, 3],\n            ],\n            \"cols\": [0.0, 0.0228519195612, 0.524246192105, 0.977148080439, 1.0],\n            \"nickname\": \"special_1_large\",\n            \"rows\": [0.0, 0.0378979282466, 0.476755937342, 0.836280949974, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 1, 3],\n                [0, 3, 1, 4],\n                [1, 0, 3, 1],\n                [1, 3, 3, 4],\n                [3, 0, 4, 1],\n                [3, 1, 4, 3],\n                [3, 3, 4, 4],\n                [1, 1, 2, 3],\n                [2, 1, 3, 2],\n                [2, 2, 3, 3],\n            ],\n            \"cols\": [0.0, 0.0233136462543, 0.524246192105, 0.829654958036, 1.0],\n            \"nickname\": \"special_1\",\n            \"rows\": [0.0, 0.077817079333, 0.469176351693, 0.962102071753, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [1, 0, 2, 1],\n                [0, 1, 1, 2],\n                [1, 1, 2, 2],\n                [0, 2, 1, 3],\n                [1, 2, 2, 3],\n                [0, 3, 1, 4],\n                [1, 3, 2, 4],\n            ],\n            \"cols\": [\n                0.0,\n                0.420298507463,\n                1.0,\n            ],\n            \"nickname\": \"multirows_1\",\n            \"rows\": [0.0, 0.0311100049776, 0.0622200099552, 0.0933300149328, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [1, 0, 2, 1],\n                [0, 1, 1, 2],\n                [1, 1, 2, 2],\n                [0, 2, 1, 3],\n                [1, 2, 2, 3],\n                [0, 3, 1, 4],\n                [1, 3, 2, 4],\n            ],\n            \"cols\": [0.0, 0.420298507463, 1.0],\n            \"nickname\": \"multirows_2\",\n            \"rows\": [0.0, 0.0533049040512, 0.106609808102, 0.946695095949, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"(3) Two Rows, Lower Row has Two Columns\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [0, 1, 2, 2], [1, 0, 2, 1]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"(3) Two Rows, Upper Row has Two Columns\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [[0, 1, 2, 2], [0, 2, 1, 3], [1, 2, 2, 3], [0, 0, 2, 1]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"(4) Three Rows, Lower Row has Two Columns\",\n            \"rows\": [0.0, 0.0550660792952, 0.5, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 2], [1, 0, 2, 1], [1, 1, 2, 3], [0, 2, 1, 3]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"(4) Two Columns with Two Misaligned Rows\",\n            \"rows\": [0.0, 0.451264087725, 0.482374092702, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [0, 1, 2, 2], [1, 0, 3, 1], [2, 1, 3, 2]],\n            \"cols\": [0.0, 0.5, 0.496764705882, 1.0],\n            \"nickname\": \"(4) Two Rows with Two Misaligned Columns\",\n            \"rows\": [0.0, 0.529367844699, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 2], [1, 1, 2, 2], [1, 0, 3, 1], [2, 1, 3, 2]],\n            \"cols\": [0.0, 0.5, 0.75, 1.0],\n            \"nickname\": \"(4) Two Columns, Right Column has 3 Cells\",\n            \"rows\": [0.0, 0.364111498258, 1.0],\n        },\n        {\n            \"cells\": [[1, 0, 3, 1], [0, 0, 1, 2], [1, 1, 2, 2], [2, 1, 3, 2]],\n            \"cols\": [0.0, 0.280078125, 0.6400390625, 1.0],\n            \"nickname\": \"(4) Custom #1\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 2, 1],\n                [2, 0, 3, 3],\n                [0, 1, 1, 2],\n                [0, 2, 2, 3],\n                [1, 1, 2, 2],\n            ],\n            \"cols\": [0.0, 0.398623853211, 0.742201834862, 1.0],\n            \"nickname\": \"(5) Custom #2\",\n            \"rows\": [0.0, 0.309167446211, 0.843545369504, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 3],\n                [1, 1, 2, 3],\n                [1, 0, 3, 1],\n                [2, 1, 3, 2],\n                [2, 2, 3, 3],\n            ],\n            \"cols\": [0.0, 0.5, 0.75, 1.0],\n            \"nickname\": \"(5) Fibonnachi\",\n            \"rows\": [0.0, 0.364111498258, 0.682055749129, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [1, 0, 2, 1],\n                [0, 1, 1, 2],\n                [1, 1, 2, 3],\n                [0, 2, 1, 3],\n            ],\n            \"cols\": [0.0, 0.300639349026, 1.0],\n            \"nickname\": \"(5) Hierarchical\",\n            \"rows\": [0.0, 0.0321833161689, 0.431171054909, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"(Grid x 4) Two Rows, Two Columns\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [1, 0, 2, 1]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"(Grid x 2) Two Columns\",\n            \"rows\": [0.0, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [0, 1, 1, 2]],\n            \"cols\": [0.0, 1.0],\n            \"nickname\": \"(Grid x 2) Two Rows\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"dwdw\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [0, 1, 1, 2]],\n            \"cols\": [0.0, 1.0],\n            \"nickname\": \"row1\",\n            \"rows\": [0.0, 0.0378979282466, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [1, 0, 2, 1]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"cols2\",\n            \"rows\": [0.0, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [0, 1, 1, 2], [0, 0, 1, 1], [1, 0, 2, 1]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"testing\",\n            \"rows\": [0.0, 1.0],\n        },\n        {\n            \"cells\": [[1, 0, 2, 1], [2, 0, 3, 2], [0, 0, 1, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.0233136462543, 0.976686353746, 1.0],\n            \"nickname\": \"wip\",\n            \"rows\": [0.0, 0.0395569620253, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 1, 2],\n                [0, 2, 1, 3],\n                [1, 0, 3, 1],\n                [1, 1, 2, 2],\n                [1, 2, 3, 3],\n                [3, 0, 4, 1],\n                [3, 1, 4, 2],\n                [3, 2, 4, 3],\n                [2, 1, 3, 2],\n            ],\n            \"cols\": [0.0, 0.0233136462543, 0.461765620143, 0.976686353746, 1.0],\n            \"nickname\": \"wip_2\",\n            \"rows\": [0.0, 0.0378979282466, 0.962102071753, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 1, 3],\n                [0, 3, 1, 4],\n                [1, 0, 4, 1],\n                [1, 1, 2, 2],\n                [1, 3, 4, 4],\n                [4, 0, 5, 1],\n                [4, 1, 5, 3],\n                [4, 3, 5, 4],\n                [1, 2, 3, 3],\n                [2, 1, 4, 2],\n                [3, 2, 4, 3],\n            ],\n            \"cols\": [0.0, 0.0233136462543, 0.5, 0.5, 0.976686353746, 1.0],\n            \"nickname\": \"wip_3\",\n            \"rows\": [0.0, 0.0378979282466, 0.423193532087, 0.962102071753, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 1, 3],\n                [0, 3, 1, 4],\n                [1, 0, 3, 1],\n                [1, 3, 3, 4],\n                [3, 0, 4, 1],\n                [3, 1, 4, 3],\n                [3, 3, 4, 4],\n                [1, 1, 2, 3],\n                [2, 1, 3, 2],\n                [2, 2, 3, 3],\n            ],\n            \"cols\": [0.0, 0.0233136462543, 0.377836493628, 0.976686353746, 1.0],\n            \"nickname\": \"wip_4\",\n            \"rows\": [0.0, 0.0378979282466, 0.469176351693, 0.962102071753, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 1, 3],\n                [0, 3, 1, 4],\n                [1, 0, 3, 1],\n                [1, 3, 3, 4],\n                [3, 0, 4, 1],\n                [3, 1, 4, 3],\n                [3, 3, 4, 4],\n                [1, 1, 2, 3],\n                [2, 1, 3, 2],\n                [2, 2, 3, 3],\n            ],\n            \"cols\": [0.0, 0.0233136462543, 0.524246192105, 0.829654958036, 1.0],\n            \"nickname\": \"wip_6\",\n            \"rows\": [0.0, 0.077817079333, 0.469176351693, 0.962102071753, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 2], [1, 0, 3, 1], [1, 1, 2, 2], [2, 1, 3, 2]],\n            \"cols\": [0.0, 0.607856481481, 0.82481712963, 1.0],\n            \"nickname\": \"shell_custom\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [1, 3, 2, 4],\n                [0, 1, 1, 2],\n                [0, 2, 1, 4],\n                [1, 0, 2, 3],\n            ],\n            \"cols\": [0.0, 0.327297216402, 1.0],\n            \"nickname\": \"29.05.2024_001\",\n            \"rows\": [0.0, 0.0311100049776, 0.648456943753, 0.231707317073, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [1, 4, 2, 5],\n                [0, 1, 1, 2],\n                [0, 2, 1, 5],\n                [1, 3, 2, 4],\n                [1, 0, 2, 3],\n            ],\n            \"cols\": [0.0, 0.327596527986, 1.0],\n            \"nickname\": \"29.05.2024_002\",\n            \"rows\": [\n                0.0,\n                0.0311100049776,\n                0.469124393371,\n                0.0321833161689,\n                0.50130770954,\n                1.0,\n            ],\n        },\n        {\n            \"cells\": [[1, 0, 2, 1], [2, 0, 3, 2], [0, 0, 1, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.3904296875, 0.7109375, 1.0],\n            \"nickname\": \"nss_laptop\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 2, 1], [0, 1, 2, 2], [0, 2, 1, 3], [1, 2, 2, 3]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"two_upper_rows\",\n            \"rows\": [0.0, 0.028, 0.056, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 2, 1],\n                [0, 1, 2, 2],\n                [0, 2, 1, 4],\n                [1, 2, 2, 3],\n                [1, 3, 2, 4],\n            ],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"two_upper_rows_2\",\n            \"rows\": [0.0, 0.028, 0.056, 0.0871100049776, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [0, 1, 2, 2], [1, 0, 2, 1]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"wip_26.10.2024\",\n            \"rows\": [0.0, 0.838169084542, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 4, 2],\n                [2, 0, 3, 1],\n                [1, 0, 2, 1],\n                [3, 0, 4, 1],\n                [0, 2, 4, 3],\n            ],\n            \"cols\": [0.0, 0.25, 0.5, 0.75, 1.0],\n            \"nickname\": \"rows_and_columns_1\",\n            \"rows\": [0.0, 0.47987012987, 0.75, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 4, 2],\n                [2, 0, 3, 1],\n                [1, 0, 2, 1],\n                [3, 0, 4, 1],\n                [0, 2, 4, 3],\n            ],\n            \"cols\": [0.0, 0.25, 0.5, 0.75, 1.0],\n            \"nickname\": \"rows_and_columns_b\",\n            \"rows\": [0.0, 0.47987012987, 0.871166415284, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"2025.01.26_large\",\n            \"rows\": [0.0, 0.0305118110236, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 1, 2],\n                [1, 0, 2, 1],\n                [1, 1, 2, 2],\n                [2, 0, 3, 1],\n                [2, 1, 3, 2],\n            ],\n            \"cols\": [\n                0.0,\n                0.*********333,\n                0.666666666667,\n                1.0,\n            ],\n            \"nickname\": \"ThreeColumnsWithHeaders\",\n            \"rows\": [\n                0.0,\n                0.21407480315,\n                1.0,\n            ],\n        },\n        {\n            \"cells\": [[0, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.5, 1.0],\n            \"nickname\": \"row_2cols\",\n            \"rows\": [0.0, 0.231880108992, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 2], [1, 0, 3, 1], [1, 1, 2, 2], [2, 1, 3, 2]],\n            \"cols\": [0.0, 0.5, 0.75, 1.0],\n            \"nickname\": \"llm_framework_langchain\",\n            \"rows\": [0.0, 0.0920275590551, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 2], [1, 0, 2, 1], [2, 0, 3, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.365446079904, 0.666666666667, 1.0],\n            \"nickname\": \"awesomelayout\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [1, 0, 2, 1], [0, 1, 1, 2], [1, 1, 2, 2]],\n            \"cols\": [0.0, 0.510856453559, 1.0],\n            \"nickname\": \"CrossQuadView\",\n            \"rows\": [0.0, 0.545275590551, 1.0],\n        },\n        {\n            \"cells\": [[0, 0, 1, 1], [0, 1, 1, 2], [0, 2, 1, 3], [0, 3, 1, 4]],\n            \"cols\": [0.0, 1.0],\n            \"nickname\": \"2025.06.03_a_vertical\",\n            \"rows\": [0.0, 0.0444293614882, 0.245218702866, 0.957516339869, 1.0],\n        },\n        {\n            \"cells\": [\n                [0, 0, 1, 1],\n                [0, 1, 1, 2],\n                [1, 0, 2, 1],\n                [1, 1, 2, 2],\n                [2, 0, 3, 1],\n                [2, 1, 3, 2],\n            ],\n            \"cols\": [0.0, 0.*********333, 0.666666666667, 1.0],\n            \"nickname\": \"2025.06.20_test\",\n            \"rows\": [0.0, 0.5, 1.0],\n        },\n    ],\n}\n", "settings": {"buffer_size": 15310, "line_ending": "Windows"}, "undo_stack": [[1, 1, "paste", null, "AQAAAAAAAAAAAAAAzjsAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [4, 1, "black", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABSOAAAAAAAAFI4AAAAAAAAAAAAAAAA8L8"]]}, {"contents": "", "redo_stack": [[1, 1, "insert", {"characters": "pla"}, "AwAAAAIAAAAAAAAAAgAAAAAAAAABAAAAYQEAAAAAAAAAAQAAAAAAAAABAAAAbAAAAAAAAAAAAAAAAAAAAAABAAAAcA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAADAAAAAAAAAAMAAAAAAAAAAAAAAAAA8L8"]], "settings": {"buffer_size": 0, "line_ending": "Windows"}}, {"contents": "", "settings": {"buffer_size": 0, "line_ending": "Windows"}}, {"contents": "\torganizerorganizer", "settings": {"buffer_size": 19, "line_ending": "Windows", "name": "organizerorganizer"}, "undo_stack": [[1, 1, "paste", null, "AQAAAAAAAAAAAAAACQAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [2, 1, "indent", null, "AQAAAAAAAAAAAAAAAQAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAJAAAAAAAAAAkAAAAAAAAAAAAAAAAA8L8"], [3, 1, "paste", null, "AQAAAAoAAAAAAAAAEwAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAKAAAAAAAAAAoAAAAAAAAAAAAAAAAA8L8"]]}, {"contents": "place all", "settings": {"buffer_size": 9, "line_ending": "Windows", "name": "place all"}, "undo_stack": [[1, 1, "insert", {"characters": "place"}, "BQAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAQAAAAAAAAACAAAAAAAAAAAAAAACAAAAAAAAAAMAAAAAAAAAAAAAAAMAAAAAAAAABAAAAAAAAAAAAAAABAAAAAAAAAAFAAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"], [2, 1, "insert", {"characters": " all"}, "BAAAAAUAAAAAAAAABgAAAAAAAAAAAAAABgAAAAAAAAAHAAAAAAAAAAAAAAAHAAAAAAAAAAgAAAAAAAAAAAAAAAgAAAAAAAAACQAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAFAAAAAAAAAAUAAAAAAAAAAAAAAAAA8L8"]]}, {"contents": "\n", "settings": {"buffer_size": 1, "line_ending": "Windows"}, "undo_stack": [[1, 1, "insert", {"characters": "\n"}, "AQAAAAAAAAAAAAAAAQAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8"]]}, {"file": "Packages/Jorn_AutoPlaceTabs/techstack.md", "settings": {"buffer_size": 1522, "encoding": "UTF-8", "line_ending": "Unix"}}, {"file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.md", "settings": {"buffer_size": 5387, "line_ending": "Unix"}, "undo_stack": [[4, 1, "revert", null, "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", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACYCgAAAAAAANgHAAAAAAAAAAAAAAAA8L8"]]}, {"file": "refs/all_sublime_packages/Origami/Origami.md", "settings": {"buffer_size": 73303, "encoding": "UTF-8", "line_ending": "Windows"}}, {"contents": "", "redo_stack": [[1, 1, "insert", {"characters": "sss"}, "AwAAAAIAAAAAAAAAAgAAAAAAAAABAAAAcwEAAAAAAAAAAQAAAAAAAAABAAAAcwAAAAAAAAAAAAAAAAAAAAABAAAAcw", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAADAAAAAAAAAAMAAAAAAAAAAAAAAAAA8L8"]], "settings": {"buffer_size": 0, "line_ending": "Windows"}}], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["place", "<PERSON><PERSON>lace: Place All Tabs"], ["place ", "<PERSON><PERSON>lace: Place All Tabs"], ["", "AlignTab: Table Mode"], ["place all", "<PERSON><PERSON>lace: Place All Tabs"], ["org", "Jorn: Organize Views by Directory"], ["previ", "Markdown Preview: Preview in Browser"], ["min", "Jorn - Minify Text"], ["unm", "Jorn - Unminify Text"], ["tab", "Table Editor: Enable for current view"], ["MIN", "Jorn - Minify Text"], ["previe", "Markdown Preview: Preview in Browser"], ["prev", "Permute Lines: Reverse"]], "width": 0.0}, "console": {"height": 290.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/.vscode", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/Packages", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/Packages/Jorn_AutoPlaceTabs", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/Jorn_AppInterface", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/Jorn_ColumnWidth", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/Jorn_OrganizeViewsByDirectory", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/Jorn_SublimeTabOrganizer", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/Jorn_TabUtils", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/Jorn_TimeIntervalActions", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/User"], "file_history": ["/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/User/Origami.sublime-settings", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/all_sublime_packages/Layout/Layout.py", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/all_sublime_packages/Origami/Origami.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/Jorn_LayoutTools/Jorn_LayoutTools.py", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/Jorn_AppInterface/notes/01_Jorn_AppInterface_Project_Overview.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/refs/my_sublime_packages/my_sublime_packages.dirtree.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/.vscode/Augment-Memories.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/Packages/Jorn_AutoPlaceTabs/.python-version", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.07.22-kl.12.46--my_st_packages/my_sublime/my_sublime.sublime-project", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/src/main.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.30-kl.11.44.32--sequence-3034--gpt-4.1.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3042-context--intent_amplifier.5.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/project_files/py.codeguidelines.jinja-md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0001--hybrid/src/playground/litellm_list_all_models.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/md/8980-a-runway_prompt_generator.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/ai_systems.0011.sublime-workspace", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/techstack.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/debug_system_instruction.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/ai_systems.0011.sublime-project", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai.systems.0012--runway/.vscode/Augment-Memories.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/working_systems/working_systems.dirtree.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9298-f-quality_compliance_final_formatter.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9298-d-compose_80_tegn_keyword_optimizer.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9298-a-company_reference_injection.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9299-a-service_reference.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9299-b-parse_and_map.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9300-d-engagement_enhancer.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9300-a-seo_localizer.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/md/3034-b-service_taxonomy_integration.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_SublimeTabOrganizer/tab_organization.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3034-context--ringerikelandskap_seo.x.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3031-expanders--problem_exploder.1.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3033-translator--norwegian_english_translator.1.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3032-translator--english_norwegian_translator.1.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3035-context--seo_convergence.x.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/lvl1.md.templates.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/lvl1_md_to_json.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-001-b.kl09__len[37].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-001-a.kl09__len[1254].py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-002-b.kl09__len[39].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-002-a.kl09__len[70].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-003-d.kl11__len[83].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-003-c.kl11__len[26].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-003-b.kl11__len[40].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_TabUtils/unsaved_tabs_backup/my_ai_systems/2025.06.30-dsk-003-a.kl11__len[38].md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3002-compressors.good--title_extractor.4.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3030-compressors.great--singular_value.1.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.history.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3020-enhancers--wip.x.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/md/3005-a-instruction_converter.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0007--wip/src/templates/stage2/md/2002-a-norwegian_translator.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage2/generators/2000-2099.todo.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/md/3020-a-problem_exploder.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0001--hybrid/src/templates/lvl1/md/0041-a-singular_value_maximizer.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.30-kl.09.45.02--sequence-3003+3100-a-c+3022+3005--gpt-4.1.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Chrome/wip_chrome_ext 1.md", "/C/Users/<USER>/AppData/Local/Temp/tmpBAAC.tmp.tsx", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.06.30-kl.08.27--rl-website-v2-006/docs/.new_hashes.py", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.06.30-kl.08.27--rl-website-v2-006/docs/complete-architecture-analysis.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/sub-prompts-notes.py", "/C/Users/<USER>/AppData/Local/Temp/tmpC05B.tmp.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.28-kl.12.16.35--sequence-2701-a+3017+3016+3003-a+3001-a+3003-b+3001-a+3003+3017+3016--gpt-4.1.good.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3004-compressors.good--form_classifier.4.history.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9200-c-framework_mapper.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3011-rephrasers.good--precision_refiner.6.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage3/generators/3015-transformers.--prompt_generalizer.5.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9200-d-generalization_amplifier.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0010--consolidated/src/lvl1/templates/md/9200-e-template_synthesizer.md", "/C/Users/<USER>/Desktop/__SCRATCH__/2025.06.28-kl.10.28--/lvl1_sequence_executor.py", "/?NAS/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.sync-conflict-20250628-101836-IOCXSFF.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.sync-conflict-20250622-211334-IEE7KQY.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/lvl1_sequence_executor.sync-conflict-20250622-210857-IEE7KQY.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.27-kl.16.45.31--sequence-3001-a+3003-a+3001-a+3003-b+3001-a+3003-c-d+3100-a-c+3001-a+3000+3100-a-c--gpt-4.1.GREAT.GREAT.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_LOG.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Projects/prj_ringerikelandskap/firma/arbeidskontrakt/arbeidskontrakt_ringerike_landskap.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/output/history--2025.06.22-kl.20.22.09--sequence-sequence-3001--gpt-4.1.json", "/C/Users/<USER>/Desktop/my/flow/android/prj/helserelatert/mcp/Full Process for Coding with AI Coding Assistants - Agent Edition.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_SCRATCHPAD.md", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Cli_Utils/py__ProjectGenerator/src/templates/prompts/unsorted_gpt_prompts/STICKY_NOTES.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0008--wip/src/templates/stage1/generators/1500-1599.innovators.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0004--hybrid-instructions/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/JORN_ENVIRONMENT_VARIABLES.bat", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0007--wip/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0005--hybrid/src/lvl1_sequence_executor.py", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Python/Py_Ai_Utils/my_ai_systems/ai_systems.0007--wip/src/output/history--2025.06.15-kl.01.00.03--sequence-sequence-1100+1300+1204+1100+1300+1204+1100--gpt-4.1.good.json", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Batch/todo/choco-install-my.bat", "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Scripts/Batch/Cmd - Personal - Preferences/bat__SetCustomPrefs_Win11.bat"], "find": {"height": 29.*********3}, "find_in_files": {"height": 184.0, "where_history": []}, "find_state": {"case_sensitive": false, "find_history": [" // "], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.py", "semi_transient": false, "settings": {"buffer_size": 32776, "regions": {}, "selection": [[443, 443]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753199066.79, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 4362, "tab_activation_time": 1753199066, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 5, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-keymap", "semi_transient": false, "settings": {"buffer_size": 576, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753198955.0, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 4245, "tab_activation_time": 1753198955, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 415.*********, "zoom_level": 1.0}, "stack_index": 6, "stack_multiselect": false, "type": "text"}, {"buffer": 2, "file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-commands", "semi_transient": false, "settings": {"buffer_size": 867, "regions": {}, "selection": [[17, 17]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753199068.15, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 4358, "tab_activation_time": 1753199068, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 4, "stack_multiselect": false, "type": "text"}, {"buffer": 3, "file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-settings", "semi_transient": false, "settings": {"buffer_size": 1930, "regions": {}, "selection": [[919, 710]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753206057.68, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 11346, "tab_activation_time": 1753206057, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 3, "stack_multiselect": false, "type": "text"}, {"buffer": 4, "file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.sublime-project", "semi_transient": true, "settings": {"buffer_size": 537, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753206062.58, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 0, "tab_activation_time": 1753206062, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 2, "stack_multiselect": false, "type": "text"}, {"buffer": 5, "file": "refs/my_sublime_packages/User/Plain text.sublime-settings", "semi_transient": false, "settings": {"buffer_size": 33, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753206243.13, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 9809, "tab_activation_time": 1753206243, "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 6, "file": "/C/Users/<USER>/Desktop/my/flow/home/<USER>/Apps/app_sublimetext/exe/Data/Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.py", "semi_transient": false, "settings": {"buffer_size": 32776, "regions": {}, "selection": [[73, 73]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753198947.56, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 2085, "tab_activation_time": 1753198947, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 7, "stack_multiselect": false, "type": "text"}, {"buffer": 7, "file": "my_sublime.sublime-project", "selected": true, "semi_transient": false, "settings": {"buffer_size": 823, "regions": {}, "selection": [[116, 116]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "color_helper.scan": {"allow_scanning": false, "color_class": [{"class": "css-level-4", "scopes": ""}], "color_trigger": "(?xi)\n(?:\n    \\b(?<![-#&$])(?:\n        color\\((?!\\s*-)|(?:hsla?|(?:ok)?(?:lch|lab)|hwb|rgba?)\\(\n) |\n\\b(?<![-#&$])[\\w]{3,}(?![(-])\\b|(?<![&])\\#)\n", "current_ext": ".sublime-project", "current_syntax": "JSON/JSON", "enabled": true, "last_updated": 1753206377.36, "scanning": "-comment"}, "sorttabs_lastactivated": 1753208739.73, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 13658, "tab_activation_time": 1753208739, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}, {"buffer": 8, "file": "refs/my_sublime_packages/Jorn_SublimeTabOrganizer/Jorn_SublimeTabOrganizer.sublime-project", "semi_transient": false, "settings": {"buffer_size": 441, "regions": {}, "selection": [[147, 147]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "sorttabs_lastactivated": 1753197794.87, "syntax": "Packages/JSON/JSON.sublime-syntax", "tab_activation_duration": 18, "tab_activation_time": 1753197794, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 8, "stack_multiselect": false, "type": "text"}, {"buffer": 9, "semi_transient": false, "settings": {"buffer_size": 0, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime", "sorttabs_lastactivated": 1753199014.27, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 2450, "tab_activation_time": 1753199014}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 9, "stack_multiselect": false, "type": "text"}, {"buffer": 10, "semi_transient": false, "settings": {"buffer_size": 0, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "auto_name": "", "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime", "sorttabs_lastactivated": 1753199014.27, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 1946, "tab_activation_time": 1753199014}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 10, "stack_multiselect": false, "type": "text"}, {"buffer": 11, "semi_transient": false, "settings": {"buffer_size": 0, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime\\refs\\my_sublime_packages\\User", "sorttabs_lastactivated": 1753199014.28, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 1861, "tab_activation_time": 1753199014, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 11, "stack_multiselect": false, "type": "text"}, {"buffer": 12, "semi_transient": false, "settings": {"buffer_size": 15310, "regions": {}, "selection": [[1625, 24]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime\\refs\\my_sublime_packages\\User", "sorttabs_lastactivated": 1753199014.29, "syntax": "Packages/Python/Python.sublime-syntax", "tab_activation_duration": 1880, "tab_activation_time": 1753199014, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 12, "stack_multiselect": false, "type": "text"}, {"buffer": 13, "semi_transient": false, "settings": {"buffer_size": 0, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "auto_name": "", "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime\\Packages\\Jorn_AutoPlaceTabs", "sorttabs_lastactivated": 1753199014.3, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 667, "tab_activation_time": 1753199014}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 13, "stack_multiselect": false, "type": "text"}, {"buffer": 14, "semi_transient": false, "settings": {"buffer_size": 0, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime", "sorttabs_lastactivated": 1753199014.31, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 2577, "tab_activation_time": 1753199014}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 14, "stack_multiselect": false, "type": "text"}, {"buffer": 15, "semi_transient": false, "settings": {"buffer_size": 19, "regions": {}, "selection": [[19, 19]], "settings": {"apply_syntax_touched": true, "auto_name": "organizerorganizer", "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime", "sorttabs_lastactivated": 1753199014.32, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 1243, "tab_activation_time": 1753199014, "tab_size": 4, "translate_tabs_to_spaces": false}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 15, "stack_multiselect": false, "type": "text"}, {"buffer": 16, "semi_transient": false, "settings": {"buffer_size": 9, "regions": {}, "selection": [[9, 9]], "settings": {"apply_syntax_touched": true, "auto_name": "place all", "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime", "sorttabs_lastactivated": 1753199014.33, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 34, "tab_activation_time": 1753199014}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 16, "stack_multiselect": false, "type": "text"}, {"buffer": 17, "semi_transient": false, "settings": {"buffer_size": 1, "regions": {}, "selection": [[1, 1]], "settings": {"apply_syntax_touched": true, "auto_name": "", "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime", "sorttabs_lastactivated": 1753199009.53, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 0, "tab_activation_time": 1753199009}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 17, "stack_multiselect": false, "type": "text"}, {"buffer": 18, "file": "Packages/Jorn_AutoPlaceTabs/techstack.md", "semi_transient": false, "settings": {"buffer_size": 1522, "regions": {}, "selection": [[177, 177]], "settings": {"apply_syntax_touched": true, "color_helper.box_height": 18, "color_helper.color_scheme": "<PERSON><PERSON>_<PERSON>_Dark_Focused.sublime-color-scheme", "color_helper.refresh": true, "sorttabs_lastactivated": 1753199014.35, "syntax": "Packages/Markdown/Markdown.sublime-syntax", "tab_activation_duration": 4303, "tab_activation_time": 1753199014}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 18, "stack_multiselect": false, "type": "text"}, {"buffer": 19, "file": "Packages/Jorn_AutoPlaceTabs/Jorn_AutoPlaceTabs.md", "semi_transient": false, "settings": {"buffer_size": 5387, "regions": {}, "selection": [[1822, 1822]], "settings": {"apply_syntax_touched": true, "color_helper.box_height": 18, "color_helper.color_scheme": "<PERSON><PERSON>_<PERSON>_Dark_Focused.sublime-color-scheme", "color_helper.refresh": true, "sorttabs_lastactivated": 1753199066.1, "syntax": "Packages/Markdown/Markdown.sublime-syntax", "tab_activation_duration": 2742, "tab_activation_time": 1753199066, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 918.0, "zoom_level": 1.0}, "stack_index": 19, "stack_multiselect": false, "type": "text"}, {"buffer": 20, "file": "refs/all_sublime_packages/Origami/Origami.md", "semi_transient": false, "settings": {"buffer_size": 73303, "regions": {}, "selection": [[3903, 3903]], "settings": {"apply_syntax_touched": true, "color_helper.box_height": 18, "color_helper.color_scheme": "<PERSON><PERSON>_<PERSON>_Dark_Focused.sublime-color-scheme", "color_helper.refresh": true, "sorttabs_lastactivated": 1753199014.39, "syntax": "Packages/Markdown/Markdown.sublime-syntax", "tab_activation_duration": 3317, "tab_activation_time": 1753199014, "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 4248.0, "zoom_level": 1.0}, "stack_index": 20, "stack_multiselect": false, "type": "text"}, {"buffer": 21, "semi_transient": false, "settings": {"buffer_size": 0, "regions": {}, "selection": [[0, 0]], "settings": {"apply_syntax_touched": true, "auto_name": "", "color_helper.refresh": true, "default_dir": "C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.07.22-kl.12.46--my_st_packages\\my_sublime", "sorttabs_lastactivated": 1753206281.09, "syntax": "Packages/Text/Plain text.tmLanguage", "tab_activation_duration": 9719, "tab_activation_time": 1753206281}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 21, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 29.*********3}, "input": {"height": 64.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.find_results": {"height": 0.0}, "output.project_environment_log": {"height": 0.0}, "pinned_build_system": "", "project": "my_sublime.sublime-project", "replace": {"height": 54.6666666667}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["origam", "refs\\all_sublime_packages\\Origami\\Origami.md"], ["layout", "refs\\all_sublime_packages\\Layout\\Layout.py"], ["orig", "refs\\all_sublime_packages\\Origami\\Origami.md"], ["my_st_layouttools", "refs\\my_sublime_packages\\Jorn_LayoutTools\\Jorn_LayoutTools.py"], ["01_Jorn_AppInterface_Project_Overview", "refs\\my_sublime_packages\\Jorn_AppInterface\\notes\\01_Jorn_AppInterface_Project_Overview.md"], ["8980", "ai_systems.0008--wip\\src\\templates\\stage3\\md\\8980-a-runway_prompt_generator.md"], ["litellm_list_all_models.py", "ai_systems.0001--hybrid\\src\\playground\\litellm_list_all_models.py"], ["litellm_list_all_models", "ai_systems.0001--hybrid\\src\\playground\\litellm_list_all_models.py"], ["929", "ai_systems.0010--consolidated\\src\\lvl1\\templates\\md\\9299-a-service_reference.md"], ["transl", "ai_systems.0007--wip\\src\\templates\\stage2\\md\\2002-a-norwegian_translator.md"], ["explo", "ai_systems.0008--wip\\src\\templates\\stage3\\md\\3020-a-problem_exploder.md"], ["value", "ai_systems.0001--hybrid\\src\\templates\\lvl1\\md\\0041-a-singular_value_maximizer.md"], ["9300", "ai_systems.0010--consolidated\\src\\lvl1\\templates\\md\\9300-a-seo_localizer.md"]], "width": 0.0}, "select_project": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": false, "show_open_files": false, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 424.*********, "status_bar_visible": true, "template_settings": {}}